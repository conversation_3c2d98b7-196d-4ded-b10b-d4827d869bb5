import request from '@/utils/request'

// 查询场景列表
export const sceneListApi = (params: any) => request.get(`/scene/sceneList`, params)

// 删除场景信息
export const removeSceneApi = (params: any) => request.get(`/scene/removeScene`, params)

// 时序类型
export const queryFileTypeApi = () => request.get(`/scene/queryFileType`)

// 时间分类
export const queryDateFormatApi = () => request.get(`/scene/queryDateFormat`)

// 保存场景信息
export const addSceneInfoApi = (params: any) => request.post(`/scene/addSceneInfo`, params)

// 查询场景详情
export const querySceneDetailApi = (params: any) => request.get(`/scene/querySceneDetail`, params)

// 上传场景视频
export const uploadSceneVideoApi = (params: any) => request.post(`/scene/uploadSceneVideo`, params)
