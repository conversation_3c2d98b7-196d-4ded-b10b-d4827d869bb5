@primaryColor: #598bfd;
@bgColor: #f6f6f6;
@withe: #ffffff;

:root {
  --el-border-radius-base: 3px;
}

.el-button.is-link:focus {
  color: var(--el-color-primary);
}

// 多行隐藏
.setEllipsis(@line) {
  display: -webkit-box; /* 使用弹性盒子模型 */
  -webkit-box-orient: vertical; /* 垂直排列子元素 */
  -webkit-line-clamp: @line; /* 控制显示的行数 */
  overflow: hidden; /* 超出部分隐藏 */
  text-overflow: ellipsis; /* 显示省略号 */
}

.ellipseLine() {
  white-space: nowrap; /* 防止文字换行 */
  overflow: hidden; /* 超出部分隐藏 */
  text-overflow: ellipsis; /* 显示省略号 */
}

//去掉滚动条
.no-scrollbar(@width) {
  // 修改滚动条下面的宽度
  &::-webkit-scrollbar {
    width: @width;
  }
  // 修改滚动条的下面的样式
  &::-webkit-scrollbar-track {
    background-color: #ebeef5;
    -webkit-border-radius: 2em;
    -moz-border-radius: 2em;
    border-radius: 2em;
  }
  // 修改滑块
  &::-webkit-scrollbar-thumb {
    background-color: #909399;
    -webkit-border-radius: 2em;
    -moz-border-radius: 2em;
    border-radius: 2em;
  }
}
