<template>
  <transition name="fade">
    <div v-if="showViewer" class="common-preview-wrapper">
      <div class="tip-wrapper">
        <div></div>
        <p>{{ getFileNameComplete(rowData) }}</p>
        <div class="tip-right">
          <SvgIcon name="closeBold" @click="handleClose" class="close-icon" />
        </div>
      </div>
      <div class="map-wrapper">
        <div class="olMap">
          <iframe :src="vectorUrl" height="100%" width="100%" frameborder="0"></iframe>
        </div>
      </div>
    </div>
  </transition>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { getVectorDrawUrlApi } from '@/api/data_catalog'

import { ElMessage } from 'element-plus'
import { getFileNameComplete } from '@/utils/fileUtils'
import useUserInfo from '@/store/useUserInfo'

const userInfo = useUserInfo()

const showViewer = ref<boolean>(false)
const handleClose = () => {
  showViewer.value = false
}

const rowData = ref<any>({})
const vectorUrl = ref<any>()
const handleOpenPreview = async (row: any) => {
  try {
    const { data, message, status } = await getVectorDrawUrlApi()
    if ([200].includes(status)) {
      rowData.value = row
      showViewer.value = true
      vectorUrl.value = `${data}?vectorFileId=${row.tid}&token=${userInfo.token}`
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}

defineExpose({ handleOpenPreview })
</script>

<style scoped lang="less">
.common-preview-wrapper {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  .tip-wrapper {
    background: rgba(0, 0, 0, 0.9);
    padding: 0 48px;
    width: 100%;
    height: 48px;
    line-height: 48px;
    color: @withe;
    font-size: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-sizing: border-box;
    .tip-right {
      display: flex;
      height: 100%;
      align-items: center;
      .close-icon {
        cursor: pointer;
      }
    }
  }
  .map-wrapper {
    flex: 1;
    box-sizing: border-box;
    // padding: 30px 40px;
    .olMap {
      height: 100%;
      width: 100%;
      box-sizing: border-box;
      background-color: rgba(0, 0, 0, 0.9);
    }
  }
}
</style>
