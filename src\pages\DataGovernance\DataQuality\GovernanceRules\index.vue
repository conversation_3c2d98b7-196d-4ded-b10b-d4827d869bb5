<template>
  <MainLayout>
    <template #header>
      <SearchTool
        @handle-search="
          (form: any) => {
            pageData.handleSearch(form, 1)
          }
        "
      />
    </template>
    <el-table
      v-loading="pageData.loading"
      header-cell-class-name="common-table-header"
      cell-class-name="common-table-cell"
      ref="multipleTableRef"
      :data="tableData"
      height="100%"
      style="width: 100%"
      row-key="tid"
    >
      <el-table-column property="autoRuleName" label="规则名称" show-overflow-tooltip />
      <el-table-column property="ruleName" label="质量规则" show-overflow-tooltip />
      <el-table-column property="fileTypeName" label="适用数据" show-overflow-tooltip />
      <el-table-column property="autoRuleMethod" label="治理规则" show-overflow-tooltip />
      <el-table-column property="autoRuleContent" label="规则说明" show-overflow-tooltip />
      <el-table-column
        v-if="permission.hasButton(['governanceRules_details'])"
        label="操作"
        width="180"
        fixed="right"
      >
        <template #default="scope">
          <el-button
            @click="EditDetailDialogRef.handleOpen(scope.row)"
            title="详情"
            class="common-icon-btn"
            type="primary"
            text
            link
          >
            <template #icon>
              <SvgIcon name="details" />
            </template>
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <template #footer>
      <el-pagination
        v-model:currentPage="pageData.page"
        v-model:page-size="pageData.limit"
        :page-sizes="pageData.pageSizes"
        background
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageData.total"
        @size-change="pageData.handleSizeChange"
        @current-change="pageData.handleCurrentChange"
      />
    </template>
    <EditDetailDialog ref="EditDetailDialogRef" />
  </MainLayout>
</template>

<script setup lang="ts">
import MainLayout from '@/components/MainLayout/index.vue'
import SearchTool from './SearchTool.vue'
import usePageData from '@/hooks/usePageData'
import { qaAutoRuleListApi } from '@/api/data_governance'
import { ref } from 'vue'
import EditDetailDialog from './EditDetailDialog.vue'
import useUserInfo from '@/store/useUserInfo'

const permission = useUserInfo()

const EditDetailDialogRef = ref<any>()
const { tableData, pageData } = usePageData(qaAutoRuleListApi)
</script>

<style scoped></style>
