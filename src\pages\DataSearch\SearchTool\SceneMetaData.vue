<template>
  <el-dialog
    v-model="dialogFormVisible"
    top="10vh"
    title="元数据"
    width="540px"
    :close-on-click-modal="false"
    @close="handleDialogClose"
  >
    <el-form ref="formRef" :model="formData" label-width="auto">
      <el-row :gutter="10">
        <el-col :span="24">
          <el-form-item label="场景名称" prop="name">
            <span>{{ formData.name }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="时序类型" prop="fileTypeName">
            <span>{{ formData.fileTypeName }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="播放时间" prop="interval">
            <span>{{ formData.interval }}秒</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="时间分类" prop="dateFormat">
            <span>{{ formData.dateFormat }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="描述" prop="notes">
            <span>{{ formData.notes }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="文件" prop="fileList">
            <el-table
              header-cell-class-name="common-table-header"
              cell-class-name="common-table-cell"
              ref="multipleTableRef"
              :data="formData.fileList"
              height="300px"
            >
              <el-table-column property="sceneTimeFormat" label="时间" />
              <el-table-column property="fileName" label="文件" show-overflow-tooltip />
            </el-table>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="setDialogFormVisible(false)">取消</el-button>
        <el-button type="primary" @click="setDialogFormVisible(false)">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import useDialogForm from '@/hooks/useDialogForm'
import { ref } from 'vue'
import { querySceneDetailApi } from '@/api/sequential_scene'
import { ElMessage } from 'element-plus'

const formData = ref<any>({})
const { dialogFormVisible, handleDialogClose, formRef, setDialogFormVisible } = useDialogForm()

const handleOpen = async ({ tid }: any) => {
  try {
    const { data, status, message } = await querySceneDetailApi({ tid })
    if ([200].includes(status)) {
      formData.value = data
      formData.value.fileList = formData.value.fileList.map((item: any) => ({
        fileName: item.fileName,
        sceneTimeFormat: item.sceneTimeFormat,
        tid: item.userFileId,
        gisPreviewVO: item.detail.gisPreviewVO,
        metadataVO: item.detail.metadataVO,
      }))
      setDialogFormVisible(true)
    } else {
      ElMessage.error(message || '获取场景信息失败!')
    }
  } catch (error) {}
}

defineExpose({ handleOpen })
</script>

<style lang="less" scoped></style>
