import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios'

import { ElMessage } from 'element-plus'
import NProgress from 'nprogress'
import useUserInfo from '@/store/useUserInfo'
import { ssoLoginUrl } from '@/api/login'

// 数据返回的接口
// 定义请求响应参数，不含data
interface Result {
  status: number
  message: any
  total: number
}

// 请求响应参数，包含data
interface ResultData<T = any> extends Result {
  data?: T
}

enum ReQuestEnums {
  TIMEOUT = 50000, // 请求等待响应时间
  OVERDUE = 1103, // 登录失效
  SUCCESS = 10000, // 请求成功
}

export const baseUrl: string = import.meta.env.VITE_BASE_URL

// 网络请求地址
const config = {
  // 默认地址
  baseURL: baseUrl,
  // 设置超时时间
  timeout: ReQuestEnums.TIMEOUT as number,
  // 跨域时候允许携带凭证
  withCredentials: true,
}

class Request {
  // 定义成员变量并指定类型
  private service: AxiosInstance

  public constructor(config: AxiosRequestConfig) {
    this.service = axios.create(config)
    /**
     * 请求拦截器
     * 客户端发送请求 -> [请求拦截器] -> 服务器
     * token校验(JWT) : 接受服务器返回的token,存储到vuex/pinia/本地储存当中
     */
    this.service.interceptors.request.use(
      (config: any) => {
        NProgress.start()
        const permission = useUserInfo()
        config.headers.token = permission.token
        config.headers.appCode = permission.appCode
        return config
      },
      (error: AxiosError) => {
        NProgress.done()
        return Promise.reject(error)
      },
    )
    /**
     * 响应拦截器
     * 服务器换返回信息 -> [拦截统一处理] -> 客户端JS获取到信息
     */
    this.service.interceptors.response.use(
      (response: AxiosResponse) => {
        const { data, status, request } = response

        NProgress.done()
        // 当是下载接口的时候返回所有接口信息
        if (request.responseType === 'blob') {
          return Promise.resolve(response)
        }
        if ([200].includes(status)) {
          if ([401].includes(data.status)) {
            console.log(ssoLoginUrl(), 'ssss')
            window.location.href = ssoLoginUrl()
            return Promise.reject(response.data)
          }

          if ([3003].includes(data.status)) {
            window.location.href = `${window.location.origin}/dataRecovery`
            return Promise.reject(response.data)
          }
          return Promise.resolve(data)
        }
        return Promise.reject(response.data)
      },
      (error: AxiosError) => {
        NProgress.done()
        if (error.response?.status) {
          const { response } = error
          const data = response.data as any
          if (['/datasafe/recoveryFinish'].includes(response.config.url as any)) {
            return Promise.reject(error)
          }
          switch (error.response.status) {
            case 404:
              ElMessage.warning('接口不存在，请刷新重试或联系管理员')
              return Promise.reject(error.response)
            case 500:
              ElMessage.error('服务异常，请稍后刷新重试或联系管理员')
              return Promise.reject(error.response)
            case 502:
              ElMessage.error('服务异常，请稍后刷新重试或联系管理员')
              return Promise.reject(error.response)
            default:
              ElMessage.error(data?.message || '服务异常，请稍后刷新重试或联系管理员')
              return Promise.reject(error.response)
          }
        }
        return Promise.reject(error)
      },
    )
  }

  // 常用方法封装get请求
  get(url: string, params?: object, controller?: AbortController): Promise<ResultData<any>> {
    return this.service.get(url, {
      params,
      // 取消相同请求的上一次请求
      signal: controller?.signal,
    })
  }

  // 常用方法封装post请求
  post(url: string, data?: object, controller?: AbortController): Promise<ResultData<any>> {
    return this.service.post(url, data, {
      // 取消相同请求的上一次请求
      signal: controller?.signal,
    })
  }

  // 删除接口调用
  delete(url: string, params?: object): Promise<ResultData<any>> {
    return this.service.delete(url, { params })
  }

  // 下载文件
  downLoad(url: string, data?: object): Promise<ResultData<any>> {
    return this.service({
      url,
      method: 'get',
      data,
      responseType: 'blob',
    })
  }
}

export default new Request(config)
