<template>
  <div class="qualityMonitor">
    <div class="top">
      <div class="square">
        <div class="fileData">
          <div v-for="(p, i) in fileData" :key="i" :style="{ backgroundImage: `url(${p.img})` }">
            <p class="title">{{ p.name }}</p>
            <p class="num">{{ p.num }}</p>
          </div>
        </div>
      </div>
      <div class="square">
        <span class="text">数据总体通过率</span>
        <Echarts v-if="passRateOptions" :options="passRateOptions" />
      </div>
      <div class="rectangle">
        <span class="text">历史趋势</span>
        <Echarts v-if="historyOptions" :options="historyOptions" />
      </div>
    </div>
    <div class="bottom">
      <div class="square">
        <span class="text">质量平均得分</span>
        <Echarts v-if="qualityScore" :options="qualityScore" />
      </div>
      <div class="rectangle">
        <span class="text">质量平均得分趋势</span>
        <Echarts v-if="qualitySubTrendOptions" :options="qualitySubTrendOptions" />
      </div>
      <div class="square">
        <span class="text">质量得分分布</span>
        <Echarts v-if="pieOptions" :options="pieOptions" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import administer from './administer.webp'
import qualified from './qualified.webp'
import issuesPaper from './issuesPaper.webp'
import Echarts from '@/components/Echarts/index.vue'
import { ref } from 'vue'
import { EChartsOption } from 'echarts'
import * as echarts from 'echarts'
import { queryResultApi } from '@/api/data_governance'
import { ElMessage } from 'element-plus'

const getAllData = async () => {
  try {
    const { data, status, message } = await queryResultApi()
    if ([200].includes(status)) {
      const {
        countTotal,
        countPass,
        countFail,
        passRate,
        avgScore,
        qaTotalList,
        qaPassList,
        qaFailList,
        avgScoreList,
        countExcellent,
        countGood,
        countMedium,
      } = data
      fileData.value = filterFileData(countTotal, countPass, countFail)
      passRateOptions.value = getPassRateOptions(passRate)
      qualityScore.value = getQualityScore(avgScore)
      historyOptions.value = getHistoryOptions(qaTotalList, qaPassList, qaFailList)
      qualitySubTrendOptions.value = getQualitySubTrendOptions(avgScoreList)
      pieOptions.value = getPieOptions(countExcellent, countGood, countMedium, countFail)
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}
getAllData()

//文件数统计
const fileData = ref<any[]>([])
const filterFileData = (countTotal: number, countPass: number, countFail: number) => {
  return [
    {
      name: '治理文件数',
      num: countTotal || 0,
      img: administer,
    },
    {
      name: '合格文件数',
      num: countPass || 0,
      img: qualified,
    },
    {
      name: '问题文件数',
      num: countFail || 0,
      img: issuesPaper,
    },
  ]
}

//数据总体通过率
const passRateOptions = ref<any>()
const getPassRateOptions = (passRate: number) => {
  return {
    series: [
      {
        name: 'Pressure',
        type: 'gauge',
        progress: {
          show: true,
        },
        axisTick: {
          distance: 0,
          length: 5,
          lineStyle: {
            width: 1,
          },
        },
        splitLine: {
          distance: 0,
          length: 10,
          lineStyle: {
            width: 2,
          },
        },
        axisLabel: {
          fontSize: 8,
        },
        detail: {
          valueAnimation: true,
          formatter: '{value}%',
          fontSize: 20,
        },
        data: [
          {
            value: passRate,
          },
        ],
      },
    ],
  }
}

// 质量平均得分
const qualityScore = ref<any>()
const getQualityScore = (avgScore: number) => {
  return {
    series: [
      {
        type: 'gauge',
        axisLine: {
          lineStyle: {
            width: 10,
            color: [
              [0.25, '#BCEBEE'],
              [0.5, '#92E0E4'],
              [0.75, '#92E0E4'],
              [1, '#26C1C9'],
            ],
          },
        },
        axisTick: {
          distance: 0,
          length: 5,
          lineStyle: {
            width: 1,
            color: '#8F99A3',
          },
        },
        splitLine: {
          distance: 0,
          length: 10,
          lineStyle: {
            width: 2,
            color: '#D0DCE5',
          },
        },
        axisLabel: {
          color: 'inherit',
          fontSize: 8,
        },
        pointer: {
          itemStyle: {
            color: 'auto',
          },
        },
        title: {
          show: false,
        },
        detail: {
          valueAnimation: true,
          formatter: '{value}',
          color: 'inherit',
          fontSize: 20,
        },
        data: [
          {
            value: avgScore,
          },
        ],
      },
    ],
  }
}

// 质量得分分布
const pieOptions = ref<any>()
const getPieOptions = (
  countExcellent: number,
  countGood: number,
  countMedium: number,
  countFail: number,
) => {
  return {
    tooltip: {
      trigger: 'item',
    },
    legend: {
      bottom: '2%',
      left: 'center',
    },
    series: [
      {
        name: '质量得分分布',
        type: 'pie',
        radius: ['40%', '60%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center',
        },
        labelLine: {
          show: false,
        },
        data: [
          { value: countExcellent, name: '优秀' },
          { value: countGood, name: '良好' },
          { value: countMedium, name: '一般' },
          { value: countFail, name: '不合格' },
        ],
      },
    ],
  }
}

// 历史趋势
const historyOptions = ref<EChartsOption | any>()
const getHistoryOptions = (qaTotalList: any[], qaPassList: any[], qaFailList: any[]) => {
  return {
    animationDuration: 5000,
    title: {
      show: false,
    },
    legend: {
      show: false,
    },
    grid: {
      top: '20%',
      left: 20,
      right: 20,
      bottom: '10%',
      containLabel: true,
    },
    tooltip: {
      trigger: 'axis',
    },
    xAxis: {
      type: 'category',
      axisLine: {
        show: true,
        lineStyle: {
          color: '#CCCCCC',
        },
      },
      splitLine: {
        show: false,
      },
      axisLabel: {
        fontFamily: 'MicrosoftYaHei',
        fontSize: 12,
        color: '#333333',
      },
      axisTick: {
        show: true,
        alignWithLabel: true,
        inside: true,
      },
      boundaryGap: true,
      data: qaTotalList.map((item: any) => item.name),
    },
    yAxis: {
      type: 'value',
      min: 0,
      axisLine: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: '#CCCCCC',
          type: 'dashed',
        },
      },
      axisLabel: {
        show: true,
        margin: 20,
      },
      axisTick: {
        show: false,
      },
    },
    series: [
      {
        name: '治理文件数',
        type: 'line',
        showSymbol: false,
        showAllSymbol: true,
        symbol: 'circle',
        symbolSize: 12,
        smooth: true,
        lineStyle: {
          color: '#5592EC',
        },
        label: {
          show: false,
        },
        itemStyle: {
          color: '#5592EC',
          borderColor: '#ffffff',
          borderWidth: 3,
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(
            0,
            0,
            0,
            1,
            [
              {
                offset: 0,
                color: 'rgba(85, 146, 236, 0.8)',
              },
              {
                offset: 1,
                color: 'rgba(255, 255, 255, 0.1)',
              },
            ],
            false,
          ),
        },
        data: qaTotalList.map((item: any) => item.value),
      },
      {
        name: '合格文件数',
        type: 'line',
        showSymbol: false,
        showAllSymbol: true,
        symbol: 'circle',
        symbolSize: 12,
        smooth: true,
        lineStyle: {
          color: 'rgba(59, 217, 193, 1)',
        },
        label: {
          show: false,
        },
        itemStyle: {
          color: 'rgba(59, 217, 193, 1)',
          borderColor: '#ffffff',
          borderWidth: 3,
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(
            0,
            0,
            0,
            1,
            [
              {
                offset: 0,
                color: 'rgba(59, 217, 193, 0.8)',
              },
              {
                offset: 1,
                color: 'rgba(255, 255, 255, 0.1)',
              },
            ],
            false,
          ),
        },
        data: qaPassList.map((item: any) => item.value),
      },
      {
        name: '问题文件数',
        type: 'line',
        showSymbol: false,
        showAllSymbol: true,
        symbol: 'circle',
        symbolSize: 12,
        smooth: true,
        lineStyle: {
          color: 'rgba(252, 120, 102, 1)',
        },
        label: {
          show: false,
        },
        itemStyle: {
          color: 'rgba(252, 120, 102, 1)',
          borderColor: '#ffffff',
          borderWidth: 3,
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(
            0,
            0,
            0,
            1,
            [
              {
                offset: 0,
                color: 'rgba(252, 120, 102, 0.8)',
              },
              {
                offset: 1,
                color: 'rgba(255, 255, 255, 0.1)',
              },
            ],
            false,
          ),
        },
        data: qaFailList.map((item: any) => item.value),
      },
    ],
  }
}

// 质量平均得分趋势
const qualitySubTrendOptions = ref<EChartsOption | any>()
const getQualitySubTrendOptions = (avgScoreList: any[]) => {
  return {
    animationDuration: 5000,
    title: {
      show: false,
    },
    legend: {
      show: false,
    },
    grid: {
      top: '20%',
      left: 20,
      right: 20,
      bottom: '10%',
      containLabel: true,
    },
    tooltip: {
      trigger: 'axis',
    },
    xAxis: {
      type: 'category',
      axisLine: {
        show: true,
        lineStyle: {
          color: '#CCCCCC',
        },
      },
      splitLine: {
        show: false,
      },
      axisLabel: {
        fontFamily: 'MicrosoftYaHei',
        fontSize: 12,
        color: '#333333',
      },
      axisTick: {
        show: true,
        alignWithLabel: true,
        inside: true,
      },
      boundaryGap: true,
      data: avgScoreList.map((item: any) => item.name),
    },
    yAxis: {
      type: 'value',
      min: 0,
      axisLine: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: '#CCCCCC',
          type: 'dashed',
        },
      },
      axisLabel: {
        show: true,
        margin: 20,
      },
      axisTick: {
        show: false,
      },
    },
    series: [
      {
        name: '',
        type: 'line',
        showAllSymbol: true,
        symbol: 'circle',
        symbolSize: 12,
        smooth: true,
        lineStyle: {
          color: 'rgba(85, 146, 236, 1)',
        },
        label: {
          show: false,
        },
        itemStyle: {
          color: 'rgba(85, 146, 236, 1)',
          borderColor: '#ffffff',
          borderWidth: 3,
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(
            0,
            0,
            0,
            1,
            [
              {
                offset: 0,
                color: 'rgba(85, 146, 236, 0.8)',
              },
              {
                offset: 1,
                color: 'rgba(255, 255, 255, 0.5)',
              },
            ],
            false,
          ),
        },
        data: avgScoreList.map((item: any) => item.value),
      },
    ],
  }
}
</script>

<style scoped lang="less">
.qualityMonitor {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  .top,
  .bottom {
    width: 100%;
    flex: 1;
    overflow: hidden;
    box-sizing: border-box;
    display: flex;
    .percentage {
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      bottom: 1vw;
      font-size: 0.8vw;
      color: #999999;
      i {
        color: #dd4e0a;
      }
    }
    .text {
      position: absolute;
      left: 1vw;
      top: 1vw;
      font-size: 1vw;
      color: #656d92;
    }
    .square {
      flex: 3;
      background-color: @withe;
      box-shadow: 0px 0px 7px 1px rgba(0, 0, 0, 0.05);
      overflow: hidden;
      position: relative;
    }
    .fileData {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      box-sizing: border-box;
      padding: 1vw;
      > div {
        box-sizing: border-box;
        flex: 1;
        background-position: center;
        background-size: cover;
        background-repeat: no-repeat;
        overflow: hidden;
        color: @withe;
        .title {
          padding-left: 15%;
          margin-top: 1vh;
          font-weight: 400;
          font-size: 1.2vw;
        }
        .num {
          text-align: center;
          margin-top: 1vh;
          font-size: 2vw;
        }
      }
      > div + div {
        margin-top: 1vw;
      }
    }
    .rectangle {
      flex: 5;
      background-color: @withe;
      box-shadow: 0px 0px 7px 1px rgba(0, 0, 0, 0.05);
      position: relative;
    }
    > div + div {
      margin-left: 1vw;
    }
  }
  .bottom {
    margin-top: 1vw;
  }
}
</style>
