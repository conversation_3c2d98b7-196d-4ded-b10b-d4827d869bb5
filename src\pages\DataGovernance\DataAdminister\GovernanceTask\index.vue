<template>
  <MainLayout>
    <template #header>
      <SearchTool @handle-search="handleSearch" />
    </template>
    <el-table
      v-loading="pageData.loading"
      header-cell-class-name="common-table-header"
      cell-class-name="common-table-cell"
      ref="multipleTableRef"
      :data="tableData"
      height="100%"
      style="width: 100%"
      row-key="tid"
    >
      <el-table-column property="taskName" label="任务名称" show-overflow-tooltip />
      <el-table-column property="ruleType" label="治理类型" show-overflow-tooltip />
      <el-table-column property="startTime" label="开始时间" show-overflow-tooltip />
      <el-table-column property="executeTime" label="执行时长" show-overflow-tooltip />
      <el-table-column property="taskStatusName" label="执行状态" show-overflow-tooltip />
      <el-table-column
        v-if="permission.hasButton(['governanceTask_details'])"
        label="操作"
        width="180"
        fixed="right"
      >
        <template #default="scope">
          <el-button
            @click="EditDetailDialogRef.handleOpen(scope.row)"
            title="详情"
            class="common-icon-btn"
            type="primary"
            text
            link
          >
            <template #icon>
              <SvgIcon name="details" />
            </template>
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <template #footer>
      <el-pagination
        v-model:currentPage="pageData.page"
        v-model:page-size="pageData.limit"
        :page-sizes="pageData.pageSizes"
        background
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageData.total"
        @size-change="pageData.handleSizeChange"
        @current-change="pageData.handleCurrentChange"
      />
    </template>
    <EditDetailDialog ref="EditDetailDialogRef" />
  </MainLayout>
</template>

<script setup lang="ts">
import MainLayout from '@/components/MainLayout/index.vue'
import SearchTool from './SearchTool.vue'
import usePageData from '@/hooks/usePageData'
import { qaTaskListApi } from '@/api/data_governance'
import EditDetailDialog from './EditDetailDialog.vue'
import { ref } from 'vue'
import useUserInfo from '@/store/useUserInfo'

const permission = useUserInfo()

const EditDetailDialogRef = ref<any>()

const { tableData, pageData } = usePageData(qaTaskListApi)
const handleSearch = (form: any) => {
  console.log(form, 'sssss')
  pageData.handleSearch(form, 1)
}
</script>

<style scoped></style>
