<template>
  <MainLayout>
    <template #header>
      <SearchTool
        @handle-search="handleSearch"
        @handle-create="createDetailsDialogRef.handleOpen(null, true)"
      />
    </template>
    <el-table
      v-loading="pageData.loading"
      header-cell-class-name="common-table-header"
      cell-class-name="common-table-cell"
      ref="multipleTableRef"
      :data="tableData"
      height="100%"
      style="width: 100%"
      row-key="tid"
    >
      <el-table-column property="nodeName" label="算子名称" show-overflow-tooltip />
      <el-table-column property="typeName" label="算子分组" show-overflow-tooltip />
      <el-table-column property="createTime" label="创建时间" show-overflow-tooltip />
      <el-table-column
        v-if="permission.hasButton(['customModel_edit', 'customModel_details', 'customModel_del'])"
        label="操作"
        width="180"
        fixed="right"
      >
        <template #default="scope">
          <el-button
            v-if="permission.hasButton('customModel_edit')"
            @click="createDetailsDialogRef.handleOpen(scope.row.tid, true)"
            class="common-icon-btn"
            type="primary"
            text
            link
            title="编辑"
          >
            <template #icon>
              <SvgIcon name="edit" />
            </template>
          </el-button>
          <el-button
            v-if="permission.hasButton('customModel_details')"
            @click="createDetailsDialogRef.handleOpen(scope.row.tid, false)"
            title="详情"
            class="common-icon-btn"
            type="primary"
            text
            link
          >
            <template #icon>
              <SvgIcon name="details" />
            </template>
          </el-button>
          <el-button
            v-if="permission.hasButton('customModel_del')"
            @click="handleDel(scope.row.tid)"
            title="删除"
            class="common-icon-btn"
            type="danger"
            text
            link
          >
            <template #icon>
              <SvgIcon name="delete" />
            </template>
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <template #footer>
      <el-pagination
        v-model:currentPage="pageData.page"
        v-model:page-size="pageData.limit"
        :page-sizes="pageData.pageSizes"
        background
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageData.total"
        @size-change="pageData.handleSizeChange"
        @current-change="pageData.handleCurrentChange"
      />
    </template>
    <CreateDetailsDialog ref="createDetailsDialogRef" @handle-refresh="handleSearch" />
  </MainLayout>
</template>

<script setup lang="ts">
import MainLayout from '@/components/MainLayout/index.vue'
import SearchTool from './SearchTool.vue'
import CreateDetailsDialog from './CreateDetailsDialog.vue'
import usePageData from '@/hooks/usePageData'
import { ref } from 'vue'
import { queryCustomPageByParamApi, removeCustomByIdApi } from '@/api/online_model'
import { ElMessage, ElMessageBox } from 'element-plus'
import useUserInfo from '@/store/useUserInfo'

const permission = useUserInfo()

const { tableData, pageData } = usePageData(queryCustomPageByParamApi, false)

const handleSearch = (form?: any) => {
  pageData.handleSearch(form ? { ...form, isSystem: 0 } : { isSystem: 0 }, 1)
}

handleSearch(null)

// 删除
const handleDel = (tid: string) => {
  ElMessageBox.confirm('确认要删除该算子嘛？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    try {
      const { status, message } = await removeCustomByIdApi(tid)
      if ([200].includes(status)) {
        ElMessage.success(message)
        pageData.handleSearch(null, 1)
      } else {
        ElMessage.error(message)
      }
    } catch (error) {
      console.error(error)
    }
  })
}

const createDetailsDialogRef = ref<any>()
</script>

<style lang="less" scoped></style>
