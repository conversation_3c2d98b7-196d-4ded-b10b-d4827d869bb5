<template>
  <ElDialog
    v-model="dialogFormVisible"
    top="10vh"
    title="详情"
    width="800px"
    :close-on-click-modal="false"
  >
    <ElRow v-if="isApproval" :gutter="20" class="info">
      <ElCol :span="8">
        <el-text class="text">提交版本：V{{ tableData.version }}版本</el-text>
      </ElCol>
      <ElCol :span="8">
        <el-text class="text">提交时间：{{ tableData.applyTime }}</el-text>
      </ElCol>
      <ElCol :span="8">
        <el-text class="text">提交人：{{ tableData.applyUname }}</el-text>
      </ElCol>
      <ElCol :span="24">
        <el-text class="text">描述：{{ tableData.applyDesc }}</el-text>
      </ElCol>
      <ElCol :span="8">
        <el-text class="text">审核人：{{ tableData.authUname }}</el-text>
      </ElCol>
      <ElCol :span="8">
        <el-text class="text">审核时间：{{ tableData.authTime }}</el-text>
      </ElCol>
      <ElCol :span="8">
        <el-text class="text">审核结果：{{ tableData.authStatusStr }}</el-text>
      </ElCol>
      <ElCol :span="24">
        <el-text class="text">审核建议：{{ tableData.authDesc }}</el-text>
      </ElCol>
    </ElRow>
    <div v-else class="title">
      <el-text style="font-weight: 600">V{{ tableData.version }}版本</el-text>
      <div>
        <el-text class="text">申请人：{{ tableData.applyUname }}</el-text>
        <el-text class="text"> 审核人：{{ tableData.authUname }}</el-text>
        <el-text class="text">发布时间：{{ tableData.releaseTime }}</el-text>
      </div>
    </div>
    <div class="table">
      <el-table
        v-loading="loading"
        header-cell-class-name="common-table-header"
        cell-class-name="common-table-cell"
        ref="multipleTableRef"
        :data="tableData.dataCatalogApplyRelationVOList"
        height="50vh"
        style="width: 100%"
        row-key="tid"
        :tree-props="treeProps"
        default-expand-all
      >
        <el-table-column property="catalogAlias" label="目录名称" show-overflow-tooltip />
        <el-table-column property="level" label="目录级别">
          <template #default="scope">{{ scope.row.bsType === 1 ? '菜单' : '数据库' }}</template>
        </el-table-column>
        <el-table-column property="catalogDesc" label="描述" show-overflow-tooltip />
        <el-table-column v-if="isApproval" property="editFlag" label="状态" :width="100">
          <template #default="scope">
            <ElTag v-if="scope.row.editFlag === 2" effect="dark" type="success">新增</ElTag>
            <ElTag v-else-if="scope.row.editFlag === 3" effect="dark" type="warning">修改</ElTag>
            <ElTag v-else-if="scope.row.editFlag === 4" effect="dark" type="danger">删除</ElTag>
            <ElTag v-else effect="dark" type="primary">默认</ElTag>
          </template>
        </el-table-column>
        <el-table-column property="createTime" label="创建时间" show-overflow-tooltip />
      </el-table>
    </div>
  </ElDialog>
</template>
<script setup lang="ts">
import { dataCatalogQueryByIdApi } from '@/api/lake_catalog'
import useDialogForm from '@/hooks/useDialogForm'
import { ElCol, ElDialog, ElMessage, ElRow } from 'element-plus'
import { ref } from 'vue'

withDefaults(defineProps<{ isApproval?: boolean }>(), { isApproval: false })

const { dialogFormVisible, setDialogFormVisible } = useDialogForm()

const treeProps = { children: 'childVOList' }

const loading = ref<boolean>(false)
const tableData = ref<any>({})

const handleOpen = async (row: any) => {
  try {
    const { data, message, status } = await dataCatalogQueryByIdApi(row.tid)
    if ([200].includes(status)) {
      tableData.value = data
      setDialogFormVisible(true)
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}

defineExpose({ handleOpen })
</script>
<style lang="less" scoped>
.title {
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .text + .text {
    margin-left: 15px;
  }
}
.info {
  margin-bottom: 10px;
  .el-col {
    line-height: 30px;
  }
}
</style>
