<template>
  <ElDialog
    v-model="dialogFormVisible"
    top="10vh"
    title="服务地址"
    width="900px"
    :close-on-click-modal="false"
    @close="handleDialogClose"
  >
    <ElTable
      header-cell-class-name="common-table-header"
      cell-class-name="common-table-cell"
      :data="tableData"
    >
      <el-table-column property="key" label="实时数据" width="150" />
      <el-table-column property="value" label="地址">
        <template #default="scope">
          <el-input size="small" v-model="scope.row.value" placeholder="地址" readonly></el-input>
        </template>
      </el-table-column>
      <el-table-column prop="date" label="操作" width="100">
        <template #default="scope">
          <el-button
            @click="hanldeCopyLink(scope.row.value)"
            class="common-icon-btn"
            type="primary"
            plain
            link
            title="复制"
          >
            <template #icon>
              <SvgIcon name="copyDocument" />
            </template>
          </el-button>
        </template>
      </el-table-column>
    </ElTable>
  </ElDialog>
</template>
<script setup lang="ts">
import useDialogForm from '@/hooks/useDialogForm'
import clipboardCopy from 'clipboard-copy'
import { ElDialog, ElMessage, ElTable } from 'element-plus'
import { ref } from 'vue'

const { dialogFormVisible, handleDialogClose, setDialogFormVisible } = useDialogForm()

const tableData = ref<any[]>([])

const hanldeCopyLink = async (url: string) => {
  try {
    await clipboardCopy(url)
    ElMessage.success('复制成功！')
  } catch (error) {
    ElMessage.error('复制失败！')
  }
}

const handleOpen = async (row: any) => {
  tableData.value = row
  setDialogFormVisible(true)
}

defineExpose({ handleOpen })
</script>
<style lang="less" scoped></style>
