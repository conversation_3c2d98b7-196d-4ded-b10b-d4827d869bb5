<template>
  <el-form ref="ruleFormRef" :model="ruleForm">
    <div class="search-content">
      <div class="search-input">
        <div class="search-item">
          <el-form-item prop="likeNodeName">
            <el-input
              class="form-item"
              v-model="ruleForm.likeNodeName"
              placeholder="请输入任务名称"
              @keydown.enter="handleSearch"
            >
              <template #suffix>
                <SvgIcon @click="handleSearch" style="cursor: pointer" name="search" />
              </template>
            </el-input>
          </el-form-item>
        </div>
        <div class="search-item">
          <el-form-item prop="mobile">
            <el-date-picker
              @change="handleSearch"
              style="width: 100%"
              value-format="YYYY-MM-DD HH:mm:ss"
              v-model="ruleForm.dateRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              clearable
            />
          </el-form-item>
        </div>
      </div>
      <div v-if="permission.hasButton('customModel_add')" class="search-btn">
        <el-button type="primary" @click="emit('handleCreate')">
          <template #icon>
            <SvgIcon name="add" />
          </template>
          新建算子
        </el-button>
      </div>
    </div>
  </el-form>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'
import type { FormInstance } from 'element-plus'
import useUserInfo from '@/store/useUserInfo'

const permission = useUserInfo()

const ruleFormRef = ref<FormInstance>()
const ruleForm = reactive<any>({})

const handleSearch = () => {
  const searchForm = {
    likeNodeName: ruleForm.likeNodeName,
    geCreateTime:
      ruleForm.dateRange && ruleForm.dateRange.length ? ruleForm.dateRange[0] : undefined,
    leCreateTime:
      ruleForm.dateRange && ruleForm.dateRange.length ? ruleForm.dateRange[1] : undefined,
  }
  emit('handleSearch', searchForm)
}

const emit = defineEmits<{
  (e: 'handleSearch', form: any): void
  (e: 'handleCreate'): void
}>()
</script>
