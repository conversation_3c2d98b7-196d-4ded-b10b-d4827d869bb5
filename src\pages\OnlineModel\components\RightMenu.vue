<template>
  <div class="rightMenu" :class="{ expanded: isShowMenu }">
    <transition name="sidebar-slide">
      <div class="right" v-show="isShowMenu">
        <div class="header">
          <SvgIcon class="icon" name="onlineModelMenu" />
          <span>算子参数</span>
        </div>
        <div class="right-content">
          <el-scrollbar>
            <div class="title">算子名称：{{ selectNode?.text }}</div>
            <DynamicForm
              ref="DynamicFormRef"
              :disabled="isRunning"
              :form-data="selectNode?.data?.nodeParam || []"
            />
          </el-scrollbar>
        </div>
      </div>
    </transition>
    <div class="aside-title" @click="handleShowMenu">
      <SvgIcon :name="isShowMenu ? 'DArrowRight' : 'DArrowLeft'" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import DynamicForm from '@/components/DynamicForm/index.vue'

const DynamicFormRef = ref<any>()
const validateForm = () => DynamicFormRef.value?.validateForm()

const isShowMenu = ref<boolean>(true)
const handleShowMenu = () => {
  isShowMenu.value = !isShowMenu.value
}

defineExpose({ validateForm })
defineProps<{ selectNode: any; isRunning: boolean }>()
</script>

<style scoped lang="less">
.rightMenu {
  height: 100%;
  width: 1px;
  position: relative;
  z-index: 100;
  transition: width 0.3s ease; /* 添加过渡效果 */
  user-select: none;
  .right {
    height: 100%;
    width: 100%;
    background-color: @withe;
    box-sizing: border-box;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    .header {
      height: 60px;
      border-bottom: 1px solid #dcdfe6;
      box-sizing: border-box;
      padding: 0 20px;
      line-height: 60px;

      .icon {
        font-size: 18px;
        color: var(--el-color-primary);
      }
      span {
        margin-left: 10px;
        font-size: 16px;
        font-weight: 400;
        color: #333333;
      }
    }
    .right-content {
      flex: 1;
      overflow: hidden;
      box-sizing: border-box;
      padding: 20px 15px;
      .title {
        font-size: 14px;
        color: #333333;
        font-weight: 400;
        margin-bottom: 10px;
      }
      :deep(.el-form-item) {
        display: block;
        .el-checkbox,
        .el-radio-group,
        .el-radio {
          display: block;
        }
      }
    }
  }
  &.expanded {
    width: 200px;
    box-shadow: 0 8px 8px 8px rgba(0, 0, 0, 0.05);
  }
  .sidebar-slide-enter-active,
  .sidebar-slide-leave-active {
    transition: all 0.3s ease; /* 添加过渡效果 */
  }

  .aside-title {
    position: absolute;
    top: calc(50% - 50px);
    left: -12px;
    z-index: 101;
    background-color: @withe;
    box-shadow: -2px 0px 4px 4px rgba(0, 0, 0, 0.05);
    width: 12px;
    height: 100px;
    line-height: 100px;
    cursor: pointer;
    border-radius: 16px 0 0 16px;
    font-size: 12px;
    color: #999999;
  }
}
</style>
