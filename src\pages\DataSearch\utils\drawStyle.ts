export default [
  // 线的默认样式
  {
    id: 'gl-draw-line',
    type: 'line',
    filter: ['all', ['==', '$type', 'LineString'], ['!=', 'mode', 'static']],
    layout: {
      'line-cap': 'round',
      'line-join': 'round'
    },
    paint: {
      'line-color': '#438EE4',
      'line-dasharray': [0.2, 2],
      'line-width': 4,
      'line-opacity': 0.7
    }
  },
  // 面的默认样式
  {
    id: 'gl-draw-polygon-fill',
    type: 'fill',
    filter: ['all', ['==', '$type', 'Polygon'], ['!=', 'mode', 'static']],
    paint: {
      'fill-color': '#438EE4',
      'fill-outline-color': '#438EE4',
      'fill-opacity': 0.1
    }
  },
  {
    id: 'gl-draw-polygon-stroke',
    type: 'line',
    filter: ['all', ['==', '$type', 'Polygon'], ['!=', 'mode', 'static']],
    paint: {
      'line-color': '#438EE4',
      'line-width': 2
    }
  },
  // 非激活状态下的线和面的样式
  {
    id: 'gl-draw-line-inactive',
    type: 'line',
    filter: ['all', ['==', '$type', 'LineString'], ['==', 'active', 'false']],
    layout: {
      'line-cap': 'round',
      'line-join': 'round'
    },
    paint: {
      'line-color': '#438EE4',
      'line-dasharray': [0.2, 2],
      'line-width': 4,
      'line-opacity': 0.7
    }
  },
  {
    id: 'gl-draw-polygon-fill-inactive',
    type: 'fill',
    filter: ['all', ['==', '$type', 'Polygon'], ['==', 'active', 'false']],
    paint: {
      'fill-color': '#438EE4',
      'fill-outline-color': '#438EE4',
      'fill-opacity': 0.1
    }
  },
  {
    id: 'gl-draw-polygon-stroke-inactive',
    type: 'line',
    filter: ['all', ['==', '$type', 'Polygon'], ['==', 'active', 'false']],
    paint: {
      'line-color': '#438EE4',
      'line-width': 2
    }
  }
]
