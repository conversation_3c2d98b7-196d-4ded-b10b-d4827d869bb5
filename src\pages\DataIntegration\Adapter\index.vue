<template>
  <div class="adapter">
    <ElScrollbar>
      <ul class="adapterList">
        <AdapterItem
          v-for="p in tableData"
          :key="p.tid"
          :card="p"
          @click="handleOpen(p)"
          :color="getRandomColor()"
        />
      </ul>
    </ElScrollbar>
    <ElDialog
      top="10vh"
      :title="`${rowData.adapterName}适配器详情`"
      v-model="dialogFormVisible"
      width="560px"
      :close-on-click-modal="false"
      @close="handleDialogClose"
    >
      <ElForm :model="rowData" label-width="auto">
        <ElFormItem label="适配器：">
          <span>{{ rowData.adapterName }}适配器</span>
        </ElFormItem>
        <ElFormItem label="适配器版本：">
          <span>{{ rowData.adapterVersion }}</span>
        </ElFormItem>
        <ElFormItem label="数据源名称：">
          <span>{{ rowData.datasourceName || '--' }}</span>
        </ElFormItem>
        <ElFormItem label="描述：">
          <span>{{ rowData.adapterDesc }}</span>
        </ElFormItem>
      </ElForm>
    </ElDialog>
  </div>
</template>

<script setup lang="ts">
import { ElDialog, ElForm, ElFormItem, ElMessage, ElScrollbar } from 'element-plus'
import AdapterItem from './AdapterItem.vue'
import useDialogForm from '@/hooks/useDialogForm'
import { ref } from 'vue'
import { adapterQueryListApi } from '@/api/adapter'
import { queryPageByParamApi } from '@/api/data_source'

const tableData = ref<any[]>([])
const getTableData = async () => {
  try {
    const { data, message, status } = await adapterQueryListApi({ adapterTypeAsc: true })
    if (status === 200) {
      tableData.value = data
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}
getTableData()

const rowData = ref<any>({})
const { dialogFormVisible, setDialogFormVisible, handleDialogClose } = useDialogForm()
const handleOpen = async (row: any) => {
  try {
    rowData.value = row
    const { data, message, status } = await queryPageByParamApi({ adapterCode: row.adapterCode })
    if (status === 200) {
      rowData.value.datasourceName = data.map((item: any) => item.datasourceName).join(',')
      setDialogFormVisible(true)
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}

const getRandomColor = (): string => {
  const colors: string[] = [
    '23, 83, 210',
    '244, 189, 79',
    '246, 94, 94',
    '108, 69, 223',
    '93, 192, 81',
    '239, 142, 61',
  ]
  // 生成一个随机索引
  const randomIndex = Math.floor(Math.random() * colors.length)

  return colors[randomIndex]
}
</script>

<style lang="less" scoped>
.adapter {
  height: 100%;
  box-sizing: border-box;
  overflow: hidden;
  .adapterList {
    display: grid;
    justify-content: space-between;
    grid-template-columns: repeat(auto-fill, minmax(370px, 1fr));
    grid-gap: 20px;
  }
  :deep(.el-form-item) {
    margin-bottom: 0px;
    .el-form-item__content {
      line-height: 32px;
    }
  }
}
</style>
