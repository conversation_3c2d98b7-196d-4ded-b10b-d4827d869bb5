import request, { baseUrl } from '@/utils/request'
import useUserInfo from '@/store/useUserInfo'

// 用户文件列表
export const userFileListApi = (data: any) => request.get(`/userFile/list`, data)

// 元数据详情
export const userFileListDetailApi = (data: any) => request.get(`/userFile/detail`, data)

// 修改元数据
export const updateDetailApi = (data: any) => request.post(`/userFile/updateDetail`, data)

// 批量删除文件
export const removeApi = (data: any) => request.post(`/userFile/remove`, data)

//创建文件夹
export const mkdirApi = (data: any) => request.get(`/userFile/mkdir`, data)

// 修改文件夹名称
export const updateDirApi = (data: any) => request.post(`/userFile/updateDir`, data)

// 根据后缀查询-文件类型（树）
export const queryBySuffixApi = (data: any) => request.get(`/filetype/queryBySuffix`, data)

// 预览文件 链接预览
export const linkPreviewApi = (tid: string) => {
  const userInfo = useUserInfo()
  return `${baseUrl}/fileTransfer/preview?tid=${tid}&token=${userInfo.token}`
}

//  预览文件 接口预览
export const filePreviewApi = (data: any) => request.get(`/fileTransfer/preview`, data)

// 修改文档内容
export const updateDocumentFileApi = (data: any) =>
  request.post(`/fileTransfer/updateDocumentFile`, data)

// 预览office文件
export const previewOfficeApi = (data: any) =>
  request.get(`/internal/wopi/files/formOfficeUrl`, data)

// 发布文件服务
export const publishApi = (tid: any) => request.get(`/userFile/publish/${tid}`)

// 获取文件服务
export const serverListApi = (tid: any) => request.get(`/userFile/serverList/${tid}`)

// 获取文件血缘关系
export const queryRelationListApi = (params: any) =>
  request.get(`/fileRelation/queryRelationList`, params)

//查询列表
export const userFileVersionListApi = (params: any) => request.get(`/userFileVersion/list`, params)

//治理详情列表
export const qaDetailListApi = (params: any) => request.get(`/qaDetail/list`, params)

// 自动数据治理
export const autoRepairApi = (params: any) => request.get(`/qaDetail/autoRepair`, params)

// 手动数据治理
export const uploadRepairFileApi = () => `${baseUrl}/qaDetail/uploadRepairFile`

// 获取矢量跳转地址
export const getVectorDrawUrlApi = () => request.get(`/vectorDraw/getUrl`)

// 查询矢量文件样式和服务地址
export const queryVectorDrawStyleApi = (params: any) =>
  request.get(`/vectorDraw/queryStyle`, params)

// 数据分类列表
export const productTypeListApi = (data: any) => request.post(`/dateCenter/productTypeList`, data)

// 标签列表
export const productLabelPageApi = (data: any) => request.post(`/dateCenter/productLabelPage`, data)

//行政区划树
export const getAdmincodeTreeListApi = () => request.get(`/dateCenter/getAdmincodeTreeList`)

// 根据文件id查询数据详情
export const getProductDetailByUserFileIdApi = (data: any) =>
  request.get(`/dateCenter/getProductDetailByUserFileId`, data)

// 查询文件元数据
export const getExcellentDetailApi = (data: any) =>
  request.get(`/dateCenter/getExcellentDetail`, data)

// 注册数据
export const productRegisterApi = (data: any) => request.post(`/dateCenter/productRegister`, data)

// 获取文件服务
export const getServerListApi = (tid: any) => request.post(`/dateCenter/serverList/${tid}`)

//裁剪并下载文件
export const cropAndDownloadApi = (params: string) =>
  `${baseUrl}/fileHandle/cropAndDownload${params}`

// 查询文件元数据
export const getDirTreeApi = (data: any) => request.get(`/userFile/dirTree`, data)

// 获取专题矢量子服务
export const getChildServerApi = (data: any) => request.get(`/groupLayer/getChildServer`, data)

// 图层组服务发布：自图层需要传、serverName为图层组名称==layerName
export const groupLayerPublishApi = (data: any) => request.post(`/groupLayer/publish`, data)

// 获取服务前缀
export const getEServerUrlPreApi = (data: any) => request.post(`/groupLayer/preview`, data)

// 切片处理url
export const getProcessUrlApi = () => request.get(`/constant/getProcessUrl`)

// 质检报告
export const reportApi = (data: any) => request.get(`/qaDetail/report`, data)

//导出资源登记表
export const exportCatalogListApi = () => `${baseUrl}/userFile/exportList`

// 批量移动
export const batchMoveApi = (data: any) => request.post(`/userFile/batchMove`, data)

// 批量复制数据
export const batchCopyApi = (data: any) => request.post(`/userFile/batchCopy`, data)
