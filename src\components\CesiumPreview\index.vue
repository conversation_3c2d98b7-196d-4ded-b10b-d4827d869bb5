<template>
  <transition name="fade">
    <div v-if="showViewer" class="common-preview-wrapper">
      <div class="tip-wrapper">
        <div></div>
        <p>{{ getFileNameComplete(rowData) }}</p>
        <div class="tip-right">
          <SvgIcon name="closeBold" @click="handleClose" class="close-icon" />
        </div>
      </div>
      <div class="map-wrapper">
        <div ref="threeRef" id="cesiumRef" class="olMap"></div>
      </div>
    </div>
  </transition>
</template>

<script setup lang="ts">
import { nextTick, ref } from 'vue'
import { userFileListDetailApi } from '@/api/data_catalog'
import { ElMessage } from 'element-plus'
import { getFileNameComplete } from '@/utils/fileUtils'
import useCesium from '@/hooks/useCesium'
import useThree from '@/hooks/useThree'
import { threePreview } from '@/utils/fileMap'

const { initCesium, set3DPreviewLayers, destroyCesium } = useCesium()

const { initThree, threeRef, destroyThree } = useThree()

const showViewer = ref<boolean>(false)
const handleClose = () => {
  destroyCesium()
  destroyThree()
  showViewer.value = false
}

const rowData = ref<any>({})
const handleOpenPreview = async (row: any) => {
  try {
    const { data, status, message } = await userFileListDetailApi({ tid: row.tid })
    if ([200].includes(status)) {
      setPreviewData(data)
    } else {
      ElMessage.error(message)
    }
  } catch (error) {
    console.error(error)
  }
}

const setPreviewData = (data: any) => {
  const { gisPreviewVO } = data
  const format: string = gisPreviewVO.url.split('.')[1].toLowerCase()
  if (!threePreview.includes(format)) return ElMessage.warning('暂不支持该格式预览')
  rowData.value = data
  showViewer.value = true
  nextTick(() => {
    if (['json'].includes(format)) {
      initCesium('cesiumRef', () => {
        set3DPreviewLayers(gisPreviewVO.url)
      })
    }
    if (threePreview.filter((item: string) => item !== 'json').includes(format)) {
      initThree(gisPreviewVO.url, format)
    }
  })
}

defineExpose({ handleOpenPreview })
</script>

<style scoped lang="less">
.common-preview-wrapper {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  .tip-wrapper {
    background: rgba(0, 0, 0, 0.5);
    padding: 0 48px;
    width: 100%;
    height: 48px;
    line-height: 48px;
    color: @withe;
    font-size: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-sizing: border-box;
    .tip-right {
      display: flex;
      height: 100%;
      align-items: center;
      .close-icon {
        cursor: pointer;
      }
    }
  }
  .map-wrapper {
    flex: 1;
    box-sizing: border-box;
    padding: 30px 40px;
    position: relative;
    overflow: hidden;
    .olMap {
      height: 100%;
      width: 100%;
      box-sizing: border-box;
      background-color: rgba(0, 0, 0, 0.9);
      overflow: hidden;
    }
  }
}
</style>
