<template>
  <div class="dataHouse">
    <div class="topMain">
      <el-form ref="ruleFormRef" :model="ruleForm">
        <div class="search-content">
          <div class="search-input">
            <div class="search-item">
              <el-form-item prop="databaseId">
                <el-select
                  class="form-item"
                  v-model="ruleForm.databaseId"
                  placeholder="选择数据库"
                  clearable
                >
                  <el-option
                    v-for="p in dataBaseOptions"
                    :key="p.tid"
                    :label="p.catalogAlias"
                    :value="p.tid"
                  />
                </el-select>
              </el-form-item>
            </div>
          </div>
        </div>
      </el-form>
      <div class="topContent">
        <div class="topNav">
          <p @click="handleRevoke" class="optbtn">
            <img :src="revoke" alt="revoke" />
            <span>撤销</span>
          </p>
          <p @click="handleRun" class="optbtn">
            <ElIcon class="icon"><SvgIcon name="videoPlay" /></ElIcon>
            <span>运行</span>
          </p>
        </div>
        <div class="sql-preview">
          <Codemirror
            v-model="ruleForm.sql"
            readOnly
            :disabled="config.disabled"
            :extensions="config.extensions"
            :tabSize="config.tabSize"
            :indentWithTab="config.indentWithTab"
            :autofocus="config.autofocus"
            placeholder="Please enter the code."
            :style="{
              height: config.height,
              width: '100%',
            }"
          />
        </div>
      </div>
    </div>
    <div ref="bottomRef" class="bottomMain">
      <div @mousedown.stop.prevent="startVerticalResize" class="drag-handle"></div>
      <ElTabs v-model="activeName">
        <ElTabPane name="log">
          <template #label>
            <ElTooltip
              content="最多返回10个查询结果,且每条结果的数据最多显示1000条"
              placement="top"
            >
              <ElIcon style="color: RGBA(255, 172, 41, 1)"><SvgIcon name="WarningFilled" /></ElIcon>
            </ElTooltip>
            <span style="margin-left: 10px">运行日志</span>
          </template>
          <ElScrollbar>
            <ul class="logData">
              <li v-for="(p, i) in displayedText" :key="i" v-html="p"></li>
            </ul>
          </ElScrollbar>
        </ElTabPane>
        <ElTabPane name="table" label="运行记录">
          <div class="logList">
            <div class="logListTable">
              <el-table
                header-cell-class-name="common-table-header"
                cell-class-name="common-table-cell"
                ref="multipleTableRef"
                :data="tableData"
                height="100%"
                style="width: 100%"
              >
                <el-table-column type="expand">
                  <template #default="props">
                    <div class="logListTableDetails">
                      <p>运行日志:</p>
                      <p v-html="props.row.log.replace(/\n/g, '<br>')"></p>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column property="startTime" label="运行时间" show-overflow-tooltip />
                <el-table-column property="sql" label="SQL语句" show-overflow-tooltip />
                <el-table-column property="statusName" label="执行结果" show-overflow-tooltip />
              </el-table>
            </div>
            <div class="logListFooter">
              <el-pagination
                v-model:currentPage="pageData.page"
                v-model:page-size="pageData.limit"
                :page-sizes="pageData.pageSizes"
                background
                layout="total, sizes, prev, pager, next, jumper"
                :total="pageData.total"
                @size-change="pageData.handleSizeChange"
                @current-change="pageData.handleCurrentChange"
              />
            </div>
          </div>
        </ElTabPane>
        <ElTabPane
          v-for="(item, i) in tabData"
          :key="i"
          :name="i.toString()"
          :label="item.tabName"
          lazy
        >
          <el-table
            header-cell-class-name="common-table-header"
            cell-class-name="common-table-cell"
            ref="multipleTableRef"
            :data="item.table"
            height="100%"
            style="width: 100%"
          >
            <el-table-column
              v-for="p in item.headers"
              :key="p.fieldName"
              :prop="p.fieldName"
              :label="p.fieldName"
              min-width="150"
            >
              <template #header>
                <span :title="p.fieldName">{{ p.fieldName }}</span>
              </template>
              <template #default="scope">
                <span :title="scope.row[p.fieldName]">{{ scope.row[p.fieldName] || '--' }}</span>
              </template>
            </el-table-column>
          </el-table>
        </ElTabPane>
      </ElTabs>
    </div>
  </div>
</template>
<script setup lang="ts">
import {
  ElIcon,
  ElLoading,
  ElMessage,
  ElScrollbar,
  ElTabPane,
  ElTabs,
  ElTooltip,
} from 'element-plus'

import revoke from '@/assets/commonImg/revoke.png'
import useDragResize from '@/hooks/useDragResize'
import { Codemirror } from 'vue-codemirror'
import { sql } from '@codemirror/lang-sql'
import { computed, reactive, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import { catalogQueryDatabaseByIdApi, getHistoryPageApi, sqlEditRunApi } from '@/api/data_house'
import { v4 as uuidV4 } from 'uuid'
import useFetchStream from './useFetchStream'
import usePageData from '@/hooks/usePageData'

const { tableData, pageData } = usePageData(getHistoryPageApi)

const route = useRoute()

interface RuleForm {
  uuid: string
  databaseId: string
  sql: string
}

const ruleForm = reactive<RuleForm>({
  uuid: '',
  databaseId: '',
  sql: '',
})

const dataBaseOptions = ref<any[]>([])
const getOptions = async (dataCatalogId: any = route.query.dataCatalogId) => {
  if (!dataCatalogId) return
  try {
    const { data, message, status } = await catalogQueryDatabaseByIdApi(dataCatalogId)
    if ([200].includes(status)) {
      dataBaseOptions.value = data
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}
getOptions()

const dataCatalogId = computed(() => route.query.dataCatalogId as string)
watch(dataCatalogId, (val: string) => {
  getOptions(val)
  ruleForm.databaseId = ''
  handleRevoke()
})

const { displayedText, fetchStream, clearDisplayedText } = useFetchStream()

const config = ref<any>({
  disabled: false,
  indentWithTab: true,
  tabSize: 2,
  autofocus: false,
  height: '100%',
  extensions: [sql()],
})

const handleRevoke = () => {
  ruleForm.sql = ''
  tabData.value = []
  clearDisplayedText()
}

const tabData = ref<any[]>([])
const handleRun = async () => {
  if (!ruleForm.databaseId) return ElMessage.warning('请选择数据库！')
  if (!ruleForm.sql) return ElMessage.warning('请输入SQL语句！')
  const loading = ElLoading.service({
    lock: true,
    text: '正在执行，请稍等...',
    background: 'rgba(0, 0, 0, 0.7)',
  })
  try {
    ruleForm.uuid = uuidV4()
    fetchStream(ruleForm.uuid)
    const { data, message, status } = await sqlEditRunApi(ruleForm)
    if ([200].includes(status)) {
      tabData.value = data
      pageData.handleSearch(null, 1)
    } else {
      ElMessage.error(message)
      tabData.value = []
    }
    loading.close()
  } catch (error) {
    loading.close()
  }
}

const { startVerticalResize, bottomRef } = useDragResize()
const activeName = ref<string>('log')
</script>
<style lang="less" scoped>
.dataHouse {
  height: 100%;
  box-sizing: border-box;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  .topMain {
    flex: 1;
    box-sizing: border-box;
    overflow: hidden;
    box-shadow: 0px 0px 7px 1px rgba(0, 0, 0, 0.05);
    background-color: @withe;
    min-height: 150px;
    display: flex;
    flex-direction: column;
    padding: 0.7813vw;
    .topContent {
      flex: 1;
      box-sizing: border-box;
      overflow: hidden;
      margin-top: 10px;
      display: flex;
      flex-direction: column;
      .topNav {
        height: 36px;
        background: #f3f5f8;
        padding: 0 30px;
        display: flex;
        align-items: center;
        box-sizing: border-box;
        border: 1px solid #dcdfe6;
        .optbtn {
          display: flex;
          align-items: center;
          cursor: pointer;
          color: #333333;
          img {
            width: 16px;
          }
          .icon {
            font-size: 14px;
          }
          span {
            margin-left: 10px;
            font-size: 14px;
          }
        }
        .optbtn + .optbtn {
          margin-left: 30px;
        }
      }
      .sql-preview {
        flex: 1;
        box-sizing: border-box;
        overflow: hidden;
        border: 1px solid #dcdfe6;
        border-top: none;
      }
    }
  }
  .bottomMain {
    margin-top: 0.7813vw;
    height: 225px;
    min-height: 150px;
    box-sizing: border-box;
    box-shadow: 0px 0px 7px 1px rgba(0, 0, 0, 0.05);
    background-color: @withe;
    position: relative;
    .drag-handle {
      position: absolute;
      top: 0;
      height: 3px;
      width: 100%;
      cursor: ns-resize;
      z-index: 10;
    }
    :deep(.el-tabs) {
      height: 100%;
      display: flex;
    }
    :deep(.el-tabs__header) {
      .el-tabs__nav-scroll {
        padding: 0 0.7813vw;
      }
    }
    :deep(.el-tabs__content) {
      padding: 0 0.7813vw 0.7813vw 0.7813vw;
      box-sizing: border-box;
      flex: 1;
      overflow: hidden;
      .el-tab-pane {
        height: 100%;
        overflow: hidden;
        box-sizing: border-box;
      }
    }
    :global(.common-table-header .cell) {
      .ellipseLine();
    }
    :global(.common-table-cell .cell) {
      .ellipseLine();
    }
  }
  .logList {
    height: 100%;
    box-sizing: border-box;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    .logListTable {
      flex: 1;
      box-sizing: border-box;
      overflow: hidden;
      .logListTableDetails {
        padding-left: 60px;
        display: flex;
        width: 100%;
        p + p {
          margin-left: 10px;
        }
        p:nth-child(2) {
          .ellipseLine();
        }
      }
    }
    .logListFooter {
      margin-top: 10px;
      display: flex;
      justify-content: flex-end;
    }
  }
}
</style>
