<template>
  <MainLayout>
    <template #header>
      <SearchTool @handle-search="handleSearch" @handle-apply="handleApply" />
    </template>
    <el-table
      header-cell-class-name="common-table-header"
      cell-class-name="common-table-cell"
      ref="multipleTableRef"
      :data="tableData"
      height="100%"
      style="width: 100%"
      row-key="tid"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        type="selection"
        :selectable="handleSelectable"
        reserve-selection
        width="30"
      />
      <el-table-column :width="60">
        <template #default="scope">
          <ElTag v-if="scope.row.useStatus" effect="dark" type="primary">使用中</ElTag>
        </template>
      </el-table-column>
      <el-table-column property="version" label="目录版本" show-overflow-tooltip>
        <template #default="scope">
          {{ scope.row.version ? `V${scope.row.version}` : '--' }}
        </template>
      </el-table-column>
      <el-table-column property="applyDesc" label="描述" show-overflow-tooltip />
      <el-table-column property="authStatusStr" label="审核状态" show-overflow-tooltip />
      <el-table-column property="authDesc" label="发布状态" show-overflow-tooltip>
        <template #default="scope">
          {{ scope.row.release ? '已发布' : '未发布' }}
        </template>
      </el-table-column>
      <el-table-column property="applyUname" label="申请人" show-overflow-tooltip>
        <template #default="scope">
          {{ scope.row.applyUname || '--' }}
        </template>
      </el-table-column>
      <el-table-column property="applyTime" label="申请时间" show-overflow-tooltip />
      <el-table-column property="authUname" label="审核人" show-overflow-tooltip>
        <template #default="scope">
          {{ scope.row.authUname ? scope.row.authUname : '--' }}
        </template>
      </el-table-column>
      <el-table-column property="authTime" label="审核时间" show-overflow-tooltip>
        <template #default="scope">
          {{ scope.row.authTime ? scope.row.authTime : '--' }}
        </template>
      </el-table-column>
      <el-table-column
        v-if="
          permission.hasButton(['reviewedDir_details', 'reviewedDir_audit', 'reviewedDir_publish'])
        "
        label="操作"
        width="130"
        fixed="right"
      >
        <template #default="scope">
          <el-button
            v-if="permission.hasButton('reviewedDir_details')"
            @click="handleRouter(scope.row.tid)"
            title="详情"
            class="common-icon-btn"
            type="primary"
            plain
            link
          >
            <template #icon>
              <SvgIcon name="details" />
            </template>
          </el-button>
          <el-button
            v-if="[0].includes(scope.row.authStatus) && permission.hasButton('reviewedDir_audit')"
            @click="handleRouter(scope.row.tid, true)"
            title="审核"
            class="common-icon-btn"
            type="primary"
            plain
            link
          >
            <template #icon>
              <SvgIcon name="audit" />
            </template>
          </el-button>
          <el-button
            v-if="
              [1].includes(scope.row.authStatus) &&
              !scope.row.release &&
              permission.hasButton('reviewedDir_publish')
            "
            @click="handlePublish(scope.row.tid)"
            title="发布"
            class="common-icon-btn"
            type="primary"
            plain
            link
          >
            <template #icon>
              <SvgIcon name="position" />
            </template>
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <template #footer>
      <el-pagination
        v-model:currentPage="pageData.page"
        v-model:page-size="pageData.limit"
        :page-sizes="pageData.pageSizes"
        background
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageData.total"
        @size-change="pageData.handleSizeChange"
        @current-change="pageData.handleCurrentChange"
      />
    </template>
    <ApprovalDialog ref="ApprovalDialogRef" @handle-refresh="handleRefresh" />
  </MainLayout>
</template>

<script setup lang="ts">
import ApprovalDialog from '../DirInfo/ApprovalDialog.vue'
import MainLayout from '@/components/MainLayout/index.vue'
import SearchTool from './SearchTool.vue'
import usePageData from '@/hooks/usePageData'
import { dataCatalogApplyPageApi, modifyReleaseApi } from '@/api/lake_catalog'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox, ElTable } from 'element-plus'
import useUserInfo from '@/store/useUserInfo'
import { ref } from 'vue'

const permission = useUserInfo()

const router = useRouter()

const handleSelectable = (row: any) => {
  return [0].includes(row.authStatus)
}

const selectData = ref<any[]>([])
const handleSelectionChange = (list: any[]) => {
  selectData.value = list.map((item) => item.tid)
}

const ApprovalDialogRef = ref<InstanceType<typeof ApprovalDialog>>()
const handleApply = () => {
  if (!selectData.value.length) return ElMessage.warning('请选择需要审核的数据！')
  ApprovalDialogRef.value?.handleOpen(selectData.value)
}

const { tableData, pageData } = usePageData(dataCatalogApplyPageApi)

const multipleTableRef = ref<InstanceType<typeof ElTable>>()
const handleRefresh = () => {
  multipleTableRef.value?.clearSelection()
  pageData.handleSearch(null, 1)
}

const handleSearch = (data: any) => {
  pageData.handleSearch({ ...data }, 1)
}

const handleRouter = (tid: string, isApproval?: any) => {
  router.push({ name: 'dirInfo', query: { tid, isApproval } })
}

const handlePublish = (tid: string) => {
  ElMessageBox.confirm('确定要发布该条审批目录吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    try {
      const { message, status } = await modifyReleaseApi(tid)
      if ([200].includes(status)) {
        ElMessage.success(message)
        pageData.handleSearch(null, 1)
      } else {
        ElMessage.error(message)
      }
    } catch (error) {}
  })
}
</script>

<style lang="less" scoped></style>
