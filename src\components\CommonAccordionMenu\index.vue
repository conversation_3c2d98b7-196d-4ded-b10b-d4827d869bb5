<template>
  <el-menu class="sibar-menu" :default-active="defaultActive" @select="handleSelect">
    <template v-if="siderMenu && siderMenu.length > 0">
      <SidebarItem v-for="item in siderMenu" :key="item.path" :meun-item="item" />
    </template>
  </el-menu>
</template>

<script setup lang="ts">
import SidebarItem from './SidebarItem.vue'
import useRouterMenu from '@/hooks/useRouterMenu'
import { computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'

const router = useRouter()
const route = useRoute()

const { siderMenu } = useRouterMenu()

console.log(route, route.path.split('/'), 'ssssssss')

const defaultActive = computed(() => {
  if (['addEditRules'].includes(route.name as any)) {
    return 'qualityRules'
  }
  const name: any[] = route.path.split('/')
  if (!route.meta.show && name.length >= 4) {
    return name[2]
  }
  return route.name
})

const handleSelect = (name: any) => {
  router.push({ name })
}
</script>

<style scoped lang="less">
.sibar-menu {
  border-right: none !important;
  width: 150px;
  height: 100%;
  box-shadow: 0px 0px 7px 1px rgba(0, 0, 0, 0.05);
  padding: 20px 0;
}

:deep(.el-menu-item.is-active) {
  color: @withe;
  background: var(--el-color-primary);
}
:deep(.el-sub-menu__title) {
  height: 36px;
}
:deep(.el-sub-menu) {
  margin-inline: 10px;
  // margin-block: 10px;
  .el-menu-item {
    // margin-left: 10px !important;
    line-height: 36px;
    padding-left: 28px !important;
  }
}
:deep(.el-menu-item) {
  // margin-inline: 10px;
  // margin-block: 10px;
  // border-radius: 6px;
  height: 36px;
}
</style>
