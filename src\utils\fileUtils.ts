import { unknownImg, fileImgMap } from '@/utils/fileMap'

// 获取文件图表
export const getFileIcon = (file: any): any => {
  if ([1].includes(file.isDir)) {
    return fileImgMap.get('dir')
  }
  if (file?.metadataVO?.thumbnailUrl) {
    return file?.metadataVO?.thumbnailUrl
  }
  if (fileImgMap.has(file.suffix?.toLowerCase())) {
    return fileImgMap.get(file.suffix.toLowerCase())
  }
  return unknownImg
}

// 获取完整的文件名
export const getFileNameComplete = (file: any) => {
  return `${file.fileName}${[0].includes(file.isDir) && file.suffix ? `.${file.suffix}` : ''}`
}

/**
 * 格式化文件大小
 * @param {number} size 文件大小
 * @param {boolean} isInteger 是否只显示整数位，默认不截取
 * @returns {string} 文件大小（带单位）
 */
export const calculateFileSize = (size: number, isInteger = false) => {
  const B = 1024
  const KB = Math.pow(1024, 2)
  const MB = Math.pow(1024, 3)
  const GB = Math.pow(1024, 4)
  if (isInteger) {
    // 截取为整数
    if (size < B) {
      return `${size}B`
    } else if (size < KB) {
      return `${(size / B).toFixed(0)}KB`
    } else if (size < MB) {
      return `${(size / KB).toFixed(0)}MB`
    } else if (size < GB) {
      return `${(size / MB).toFixed(0)}GB`
    } else {
      return `${(size / GB).toFixed(0)}TB`
    }
  } else {
    // 保留小数位
    if (size < B) {
      return `${size}B`
    } else if (size < KB) {
      return `${(size / B).toFixed(0)}KB`
    } else if (size < MB) {
      return `${(size / KB).toFixed(1)}MB`
    } else if (size < GB) {
      return `${(size / MB).toFixed(2)}GB`
    } else {
      return `${(size / GB).toFixed(3)}TB`
    }
  }
}

// 文件下载处理
export const downloadFile = (url: string) => {
  const aLink = document.createElement('a')
  aLink.style.display = 'none'
  aLink.href = url
  document.body.appendChild(aLink)
  aLink.click()
  document.body.removeChild(aLink) // 下载完成移除元素
}
