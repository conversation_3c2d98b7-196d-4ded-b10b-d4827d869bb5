<template>
  <div class="home">
    <div class="home-layout">
      <div class="home-main">
        <div class="main-aside">
          <div class="aside-item">
            <CommonTile name="数据资产-数据资源占比" :isActiveShow="false">
              <template #updateFrequency>
                <span class="frequency-label">更新频率：</span>
                <span class="frequency-value">6小时</span>
              </template>
            </CommonTile>
            <DataAssets v-if="dataTotal" :dataTotal />
          </div>
          <div class="aside-item">
            <CommonTile @handle-change="handleLakeChange" name="数据湖统计" />
            <DataLake v-if="catalogData" :type="lakeType" :catalogData />
          </div>
          <div class="aside-item">
            <CommonTile @handle-change="handleHouseChange" name="数据仓统计" />
            <DataHouse v-if="catalogData" :type="houseType" :catalogData />
          </div>
        </div>
        <div class="main-content">
          <DataStorage v-if="dataTotal" :dataTotal />
          <DataStatistics />
        </div>
        <div class="main-aside">
          <div class="aside-item">
            <CommonTile name="服务统计-数据转化率" :isActiveShow="false" />
            <ServeTotal />
          </div>
          <div class="aside-item">
            <CommonTile name="任务统计" :isActiveShow="false" />
            <TaskData />
          </div>
          <div class="aside-item">
            <CommonTile name="健康度" :isActiveShow="false" />
            <HealthLevel />
          </div>
        </div>
      </div>
    </div>
    <video
      class="video-background"
      preload="auto"
      loop
      playsinline
      autoplay
      :src="bg"
      tabindex="-1"
      muted
      :playbackRate="1.0"
    ></video>
  </div>
</template>
<script setup lang="ts">
import bg from '@/assets/homeImg/bg.mp4'
import TaskData from './components/TaskData.vue'
import ServeTotal from './components/ServeTotal.vue'
import CommonTile from './CommonTile.vue'
import DataAssets from './components/DataAssets.vue'
import DataHouse from './components/DataHouse.vue'
import DataLake from './components/DataLake.vue'
import DataStorage from './components/DataStorage.vue'
import HealthLevel from './components/HealthLevel.vue'
import { queryCatalogDataApi, queryDataLakeWarehouseApi } from '@/api/home'
import { ElMessage } from 'element-plus'
import { ref } from 'vue'
import DataStatistics from './components/DataStatistics.vue'

const dataTotal = ref<any>()
const getQueryDataLakeWarehouse = async () => {
  try {
    const { data, message, status } = await queryDataLakeWarehouseApi()
    if ([200].includes(status)) {
      dataTotal.value = data
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}
getQueryDataLakeWarehouse()

const catalogData = ref<any>()
const getQueryCatalogData = async () => {
  try {
    const { data, status, message } = await queryCatalogDataApi()
    if ([200].includes(status)) {
      catalogData.value = data
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}
getQueryCatalogData()

const lakeType = ref<number>(1)
const handleLakeChange = (index: number) => {
  lakeType.value = index
}

const houseType = ref<number>(1)
const handleHouseChange = (index: number) => {
  houseType.value = index
}
</script>
<style scoped lang="less">
.home {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
  font-size: 0;
  background-color: none;
  .home-layout {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    left: 0;
    height: 100%;
    z-index: 2;
    display: flex;
    flex-direction: column;
    padding: 1.0417vw;
    box-sizing: border-box;
  }
  .home-main {
    box-sizing: border-box;
    flex: 1;
    display: flex;
    .main-aside {
      width: 20.8333vw;
      height: 100%;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      .aside-item {
        padding: 1.0417vw;
        box-sizing: border-box;
        flex: 1;
        background-color: rgba(255, 255, 255, 0.5);
        border-radius: 0.3125vw 0.3125vw 0.3125vw 0.3125vw;
        box-shadow: 0px 0.4167vw 0.4167vw 0.4167vw rgba(0, 0, 0, 0.025);
        display: flex;
        flex-direction: column;
      }
      .aside-item + .aside-item {
        margin-top: 0.5208vw;
      }
    }
    .main-content {
      flex: 1;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }
  }
  .video-background {
    position: absolute;
    width: 100%;
    height: 100%;
    /*保证视频充满屏幕*/
    object-fit: cover;
    z-index: 0;
    outline: none !important;
    mix-blend-mode: darken;
    &:focus {
      outline: none !important;
    }
    // left: 1.8229vw;
    // top: 2.0833vw;
    // top: 55%;
    // left: 50%;
    // transform: translate(-50%, -50%);
  }
}

.frequency-label {
  font-size: 0.625vw;
  font-weight: 400;
  color: #656565;
}
.frequency-value {
  font-size: 0.625vw;
  font-weight: 400;
  color: #333333;
  margin-left: 0.2604vw;
}
</style>
