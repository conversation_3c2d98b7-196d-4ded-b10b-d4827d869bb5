<template>
  <ElDialog
    v-model="dialogFormVisible"
    title="详情"
    top="10vh"
    width="550px"
    :close-on-click-modal="false"
    @close="handleDialogClose"
  >
    <ElForm ref="formRef" :model="formData" label-width="80px">
      <ElFormItem label="任务名称" prop="taskName">
        <ElInput readonly v-model="formData.taskName" placeholder="请输入任务名称" />
      </ElFormItem>
      <ElFormItem label="治理类型" prop="ruleType">
        <ElInput readonly v-model="formData.ruleType" placeholder="请输入治理类型" />
      </ElFormItem>
      <ElFormItem label="开始时间" prop="startTime">
        <ElInput readonly v-model="formData.startTime" placeholder="请输入开始时间" />
      </ElFormItem>
      <ElFormItem label="执行时长" prop="executeTime">
        <ElInput readonly v-model="formData.executeTime" placeholder="请输入执行时长" />
      </ElFormItem>
      <ElFormItem label="执行状态" prop="taskStatusName">
        <ElInput readonly v-model="formData.taskStatusName" placeholder="请输入执行状态" />
      </ElFormItem>
    </ElForm>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="setDialogFormVisible(false)">取消</el-button>
        <el-button type="primary" @click="setDialogFormVisible(false)">确定</el-button>
      </span>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
import { ElDialog, ElForm, ElFormItem, ElInput } from 'element-plus'
import useDialogForm from '@/hooks/useDialogForm'
import { ref } from 'vue'

const { dialogFormVisible, formRef, setDialogFormVisible, handleDialogClose } = useDialogForm()

const formData = ref<any>({})

const handleOpen = (row: any) => {
  formData.value = row
  setDialogFormVisible(true)
}

defineExpose({ handleOpen })
</script>

<style scoped></style>
