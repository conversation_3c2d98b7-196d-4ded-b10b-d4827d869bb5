<template>
  <div class="health-level">
    <div class="leftTop text">
      <p>质检规则数</p>
      <p>
        <span>{{ qaData.ruleCount }}</span
        >个
      </p>
    </div>
    <div class="rightTop text">
      <p>质检模型数</p>
      <p>
        <span>{{ qaData.modelCount }}</span
        >个
      </p>
    </div>
    <div class="leftBottom text">
      <p>质检文件数</p>
      <p>
        <span>{{ qaData.countTotal }}</span
        >个
      </p>
    </div>
    <div class="rightBottom text">
      <p>质检表数量</p>
      <p>
        <span>{{ qaData.tableCountTotal }}</span
        >个
      </p>
    </div>
    <Echarts v-if="qualityScore" :options="qualityScore" />
  </div>
</template>
<script lang="ts" setup>
import { queryResultApi } from '@/api/data_governance'
import { ElMessage } from 'element-plus'
import { ref } from 'vue'

// 质量平均得分
const qualityScore = ref<any>()
const getQualityScore = (avgScore: number) => {
  return {
    series: [
      {
        radius: '80%',
        type: 'gauge',
        axisLine: {
          lineStyle: {
            width: 10,
            color: [
              [0.25, '#ffd91a'],
              [0.5, '#c8cf34'],
              [0.75, '#87b342'],
              [1, '#3fa247'],
            ],
          },
        },
        axisTick: {
          distance: 0,
          length: 5,
          lineStyle: {
            width: 1,
            color: '#8F99A3',
          },
        },
        splitLine: {
          distance: 0,
          length: 6,
          lineStyle: {
            width: 2,
            color: '#D0DCE5',
          },
        },
        axisLabel: {
          color: 'inherit',
          fontSize: 8,
        },
        pointer: {
          itemStyle: {
            color: 'auto',
          },
        },
        title: {
          show: false,
        },
        detail: {
          valueAnimation: true,
          formatter: '{value}',
          color: 'inherit',
          fontSize: 16,
        },
        data: [
          {
            value: avgScore,
          },
        ],
      },
    ],
  }
}

// 质检数
const qaData = ref<any>({})

const getAllData = async () => {
  try {
    const { data, status, message } = await queryResultApi()
    if ([200].includes(status)) {
      const { avgScore, ruleCount, modelCount, countTotal, tableCountTotal } = data
      qualityScore.value = getQualityScore(avgScore)
      qaData.value = { ruleCount, modelCount, countTotal, tableCountTotal }
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}

getAllData()
</script>
<style scoped lang="less">
.health-level {
  flex: 1;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  .text {
    position: absolute;
    font-size: 0.7292vw;
    text-align: center;
    line-height: 1.0417vw;
    span {
      color: var(--el-color-primary);
    }
  }
  .leftTop {
    left: 0;
    top: 0.5208vw;
  }
  .rightTop {
    right: 0;
    top: 0.5208vw;
  }
  .leftBottom {
    left: 0;
    bottom: 0;
  }
  .rightBottom {
    right: 0;
    bottom: 0;
  }
}
</style>
