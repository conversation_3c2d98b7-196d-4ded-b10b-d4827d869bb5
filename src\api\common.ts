import request, { baseUrl } from '@/utils/request'

// 查询-平台编码
export const queryPlatformCodeApi = () => request.get(`/platform/queryPlatformCode`)

// 查询-文件类型（树）
export const queryFiletypeTreeApi = () => request.get(`/filetype/queryFileTypeTree`)

// 上传接口
export const uploadFileApi = () => `${baseUrl}/fileTransfer/uploadFile`

//下载文件
export const downloadFileApi = () => `${baseUrl}/fileTransfer/downloadFile`

// 批量打包下载文件
export const downloadBatchFileApi = () => `${baseUrl}/fileTransfer/downloadBatchFile`

// 上传缩略图
export const uploadThumbnailApi = () => `${baseUrl}/dateCenter/uploadThumbnail`

// 时空中台字典列表
export const dictDropDwonApi = (data: any) => request.get(`/dateCenter/dictDropDwon`, data)

// 查询字典值列表
export const dictDataListApi = (data: any) => request.get(`/dictData/list`, data)

// 获取系统logo
export const getLogoApi = (data: any) => request.get(`/dateCenter/getLogo`, data)
