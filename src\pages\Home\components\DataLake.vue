<template>
  <div class="data-lake">
    <Echarts v-if="options" :options="options" />
  </div>
</template>
<script lang="ts" setup>
import * as echarts from 'echarts'
import { EChartsOption } from 'echarts'
import Echarts from '@/components/Echarts/index.vue'
import { ref, watch } from 'vue'
const props = defineProps<{ catalogData: any; type: number }>()

const options = ref<EChartsOption>()

const getMaxNumber = (data: any) => {
  const arr: number[] = data.map((item: any) => Number(item.size))
  const max = Math.max(...arr) > 0 ? Math.max(...arr) : 1
  arr.fill(max)
  return arr
}

const getOptions = () => {
  let data: any[] = []
  props.type === 1
    ? (data = props.catalogData.catalogLakeCountList)
    : (data = props.catalogData.catalogLakeSizeList)
  options.value = {
    title: {
      show: false,
    },
    tooltip: {
      trigger: 'item',
      show: false,
    },
    grid: {
      borderWidth: 0,
      top: 10,
      left: 10,
      right: 10,
      bottom: 0,
    },
    yAxis: [
      {
        type: 'category',
        inverse: true,
        axisTick: {
          show: false,
        },
        axisLine: {
          show: false,
        },
        axisLabel: {
          show: false,
          inside: false,
        },
        data: data.map((item: any) => item.catalogAlias),
      },
      {
        type: 'category',
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          show: true,
          inside: false,
          verticalAlign: 'bottom',
          lineHeight: 0,
          formatter: function (val: any) {
            return `${val}`
          },
          fontSize: 12,
        },
        splitArea: {
          show: false,
        },
        splitLine: {
          show: false,
        },
        // data: props.rasterData.map((item: any) => item.data).reverse()
      },
    ],
    xAxis: {
      type: 'value',
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
      splitLine: {
        show: false,
      },
      axisLabel: {
        show: false,
      },
    },
    series: [
      {
        name: 'total',
        type: 'bar',
        zlevel: 1,
        barGap: '-100%',
        barWidth: 8,
        data: getMaxNumber(data),
        legendHoverLink: false,
        itemStyle: {
          color: '#E8EDF4',
          borderRadius: 10,
        },
        emphasis: {
          itemStyle: {
            color: '#E8EDF4',
          },
        },
      },
      {
        name: 'bar',
        type: 'bar',
        zlevel: 2,
        barWidth: 8,
        data: data.map((item: any) => ({ value: item.size, ...item })),
        itemStyle: {
          color: new echarts.graphic.LinearGradient(
            0,
            0,
            1,
            0,
            [
              {
                offset: 0,
                color: 'rgba(89, 139, 253, 1)',
              },
              {
                offset: 1,
                color: 'rgba(123, 219, 255, 1)',
              },
            ],
            false,
          ),
          borderRadius: 10,
        },
        label: {
          show: true,
          position: [0, -12],
          formatter: function (a: any) {
            // const str = `{color|${a.name}} (${a.data.unit})`
            const str = `{color|${a.name}} (条)`
            return str
          },
          fontSize: 11,
          rich: {
            color: {
              color: '#656565',
              fontSize: 11,
            },
          },
        },
      },
    ],
  }
}

watch(
  () => props.type,
  () => {
    getOptions()
  },
)

getOptions()
</script>
<style scoped lang="less">
.data-lake {
  flex: 1;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}
</style>
