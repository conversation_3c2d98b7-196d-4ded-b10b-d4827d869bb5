import request from '@/utils/request'

// 查询-分页(获取数据安全列表)
export const listApi = (data: any) => request.post(`/datasafe/list`, data)

// 新增-任务备份
export const addOneApi = (data: any) => request.post(`/datasafe/addOne`, data)

// 启动-任务备份
export const startTaskApi = (taskId: string) => request.get(`/datasafe/startTask/${taskId}`)

// 删除-任务id(根据任务id删除)
export const removeByIdApi = (taskId: string) => request.get(`/datasafe/removeById/${taskId}`)

// 查询-列表(数据源)
export const listDatasourceApi = () => request.get(`/datasafe/listDatasource`)

// 回调-更新任务状态(脚本备份回调)
export const startRecoveryTaskApi = (taskId: string) =>
  request.get(`/datasafe/startRecoveryTask/${taskId}`)

// 获取数据恢复状态
export const recoveryFinishApi = () => request.get(`/datasafe/recoveryFinish`)

// 新增-用户文件加密（批量）
export const addBatchApi = (data: any) => request.post(`/userFileEncrypt/addBatch`, data)

// 删除-用户文件加密（批量）
export const removeByUserFileIdsApi = (data: any) =>
  request.post(`/userFileEncrypt/removeByUserFileIds`, data)

//查询-用户文件加密（主键）
export const queryByUserFileIdApi = (userFileId: string) =>
  request.get(`/userFileEncrypt/queryByUserFileId/${userFileId}`)
