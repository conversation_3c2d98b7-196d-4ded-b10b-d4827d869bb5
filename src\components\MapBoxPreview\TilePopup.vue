<template>
  <div class="tile-popup" :style="popupStyle">
    <!-- 关闭按钮 -->
    <div class="close-btn" @click="handleClose">×</div>

    <!-- 信息区域 -->
    <!-- <div class="tile-info">
      <div class="level-info">相机级别: {{ cameraZoom }}，瓦片级别: {{ zoom }}</div>
      <div class="coords-info">瓦片坐标: X={{ coords[0] }}, Y={{ coords[1] }}, Z={{ zoom }}</div>
    </div> -->

    <!-- URL区域 -->
    <div v-if="tileUrl" class="url-container">
      <div class="url-label">URL (点击复制):</div>
      <div class="url-content" @click="copyUrl">{{ tileUrl }}</div>
    </div>

    <!-- 图片预览 -->
    <div v-if="tileUrl" class="image-container">
      <div v-if="isLoading" class="loading-indicator">加载瓦片中...</div>
      <div v-if="loadError" class="error-message">加载失败，级别:{{ zoom }}</div>
      <el-image
        v-show="!isLoading && !loadError"
        :src="tileUrl"
        class="tile-image"
        @load="handleImageLoaded"
        @error="handleImageError"
        :preview-src-list="previewImages"
        :initial-index="0"
        fit="contain"
        :z-index="2000"
        preview-teleported
      >
        <template #preview="{ close }">
          <div class="custom-preview">
            <img :src="tileUrl" class="preview-image" />
            <div class="preview-close" @click="close">关闭</div>
          </div>
        </template>
      </el-image>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage, ElImage } from 'element-plus'

const props = defineProps<{
  coords: [number, number]
  zoom: number
  tileUrl?: string
  cameraZoom: number
  style?: Record<string, string> // 添加样式属性
}>()

const emit = defineEmits<{
  (e: 'close'): void
}>()

// 计算弹窗样式
const popupStyle = computed(() => {
  // 合并从父组件传入的样式和基础样式
  return {
    ...props.style,
  }
})

// 图片加载状态
const isLoading = ref(true)
const loadError = ref(false)

// 用于预览的图片列表
const previewImages = computed(() => {
  return props.tileUrl ? [props.tileUrl] : []
})

// 处理图片加载成功
const handleImageLoaded = () => {
  isLoading.value = false
  loadError.value = false
}

// 处理图片加载失败
const handleImageError = () => {
  isLoading.value = false
  loadError.value = true
}

// 关闭弹窗
const handleClose = () => {
  emit('close')
}

// 复制URL到剪贴板
const copyUrl = () => {
  if (!props.tileUrl) return

  navigator.clipboard
    .writeText(props.tileUrl)
    .then(() => {
      ElMessage.success('URL已复制到剪贴板')
    })
    .catch((err) => {
      console.error('复制失败:', err)
      ElMessage.error('复制失败')
    })
}
</script>

<style scoped lang="less">
.tile-popup {
  position: absolute;
  max-width: 400px;
  padding: 12px;
  background-color: white;
  color: #000;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 10px;
  transform: translate(-41%, -87%);

  &::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-top: 10px solid white;
  }

  .close-btn {
    position: absolute;
    top: 8px;
    right: 10px;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-color: #f0f0f0;
    color: #666;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 18px;
    line-height: 1;
    transition: all 0.2s;

    &:hover {
      background-color: #e0e0e0;
      color: #333;
    }
  }

  .tile-info {
    display: flex;
    flex-direction: column;
    gap: 5px;
    margin-top: 5px;

    .level-info {
      font-weight: bold;
      font-size: 14px;
    }

    .coords-info {
      font-size: 14px;
    }
  }

  .url-container {
    margin: 5px 0;

    .url-label {
      font-weight: bold;
      font-size: 14px;
      margin-bottom: 3px;
    }

    .url-content {
      word-break: break-all;
      background-color: #f5f5f5;
      padding: 8px;
      border-radius: 4px;
      border: 1px solid #ddd;
      font-family: monospace;
      font-size: 12px;
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        background-color: #e6f7ff;
        border-color: #91d5ff;
      }
    }
  }

  .image-container {
    position: relative;
    width: 100%;
    height: 220px;
    background-color: #e0e0e0;
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;

    .loading-indicator,
    .error-message {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: #666;
      font-size: 14px;
    }

    .error-message {
      color: #f5222d;
    }

    .tile-image {
      width: 100%;
      height: 100%;
      object-fit: contain;
      cursor: zoom-in; /* 增加视觉提示，表明可点击放大 */
    }
  }
}

/* 添加全局样式，确保预览组件不受scoped限制 */
:global(.el-image-viewer__wrapper) {
  z-index: 3000 !important; /* 确保在其他元素之上 */
}

:global(.el-image-viewer__img) {
  max-height: 90vh !important; /* 图片最大高度为视口高度的90% */
  max-width: 90vw !important; /* 图片最大宽度为视口宽度的90% */
}
</style>
