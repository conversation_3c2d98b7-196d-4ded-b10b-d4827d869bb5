<template>
  <el-form ref="ruleFormRef" :model="ruleForm">
    <div class="search-content">
      <div class="search-input">
        <div class="search-item">
          <el-form-item prop="scoreName">
            <el-input
              class="form-item"
              v-model="ruleForm.scoreName"
              placeholder="请输入标准名称"
              @keydown.enter="handleSearch"
            >
              <template #suffix>
                <SvgIcon @click="handleSearch" style="cursor: pointer" name="search" />
              </template>
            </el-input>
          </el-form-item>
        </div>
      </div>
      <div v-if="permission.hasButton('scoreCriteria_add')" class="search-btn">
        <el-button type="primary" @click="emit('handleCreate')"> 新增标准 </el-button>
      </div>
    </div>
  </el-form>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import type { FormInstance } from 'element-plus'
import useUserInfo from '@/store/useUserInfo'

const permission = useUserInfo()

const ruleFormRef = ref<FormInstance>()
const ruleForm = ref<any>({})

const handleSearch = () => {
  emit('handleSearch', ruleForm.value)
}

const emit = defineEmits<{
  (e: 'handleSearch', form: any): void
  (e: 'handleCreate'): void
}>()
</script>
