<template>
  <div class="nav-bar">
    <el-scrollbar>
      <el-tree
        :data="dataSource"
        node-key="tid"
        :indent="10"
        :props="props"
        default-expand-all
        highlight-current
        @node-click="handleNodeClick"
        :expand-on-click-node="false"
        :current-node-key="currentNodeKey"
      >
        <template #default="{ node }">
          <span class="custom-tree-node">
            <img :src="fileDir" alt="file" />
            <span>{{ node.label }}</span>
          </span>
        </template>
      </el-tree>
    </el-scrollbar>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import fileDir from '@/assets/fileIconImg/file_dir.png'
import { queryModeltypeTreeListApi } from '@/api/online_model'
import { ElMessage } from 'element-plus'

const props: any = {
  label: 'typeName',
  children: 'childVOList',
}

const handleNodeClick = (data: any) => {
  emit('handleNodeClick', data.tid)
}

const dataSource = ref<any[]>([])
const currentNodeKey = ref<string>('')
const getNodeData = async () => {
  try {
    const { data, status, message } = await queryModeltypeTreeListApi({})
    if ([200].includes(status)) {
      dataSource.value = data
      currentNodeKey.value = data[0].tid
      emit('handleNodeClick', currentNodeKey)
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}
getNodeData()

const emit = defineEmits<{ handleNodeClick: [data: any] }>()
</script>

<style lang="less" scoped>
.nav-bar {
  width: 170px;
  height: 100%;
  background-color: @withe;
  box-shadow: 0 8px 8px 8px rgba(0, 0, 0, 0.05);
  padding: 20px 10px;
  box-sizing: border-box;
  overflow: auto;
  .custom-tree-node {
    display: flex;
    align-items: center;
    width: auto;
    overflow: hidden;
    img {
      width: 16px;
    }
    span {
      margin-left: 8px;
      flex: 1;
      .ellipseLine();
    }
  }
}
</style>
