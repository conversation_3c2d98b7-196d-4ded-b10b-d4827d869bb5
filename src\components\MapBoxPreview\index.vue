<template>
  <!-- 地图预览组件，用于显示地图数据 -->
  <transition name="fade">
    <div v-if="showViewer" class="common-preview-wrapper">
      <div class="tip-wrapper">
        <div></div>
        <p>{{ getFileNameComplete(rowData) }}</p>
        <div class="tip-right">
          <!-- 底图类型选择器 -->
          <el-select
            size="small"
            style="width: 100px; margin-right: 20px"
            v-model="basemapType"
            placeholder="底图"
            @change="handleChangeMap"
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
          <SvgIcon name="closeBold" @click="handleClose" class="close-icon" />
        </div>
      </div>
      <div class="map-wrapper">
        <!-- 矢量数据绘制按钮组，仅在有绘制数据时显示 -->
        <div v-if="vectorData?.haveDraw" class="btns">
          <ElButton
            @click="handleVectorPreview(true)"
            class="btn"
            type="primary"
            :plain="!isDefaultPreview"
            >预览
          </ElButton>
          <ElButton
            @click="handleVectorPreview(false)"
            class="btn"
            type="primary"
            :plain="isDefaultPreview"
            >专题图
          </ElButton>
        </div>
        <!-- 影像裁剪工具按钮 -->
        <ElIcon
          v-if="['11'].includes(rowData.fileTypePid) && isImageCutShow"
          @click="handleImageCut('imageCut')"
          title="影像裁剪"
          class="scissor"
        >
          <SvgIcon name="Scissor" />
        </ElIcon>
        <!-- 瓦片计数器 -->
        <div class="tile-counter">
          <p>总景数: {{ tileCounter.total }}</p>
          <p>已加载景数: {{ tileCounter.loaded }}</p>
        </div>
        <!-- 地图容器 -->
        <div class="olMap" ref="mapRef"></div>
        <!-- 影像裁剪操作按钮 -->
        <div v-show="imageCutData.isElement" ref="BtnRef">
          <el-button @click="clearedImageCut">取消</el-button>
          <el-button @click="handleDownload" type="primary">下载</el-button>
        </div>
      </div>
      <!-- 瓦片信息弹窗 -->
      <TilePopup
        v-if="highlightedTile && showViewer"
        :coords="highlightedTile.coords"
        :zoom="highlightedTile.zoom"
        :tile-url="highlightedTile.url"
        :camera-zoom="cameraInfo.mapZoom"
        @close="clearHighlightedTile"
        :style="popupStyle"
      />
    </div>
  </transition>
</template>

<script setup lang="ts">
import { nextTick, ref } from 'vue'
import {
  queryVectorDrawStyleApi, // 查询矢量绘制样式API
  userFileListDetailApi, // 用户文件列表详情API
  filePreviewApi, // 文件预览API
  cropAndDownloadApi, // 裁剪并下载API
} from '@/api/data_catalog'
import useMapbox from '@/hooks/useMapbox' // 使用Mapbox地图Hook
import { ElButton, ElIcon, ElMessage } from 'element-plus'
import { downloadFile, getFileNameComplete } from '@/utils/fileUtils' // 文件工具函数
import useUserInfo from '@/store/useUserInfo' // 用户信息Store
import useImageCut from '@/hooks/useImageCut' // 图像裁剪Hook
import mapGrid from './grid'
import TilePopup from './TilePopup.vue'
import axios from 'axios'
import X2JS from 'x2js'

// 定义数据结构接口
interface FileData {
  tid: string
  fileTypeId: string
  fileTypePid: string
  gisPreviewVO: {
    bbox: string
    maxZoom: number
    minZoom: number
    mapBoxUrl: string
  }
  metadataVO: {
    srid: string
  }
  [key: string]: any
}

interface VectorStyleData {
  haveDraw?: boolean
  styleId?: string
  [key: string]: any
}

interface GeoJsonData {
  sources?: Record<string, any>
  layers?: Array<Record<string, any>>
  sprite?: string
  glyphs?: string
  [key: string]: any
}

interface Layer {
  Title: {
    __text: string
  }
  TileMatrixSetLink: Array<{
    TileMatrixSet: string
    TileMatrixSetLimits: {
      TileMatrixLimits: Array<{
        TileMatrix: string
        MinTileRow: string
        MaxTileRow: string
        MinTileCol: string
        MaxTileCol: string
      }>
    }
  }>
}

interface WMTSCapabilities {
  Capabilities: {
    Contents: {
      Layer: Layer[]
    }
  }
}

// 组件属性定义
withDefaults(defineProps<{ isImageCutShow?: boolean }>(), {
  isImageCutShow: true, // 默认显示影像裁剪工具
})

// 控制预览组件的显示隐藏
const showViewer = ref<boolean>(false)
const handleClose = () => {
  vectorData.value = undefined
  isDefaultPreview.value = true
  showViewer.value = false
  clearHighlightedTile()
}

// 初始化地图和相关工具
const { mapRef, initMap, setPreviewLayers, handleChangeMap, map, mapTools, initMapDraw } =
  useMapbox('mercator')
// 初始化影像裁剪功能
const { imageCutData, handleImageCut, addMarker, clearedImageCut, BtnRef } = useImageCut(
  mapTools,
  map,
)

// 瓦片计数器
const tileCounter = ref({
  total: 0,
  loaded: 0,
})

// 相机层级信息
const cameraInfo = ref({
  mapZoom: 0,
  tileZoom: 0,
})

// 高亮瓦片状态
const highlightedTile = ref<{
  id: string
  coords: [number, number]
  zoom: number
  url?: string
  lngLat?: mapboxgl.LngLat
} | null>(null)

// 高亮选中的瓦片
const highlightTile = (e: mapboxgl.MapMouseEvent) => {
  if (!map.value) return

  // 首先清除之前的高亮
  clearHighlightedTile()

  // 获取当前地图实际缩放级别
  const mapZoom = map.value.getZoom()

  // 瓦片级别确定策略
  // 默认使用地图当前级别的整数值（与相机级别同步）
  let tileZoom = Math.floor(mapZoom)

  // 调试信息
  // 更新相机信息
  cameraInfo.value = {
    mapZoom: Number(mapZoom.toFixed(2)),
    tileZoom,
  }

  // 获取点击位置的经纬度坐标
  const lngLat = e.lngLat

  // 计算瓦片坐标 (使用正确的墨卡托投影公式)
  const tileX = Math.floor(((lngLat.lng + 180) / 360) * Math.pow(2, tileZoom))
  const tileY = Math.floor(
    ((1 -
      Math.log(
        Math.tan((lngLat.lat * Math.PI) / 180) + 1 / Math.cos((lngLat.lat * Math.PI) / 180),
      ) /
        Math.PI) /
      2) *
      Math.pow(2, tileZoom),
  )
  const baseUrl = rowData.value?.gisPreviewVO?.mapBoxUrl
  const yTMS = Math.pow(2, tileZoom) - 1 - tileY
  const url = baseUrl
    .replace('{z}', tileZoom.toString())
    .replace('{x}', tileX.toString())
    .replace('{y}', yTMS.toString())

  const tileUrl = `${window.location.origin}${import.meta.env.VITE_BASE_SERVER}${url}`

  // 计算瓦片的经纬度边界
  const west = (tileX * 360) / Math.pow(2, tileZoom) - 180
  const east = ((tileX + 1) * 360) / Math.pow(2, tileZoom) - 180
  const north =
    (Math.atan(Math.sinh(Math.PI * (1 - (2 * tileY) / Math.pow(2, tileZoom)))) * 180) / Math.PI
  const south =
    (Math.atan(Math.sinh(Math.PI * (1 - (2 * (tileY + 1)) / Math.pow(2, tileZoom)))) * 180) /
    Math.PI

  // 添加高亮瓦片的边界线
  map.value.addSource('highlight-tile-source', {
    type: 'geojson',
    data: {
      type: 'Feature',
      properties: {},
      geometry: {
        type: 'Polygon',
        coordinates: [
          [
            [west, north], // 西北角
            [east, north], // 东北角
            [east, south], // 东南角
            [west, south], // 西南角
            [west, north], // 回到西北角闭合
          ],
        ],
      },
    },
  })

  // 添加高亮边界线图层
  map.value.addLayer({
    id: 'highlight-tile-outline',
    type: 'line',
    source: 'highlight-tile-source',
    layout: {},
    paint: {
      'line-color': '#FF0000',
      'line-width': 3,
      'line-opacity': 0.8,
    },
  })

  // 添加高亮填充图层
  map.value.addLayer({
    id: 'highlight-tile-fill',
    type: 'fill',
    source: 'highlight-tile-source',
    layout: {},
    paint: {
      'fill-color': '#FF0000',
      'fill-opacity': 0.2,
    },
  })

  // 设置高亮瓦片信息，使用Vue组件显示
  highlightedTile.value = {
    id: `tile-${tileX}-${tileY}-${tileZoom}`,
    coords: [tileX, tileY],
    zoom: tileZoom,
    url: tileUrl,
    lngLat: e.lngLat, // 存储点击位置的经纬度坐标
  }

  // 点击新位置后立即更新popup样式
  updatePopupPosition()
}

const layers = ref<Layer[]>([])
async function getXMLToJsonLayers() {
  // 获取capabilities文档
  const response = await axios.get(
    `${import.meta.env.VITE_BASE_SERVER}/htc/service/wmts?SERVICE=WMTS&VERSION=1.0.0&REQUEST=GetCapabilities`,
  )

  // 使用x2js解析XML为JSON
  const x2js = new X2JS()
  const jsonData = x2js.xml2js(response.data) as WMTSCapabilities

  // 从JSON中获取图层信息
  layers.value = jsonData.Capabilities.Contents.Layer
}
getXMLToJsonLayers()

// 存储每个层级的瓦片数量
const levelTileCounts = ref<Record<number, number>>({})
// 存储总瓦片数
const totalTileCount = ref<number>(0)

async function calculateTiles(layerTitle: string) {
  tileCounter.value = {
    total: 0,
    loaded: 0,
  }
  try {
    if (!layers.value.length) return
    const targetLayer = layers.value.find((layer: Layer) => layer.Title.__text === layerTitle)
    if (!targetLayer) {
      console.error('未找到目标图层:', layerTitle)
      return 0
    }

    // 找到EPSG:3857的TileMatrixSetLink
    const tileMatrixSetLink = targetLayer.TileMatrixSetLink.find(
      (link) => link.TileMatrixSet === 'EPSG:3857',
    )

    if (!tileMatrixSetLink || !tileMatrixSetLink.TileMatrixSetLimits) {
      console.error('未找到EPSG:3857的TileMatrixSetLimits')
      return 0
    }

    // 计算每个层级的瓦片数
    let total = 0
    const limits = tileMatrixSetLink.TileMatrixSetLimits.TileMatrixLimits

    limits.forEach((limit) => {
      const minRow = parseInt(limit.MinTileRow)
      const maxRow = parseInt(limit.MaxTileRow)
      const minCol = parseInt(limit.MinTileCol)
      const maxCol = parseInt(limit.MaxTileCol)

      const levelTiles = (maxRow - minRow + 1) * (maxCol - minCol + 1)
      total += levelTiles

      // 从 TileMatrix 中提取层级数 (EPSG:3857:n 格式)
      const tileMatrix = parseInt(limit.TileMatrix.split(':').pop() || '0')
      // 存储每个层级的瓦片数量
      levelTileCounts.value[tileMatrix] = levelTiles

      console.log(`Level ${tileMatrix}:`, levelTiles)
    })

    // 存储总瓦片数
    totalTileCount.value = total
    console.log('Total tiles:', total)

    // 初始化瓦片计数器
    tileCounter.value = {
      total: total,
      loaded: 0,
    }

    return total
  } catch (error) {
    console.error('计算瓦片数量时出错:', error)
    return 0
  }
}

// 更新当前层级的已加载瓦片数
const updateLoadedTiles = () => {
  if (!map.value) return

  // 获取当前缩放级别
  const currentZoom = Math.floor(map.value.getZoom())
  if (!currentZoom) return
  // 更新瓦片计数器，使用当前层级的总瓦片数
  const TileCounts = levelTileCounts.value[currentZoom]
  if (TileCounts) {
    tileCounter.value.loaded = TileCounts
  }

  console.log(
    `当前层级 ${currentZoom} - 总瓦片数: ${levelTileCounts.value[currentZoom]}, 已加载: ${tileCounter.value}`,
  )
}

// 清除高亮瓦片
const clearHighlightedTile = () => {
  if (!map.value) return

  // 移除高亮图层
  if (map.value.getLayer('highlight-tile-outline')) {
    map.value.removeLayer('highlight-tile-outline')
  }
  if (map.value.getLayer('highlight-tile-fill')) {
    map.value.removeLayer('highlight-tile-fill')
  }
  // 移除高亮数据源
  if (map.value.getSource('highlight-tile-source')) {
    map.value.removeSource('highlight-tile-source')
  }

  // 清除高亮瓦片信息
  highlightedTile.value = null
}

// 当前预览的文件数据
const rowData = ref<FileData>({} as FileData)
// 打开预览功能
const handleOpenPreview = async (row: FileData, isApi: boolean = true) => {
  try {
    await isVector(row) // 检查是否为矢量数据
    if (!isApi) {
      setPreviewData(row)
      return
    }
    // 获取文件详情数据
    const { data, status, message } = await userFileListDetailApi({ tid: row.tid })
    if ([200].includes(status)) {
      setPreviewData(data)
    } else {
      ElMessage.error(message)
    }
  } catch (error: unknown) {
    console.error('预览失败:', error)
  }
}

// 获取用户权限信息
const permission = useUserInfo()
// 处理裁剪后下载功能
const handleDownload = () => {
  const { gisPreviewVO, metadataVO, tid } = rowData.value
  // 构建下载参数
  const params: string = `?token=${permission.token}&originalScope=${gisPreviewVO.bbox}&srid=${
    metadataVO.srid
  }&userFileId=${tid}&cropScope=${imageCutData.bbox.join(',')}`
  downloadFile(cropAndDownloadApi(params))
  clearedImageCut()
}

// 判断是否是矢量图
const vectorData = ref<VectorStyleData>()
const vectorGeoJsonData = ref<GeoJsonData>()
// 检查文件是否为矢量数据并获取相关信息
const isVector = async (row: FileData) => {
  if (!['22', '23'].includes(row.fileTypeId)) return
  try {
    // 获取矢量绘制样式
    const { data, message, status } = await queryVectorDrawStyleApi({ userFileId: row.tid })
    if ([200].includes(status)) {
      vectorData.value = data
      if (data?.haveDraw) {
        // 如果存在绘制数据，获取GeoJSON数据
        const res = await filePreviewApi({ tid: data.styleId })
        vectorGeoJsonData.value = res
      }
    } else {
      ElMessage.error(message)
    }
  } catch (error: unknown) {
    console.error('获取矢量数据失败:', error)
  }
}
// 控制是否使用默认预览模式
const isDefaultPreview = ref<boolean>(true)
// 处理矢量预览模式切换
const handleVectorPreview = async (val: boolean) => {
  isDefaultPreview.value = val

  if (isDefaultPreview.value) {
    // 默认预览模式
    const { gisPreviewVO, metadataVO } = rowData.value
    const row = {
      bbox: JSON.parse(`[${gisPreviewVO.bbox}]`),
      maxzoom: gisPreviewVO.maxZoom,
      minzoom: gisPreviewVO.minZoom,
      url: gisPreviewVO.mapBoxUrl,
      srid: metadataVO.srid,
    }
    clearVectorLayers()
    setPreviewLayers(row)
    return
  }
  // 专题图预览模式
  if (vectorGeoJsonData.value) {
    setVectorPreviewLayers(vectorGeoJsonData.value)
  }
}

// 设置矢量预览图层
const setVectorPreviewLayers = (data: GeoJsonData) => {
  // 清除矢量配图
  clearVectorLayers()
  if (data?.sources) {
    // 添加数据源
    for (const key in data.sources) {
      if (Object.prototype.hasOwnProperty.call(data.sources, key)) {
        map.value?.addSource(key, data.sources[key])
      }
    }
  }
  if (data?.layers && data.layers.length) {
    // 添加图层
    data.layers.forEach((item) => {
      map.value?.addLayer(item)
    })
  }
}
// 清除矢量图层
const clearVectorLayers = () => {
  const layerId: string[] =
    map.value?.getStyle()?.layers.map((item: { id: string }) => item.id) || []
  const soucres = map.value?.getStyle().sources
  const sourceId: string[] = Object.keys(soucres || {})
  // 不删除的基础图层
  const noDelLayer = ['base-layer', 'cia-layer', 'mapTiff']
  // 不删除的基础数据源
  const noDelSource = ['img-cia', 'base-source', 'mapTiff']
  sourceId.forEach((item: string) => {
    if (!noDelSource.includes(item)) {
      map.value?.removeSource(item)
    }
  })
  layerId.forEach((item: string) => {
    if (!noDelLayer.includes(item)) {
      map.value?.removeLayer(item)
    }
  })
}

export interface GridConfig {
  gridWidth: number
  gridHeight: number
  units: string
  minZoom?: number
  maxZoom?: number
  paint?: Record<string, any>
}

// popup样式的响应式对象，用于在地图移动时更新
const popupStyle = ref({})

// 更新popup位置的函数
const updatePopupPosition = () => {
  if (highlightedTile.value && map.value) {
    try {
      // 确保lngLat存在
      if (highlightedTile.value.lngLat) {
        const point = map.value.project(highlightedTile.value.lngLat)
        // 精确定位popup，确保小三角对准鼠标点击位置
        popupStyle.value = {
          left: `${point.x}px`,
          top: `${point.y}px`,
        }
      }
    } catch (error) {
      console.error('更新popup位置出错:', error)
    }
  }
}

// 设置预览数据
const setPreviewData = (data: FileData) => {
  const { gisPreviewVO, metadataVO } = data
  const row = {
    bbox: JSON.parse(`[${gisPreviewVO.bbox}]`),
    maxzoom: gisPreviewVO.maxZoom,
    minzoom: gisPreviewVO.minZoom,
    url: gisPreviewVO.mapBoxUrl,
    srid: metadataVO.srid,
  }
  rowData.value = data
  showViewer.value = true
  nextTick(() => {
    initMap(
      () => {
        clearHighlightedTile()
        clearVectorLayers()
        setPreviewLayers(row)
        calculateTiles(`DM-${data.fileId}`)

        // 初始化网格图层
        new mapGrid(map.value)

        // 监听地图空闲事件，更新已加载瓦片数
        map.value?.on('idle', updateLoadedTiles)

        initMapDraw((e: { features: Array<any> }) => {
          if (['imageCut'].includes(imageCutData.type)) {
            addMarker(e.features[0])
          }
        })

        // 添加点击事件监听，用于高亮瓦片
        map.value?.on('click', (e: mapboxgl.MapMouseEvent) => {
          // 只在未进行绘制操作时响应点击事件
          if (mapTools.type === '') {
            highlightTile(e)
          }
        })

        // 初始化相机信息
        updateCameraInfo()

        // 监听地图移动事件，更新相机信息
        map.value?.on('moveend', updateCameraInfo)

        // 添加地图移动事件监听，实时更新popup位置
        map.value?.on('move', updatePopupPosition)
      },
      {
        sprite: vectorGeoJsonData.value?.sprite,
        glyphs: vectorGeoJsonData.value?.glyphs,
        preserveDrawingBuffer: true,
      },
    )
  })
}

// 更新相机信息
const updateCameraInfo = () => {
  if (!map.value) return

  const mapZoom = map.value.getZoom()
  let tileZoom = Math.floor(mapZoom)

  // 如果rowData中有预设的瓦片层级信息，且用户选择了强制使用预设级别
  if (rowData.value?.gisPreviewVO?.maxZoom) {
    const maxZoom = Number(rowData.value.gisPreviewVO.maxZoom)
    if (!isNaN(maxZoom)) {
      tileZoom = maxZoom
    }
  }

  cameraInfo.value = {
    mapZoom: Number(mapZoom.toFixed(2)),
    tileZoom,
  }
}

// 底图类型
const basemapType = ref<string>('img')
// 底图选项
const options: Array<{ value: string; label: string }> = [
  { value: 'img', label: '星图影像' },
  { value: 'vec', label: '星图矢量' },
]

// 暴露组件方法
defineExpose({ handleOpenPreview })
</script>

<style scoped lang="less">
.common-preview-wrapper {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;

  .tip-wrapper {
    background: rgba(0, 0, 0, 0.5);
    padding: 0 48px;
    width: 100%;
    height: 48px;
    line-height: 48px;
    color: @withe;
    font-size: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-sizing: border-box;

    .tip-right {
      display: flex;
      height: 100%;
      align-items: center;

      .close-icon {
        cursor: pointer;
      }
    }
  }

  .map-wrapper {
    flex: 1;
    box-sizing: border-box;
    padding: 30px 40px;
    position: relative;
    overflow: hidden;

    .olMap {
      height: 100%;
      width: 100%;
      box-sizing: border-box;
      background-color: rgba(0, 0, 0, 0.9);
    }

    .btns {
      position: absolute;
      right: 55px;
      top: 50px;
      z-index: 1000;

      .btn {
        width: 75px;
        display: block;
      }

      .btn + .btn {
        margin-top: 20px;
        margin-left: 0;
      }
    }

    .scissor {
      position: absolute;
      right: 10px;
      top: 40px;
      z-index: 1000;
      color: @withe;
      font-size: 24px;
      cursor: pointer;
    }

    .tile-counter {
      position: absolute;
      left: 10px;
      top: 10px;
      z-index: 1000;
      background-color: rgba(0, 0, 0, 0.6);
      color: white;
      padding: 8px 12px;
      border-radius: 4px;
      font-size: 14px;

      p {
        margin: 0;
        line-height: 1.5;
      }
    }
  }
}

/* 弹窗样式 - 不受scoped影响，确保应用于popup内容 */
:global(.tile-popup-content) {
  padding: 8px;
  max-width: 100%;

  h4 {
    margin: 0 0 8px 0;
    padding-bottom: 5px;
    color: #333;
  }

  p {
    margin: 5px 0;
    color: #555;
  }

  .tile-url-container {
    margin: 10px 0;

    p {
      margin-bottom: 5px;
      font-weight: bold;
    }

    .tile-url {
      background-color: #f5f5f5;
      padding: 8px;
      border-radius: 4px;
      border: 1px solid #ddd;
      word-break: break-all;
      margin-bottom: 5px;
      font-family: monospace;
      font-size: 12px;
      color: #333;
      max-height: 80px;
      overflow-y: auto;
    }

    .copy-btn {
      background-color: #1890ff;
      color: white;
      border: none;
      padding: 4px 8px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 12px;
      transition: background-color 0.3s;

      &:hover {
        background-color: #40a9ff;
      }

      &:active {
        background-color: #096dd9;
      }
    }
  }
}

/* 弹窗本身样式优化 */
:global(.mapboxgl-popup-content) {
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  max-height: 80vh;
  overflow-y: auto;
}

/* 自定义popup样式 */
:global(.custom-popup .mapboxgl-popup-content) {
  padding: 10px;
  max-height: none;
  overflow: visible;
}

:global(.custom-popup) {
  z-index: 1000;
}

:global(.mapboxgl-popup-close-button) {
  font-size: 16px;
  color: #999;
  padding: 5px;
  right: 5px;
  top: 5px;

  &:hover {
    background: none;
    color: #666;
  }
}
</style>
