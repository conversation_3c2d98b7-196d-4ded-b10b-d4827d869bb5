import request from '@/utils/request'

// 分页查询审核列表
export const pageAuditApi = (data: any) => request.post(`/dataImportAudit/pageAudit`, data)

// 审核详情
export const auditDetailApi = (data: any) => request.get(`/dataImportAudit/auditDetail`, data)

// 更新审核状态
export const updateAuditApi = (data: any) => request.post(`/dataImportAudit/updateAudit`, data)

// 白名单分页查询列表
export const whiteListPageApi = (data: any) => request.post(`/dataSourcePass/page`, data)

// 移除白名单
export const whiteListRemoveByIdApi = (data: any) => request.get(`/dataSourcePass/removeById`, data)

// 添加白名单
export const whiteListAddApi = (data: any) => request.post(`/dataSourcePass/add`, data)

// 待添加数据源列表
export const whiteListAddPageApi = (data: any) => request.post(`/dataSourcePass/listAdd`, data)

// 分页查询申请列表
export const pageApplyApi = (data: any) => request.post(`/dataImportAudit/pageApply`, data)

// 更新采集取消审核状态
export const dataImportUpdateAuditApi = (data: any) =>
  request.post(`/dataImportAudit/updateAudit`, data)
