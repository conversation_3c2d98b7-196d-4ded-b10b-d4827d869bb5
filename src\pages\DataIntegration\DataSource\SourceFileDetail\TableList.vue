<template>
  <div class="TableList">
    <div class="header">
      <div class="total">
        <span>{{ props.rowData?.datasourceName }}</span>
      </div>
      <div class="search-form">
        <el-input
          v-model="formData.dataBaseName"
          class="search"
          @keydown.enter="handleSearch"
          placeholder="按文件名前缀搜索"
          @clear="handleSearch"
          clearable
        >
          <template #suffix>
            <SvgIcon style="cursor: pointer" name="search" @click="handleSearch" />
          </template>
        </el-input>
        <el-button type="primary" @click="handleBack">返回</el-button>
      </div>
    </div>
    <div class="infinite-list-wrapper" v-loading="formData.loading">
      <el-scrollbar>
        <div
          class="main"
          v-infinite-scroll="handleScroll"
          :infinite-scroll-disabled="disabled"
          :infinite-scroll-immediate="false"
        >
          <div class="main-item" v-for="p in tableData" :key="p.id" @click="handleFilePath(p)">
            <img :src="p.dir ? fileDir : file" alt="fileDir" />
            <p :title="p.fileName || p.filePath">{{ p.fileName || p.filePath }}</p>
          </div>
        </div>
      </el-scrollbar>
    </div>
  </div>
</template>

<script setup lang="ts">
import fileDir from '@/assets/fileIconImg/file_dir.png'
import file from '@/assets/commonImg/file.png'
import { reactive, ref, watch, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { queryListApi } from '@/api/data_source'
import { ElMessage } from 'element-plus'

const props = defineProps<{ rowData: any }>()

const router = useRouter()
const route = useRoute()

const tableData = ref<any[]>([])

const formData = reactive<any>({
  end: false,
  nextMarker: null,
  dataBaseName: '',
  loading: false,
})

const disabled = computed(() => formData.loading || formData.noMore)

const getListData = async (callback: (data: any) => void) => {
  try {
    formData.loading = true
    const { id, filePath } = route.query
    const { status, data, message } = await queryListApi({
      dataSourceId: id,
      filePath: formData.dataBaseName || filePath,
      pageSize: 200,
      nextMarker: formData.nextMarker,
    })
    if ([200].includes(status)) {
      callback(data)
    } else {
      ElMessage.error(message)
    }
    formData.loading = false
  } catch (e) {
    formData.loading = false
  }
}

watch(
  () => route.query.filePath,
  () => {
    if (route.query.list) {
      formData.dataBaseName = ''
      formData.nextMarker = null
      formData.end = false
      getListData((data: any) => {
        tableData.value = data.list
        formData.nextMarker = data.nextMarker
        formData.end = data.end
      })
    }
  },
  {
    deep: true,
    immediate: true,
  },
)

// 搜索
const handleSearch = () => {
  formData.nextMarker = null
  formData.end = false
  getListData((data: any) => {
    tableData.value = data.list
    formData.nextMarker = data.nextMarker
    formData.end = data.end
  })
}

// 下来刷新
const handleScroll = () => {
  if (formData.end) {
    return
  }
  getListData((data: any) => {
    tableData.value = tableData.value.concat(data.list)
    formData.nextMarker = data.nextMarker
    formData.end = data.end
  })
}

//  文件夹跳转
const handleFilePath = (p: any) => {
  if (p.dir) {
    router.push({ query: { ...route.query, filePath: p.fullPath } })
  }
}

// 返回

const handleBack = () => {
  router.push({ query: { id: route.query.id } })
}
</script>

<style scoped lang="less">
.TableList {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  .header {
    height: 60px;
    box-sizing: border-box;
    border-bottom: 1px solid #eeeeee;
    line-height: 60px;
    padding: 0 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .total {
      span {
        font-size: 16px;
        font-weight: 500;
        color: #333333;
      }
      :nth-child(2) {
        color: var(--el-color-primary);
      }
      span + span {
        margin-left: 20px;
      }
    }
    .search-form {
      display: flex;
      align-items: center;
      .search {
        margin-right: 15px;
      }
    }
  }
  .infinite-list-wrapper {
    overflow: auto;
    flex: 1;
    box-sizing: border-box;
  }
  .main {
    box-sizing: border-box;
    padding: 30px 20px;
    display: grid;
    justify-content: space-evenly;
    -ms-justify-content: space-evenly;
    grid-template-columns: repeat(auto-fill, 50px);
    grid-gap: 20px;
    .main-item {
      width: 50px;
      box-sizing: border-box;
      text-align: center;
      cursor: pointer;
      img {
        width: 45px;
      }
      p {
        font-size: 14px;
        font-weight: 400;
        color: #333333;
        .ellipseLine();
      }
    }
  }
}
</style>
