<template>
  <div class="DataCatalog">
    <div class="leftNav">
      <div class="changeBtns">
        <p @click="handleChange(2)" class="dataHouse" :class="{ active: [2].includes(dataType) }">
          湖目录
        </p>
        <p
          @click="handleChange(1)"
          class="dataLake"
          :class="{ dataLakeActive: [1].includes(dataType) }"
        >
          仓目录
        </p>
      </div>
      <div class="searchTool">
        <el-input v-model="searchText" placeholder="请输入目录名称" clearable style="width: 100%">
          <template #suffix>
            <SvgIcon style="cursor: pointer" name="search" />
          </template>
        </el-input>
      </div>
      <div class="catalog">
        <ElScrollbar>
          <el-tree
            ref="treeRef"
            class="catalog-tree"
            :data="dataSource"
            node-key="tid"
            default-expand-all
            highlight-current
            :expand-on-click-node="false"
            :props="propsTree"
            :filter-node-method="filterNode"
            :current-node-key="defaultCheckedKeys"
            @node-click="hanldeNodeClick"
          >
            <template #default="{ node }">
              <span class="custom-tree-node">
                <template v-if="[1].includes(dataType) && node.data.icon">
                  <img :src="`data:image/jpeg;base64,${node.data.icon}`" alt="" />
                </template>
                <span :title="node.label">{{ node.label }}</span>
              </span>
            </template>
          </el-tree>
        </ElScrollbar>
      </div>
      <div v-if="permission.hasButton('dataCatalog_setCatalog')" class="settings">
        <ElIcon @click="router.push({ name: 'catalogConfig' })" class="icon">
          <SvgIcon name="setting" />
        </ElIcon>
        <span @click="router.push({ name: 'catalogConfig' })">设置</span>
      </div>
    </div>
    <div class="rightContent">
      <slot></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { configQueryListApi } from '@/api/catalog_config'
import useUserInfo from '@/store/useUserInfo'
import { ElIcon, ElMessage, ElScrollbar, ElTree } from 'element-plus'
import { computed, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const permission = useUserInfo()

const router = useRouter()
const route = useRoute()

const propsTree: any = {
  label: 'catalogAlias',
  value: 'tid',
  children: 'childVOList',
}

const dataType = ref<number>(route.name === 'dataLake' ? 2 : 1)
const handleChange = async (value: any) => {
  dataType.value = value
  await router.replace({ name: [2].includes(dataType.value) ? 'dataLake' : 'dataHouse' })
}

const defaultCheckedKeys = computed<any>(() =>
  route.query.dataCatalogId && route.query.dataCatalogId !== '0' ? route.query.dataCatalogId : '0',
)
const hanldeNodeClick = (node: any) => {
  router.push({
    name: [2].includes(dataType.value) ? 'dataLake' : 'dataHouse',
    query: {
      dataCatalogId: node.tid === '0' ? undefined : node.tid,
      bsType: [1].includes(dataType.value) ? node.bsType : undefined,
    },
  })
}

const dataSource = ref<any[]>([])
const getTableData = async (type?: number) => {
  try {
    const { data, status, message } = await configQueryListApi({
      dataType: type || dataType.value,
    })
    if (status === 200) {
      if ([2].includes(type || dataType.value)) {
        dataSource.value = [{ catalogAlias: '全部', tid: '0' }, ...data]
        return
      }
      dataSource.value = data
      if (!route.query.dataCatalogId) {
        router.replace({
          query: { dataCatalogId: dataSource.value[0].tid, bsType: dataSource.value[0].bsType },
        })
      }
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}
getTableData()

const treeRef = ref<InstanceType<typeof ElTree>>()
const searchText = ref<any>('')
watch(searchText, (val: string) => {
  treeRef.value!.filter(val)
})

const filterNode = (value: string, data: any) => {
  if (!value) return true
  return data.catalogAlias.includes(value)
}
</script>

<style lang="less" scoped>
.DataCatalog {
  height: 100%;
  box-sizing: border-box;
  display: flex;
  overflow: hidden;
  .leftNav {
    width: 220px;
    height: 100%;
    box-sizing: border-box;
    box-shadow: 0px 0px 7px 1px rgba(0, 0, 0, 0.05);
    background-color: @withe;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    .btns {
      margin: 10px 10px 0 10px;
      height: 32px;
      background: #e9ebf2;
      border-radius: 2px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      .icon {
        color: var(--el-color-primary);
        font-size: 17px;
      }
      span {
        margin-left: 5px;
        font-size: 14px;
        color: var(--el-color-primary);
      }
      &:hover {
        background-color: var(--el-color-primary);
        .icon {
          color: @withe;
        }
        span {
          color: @withe;
        }
      }
    }
    .changeBtns {
      margin: 10px 10px 0 10px;
      height: 32px;
      background: #e9ebf2;
      border-radius: 2px;
      overflow: hidden;
      display: flex;
      font-size: 14px;
      color: #333333;
      transition: all 0.3s;
      box-shadow: 0 0 7px 1px rgba(0, 0, 0, 0.19);
      .dataHouse {
        position: relative;
        text-align: center;
        line-height: 32px;
        min-width: 35%;
        cursor: pointer;
      }
      .dataLake {
        flex: 1;
        overflow: hidden;
        box-sizing: border-box;
        text-align: center;
        line-height: 32px;
        position: relative;
        cursor: pointer;
      }
      .active {
        width: 65%;
        background-color: var(--el-color-primary);
        color: @withe;
        &::before {
          content: '';
          position: absolute;
          top: 0;
          right: 0;
          width: 0;
          height: 0;
          border-width: 0px 20px 32px 0px;
          border-style: solid;
          border-color: transparent #e9ebf2 transparent transparent;
          z-index: 1;
        }
      }
      .dataLakeActive {
        background-color: var(--el-color-primary);
        color: @withe;
        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          width: 0;
          height: 0;
          border-width: 0px 20px 32px 0px;
          border-style: solid;
          border-color: transparent transparent #e9ebf2 transparent;
          z-index: 1;
        }
      }
    }
    .searchTool {
      margin: 10px 10px 0 10px;
    }
    .catalog {
      margin-top: 10px;
      flex: 1;
      box-sizing: border-box;
      overflow: hidden;
      .catalog-tree {
        padding: 0 10px;
        box-sizing: border-box;
      }
      .custom-tree-node {
        display: flex;
        align-items: center;
        width: auto;
        overflow: hidden;
        img {
          width: 16px;
          margin-right: 8px;
        }
        span {
          flex: 1;
          .ellipseLine();
        }
      }
    }
    .settings {
      padding-left: 10px;
      padding-bottom: 10px;
      display: flex;
      align-items: center;
      .icon {
        color: var(--el-color-primary);
        cursor: pointer;
      }
      span {
        margin-left: 5px;
        font-size: 12px;
        color: #333333;
        cursor: pointer;
      }
    }
  }

  .rightContent {
    flex: 1;
    box-sizing: border-box;
    overflow: hidden;
    margin-left: 1.0417vw;
  }
}
</style>
