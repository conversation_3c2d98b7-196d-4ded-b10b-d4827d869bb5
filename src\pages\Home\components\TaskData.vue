<template>
  <div class="TaskData">
    <div v-for="(item, index) in taskData" :key="index" class="taskItem">
      <div class="left">
        <img :src="index ? collecteTask : manageTask" alt="" />
        <div class="total">
          <p>{{ item.size }}</p>
          <p>{{ item.taskTypeAlias }}</p>
        </div>
      </div>
      <div class="right">
        <div class="rightItem">
          <div class="fail"></div>
          <p>失败</p>
          <p>{{ item.failSize }}</p>
        </div>
        <div class="rightItem">
          <div></div>
          <p>成功</p>
          <p>{{ item.successSize }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import manageTask from '../img/manageTask.webp'
import collecteTask from '../img/collecteTask.webp'
import { ref } from 'vue'
import { queryHomeTaskApi } from '@/api/home'
import { ElMessage } from 'element-plus'

const taskData = ref<any[]>([])

const getTaskData = async () => {
  try {
    const { data, message, status } = await queryHomeTaskApi()
    if (status === 200) {
      taskData.value = data.taskList
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}

getTaskData()
</script>

<style lang="less" scoped>
.TaskData {
  flex: 1;
  box-sizing: border-box;
  overflow: hidden;
  padding: 0.7813vw 0.7813vw 0 0.7813vw;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  .taskItem {
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .left {
      display: flex;
      align-items: center;
      img {
        width: 3.6458vw;
      }
      .total {
        margin-left: 0.7813vw;
        :nth-child(1) {
          font-weight: 400;
          font-size: 28px;
          color: var(--el-color-primary);
        }
        :nth-child(2) {
          margin-top: 0.5208vw;
          font-weight: 400;
          font-size: 14px;
          color: #333333;
        }
      }
    }
    .rightItem {
      display: flex;
      align-items: center;
      > :nth-child(1) {
        width: 1.0417vw;
        height: 0.4167vw;
        background: #00a57b;
      }
      > :nth-child(2) {
        margin: 0 1.0417vw 0 0.5208vw;
        font-size: 14px;
        color: #333333;
      }
      > :nth-child(3) {
        font-weight: 400;
        font-size: 16px;
        color: var(--el-color-primary);
      }
      .fail {
        background: #ff6760;
      }
    }
    .rightItem + .rightItem {
      margin-top: 1.0417vw;
    }
  }
}
</style>
