<template>
  <ul class="topNav">
    <li v-for="p in menuList" :key="p.icon">
      <el-button
        @click="
          (e: any) => {
            handleclick(p.icon, e)
          }
        "
        class="common-icon"
        plain
        link
        :title="p.name"
        :disabled="props.isRunning"
      >
        <template #icon>
          <SvgIcon :name="p.icon" />
        </template>
      </el-button>
    </li>
  </ul>
</template>

<script setup lang="ts">
const props = defineProps<{
  isRunning: boolean
}>()

const menuList = [
  {
    icon: 'onlineModelOpen',
    name: '打开',
  },
  {
    icon: 'onlineModelSave',
    name: '保存',
  },
  {
    icon: 'omlineModelSaveAs',
    name: '另存为',
  },
  {
    icon: 'onlineModelRun',
    name: '运行',
  },
  // {
  //   icon: 'onlineModelSelect',
  //   name: '选择'
  // },
  {
    icon: 'onlineModelConnect',
    name: '连接',
  },
  {
    icon: 'onlineModelAmplify',
    name: '放大',
  },
  {
    icon: 'onlineModelReduce',
    name: '缩小',
  },
  {
    icon: 'onlineModelDel',
    name: '删除',
  },
  {
    icon: 'onlineModelClear',
    name: '清理',
  },
  {
    icon: 'onlineModelFullScreen',
    name: '全屏',
  },
  {
    icon: 'onlineModelSet',
    name: '设置',
  },
]

const handleclick = (name: string, e: any) => {
  emit('handleclick', name, e)
}

const emit = defineEmits<{
  handleclick: [name: string, e: any]
}>()
</script>

<style lang="less" scoped>
.topNav {
  height: 60px;
  width: 100%;
  box-sizing: border-box;
  background-color: @withe;
  box-shadow: 0 8px 8px 8px rgba(0, 0, 0, 0.05);
  padding: 0 30px;
  display: flex;
  align-items: center;
  z-index: 90;
  li {
    font-size: 24px;
    .common-icon {
      font-size: 24px !important;
    }
  }
  li + li {
    margin-left: 20px;
  }
}
</style>
