<template>
  <ElDialog
    v-model="dialogFormVisible"
    title="规则详情"
    top="10vh"
    width="500px"
    :close-on-click-modal="false"
    @close="handleDialogClose"
  >
    <ElScrollbar max-height="50vh" wrap-style="overflow-x: hidden;">
      <ElForm
        ref="formRef"
        :model="formData"
        :rules="rules"
        :disabled="[3].includes(optType)"
        label-width="125px"
      >
        <ElFormItem label="模型名称" prop="modelName">
          <ElInput
            :disabled="[2].includes(optType)"
            v-model="formData.modelName"
            placeholder="请输入模型名称"
          />
        </ElFormItem>
        <ElFormItem label="数据类型" prop="dataType">
          <ElSelect disabled v-model="formData.dataType" placeholder="请选择数据类型">
            <ElOption label="数据仓" :value="1" />
            <ElOption label="数据湖" :value="2" />
          </ElSelect>
        </ElFormItem>
        <ElFormItem label="适用数据目录" prop="dataCatalogId">
          <ElTreeSelect
            v-model="formData.dataCatalogId"
            :data="dataSource"
            check-strictly
            :props="{
              label: 'catalogAlias',
              value: 'tid',
              children: 'childVOList',
            }"
            value-key="tid"
            style="width: 100%"
            :disabled="[1].includes(formData.dataType) || [1].includes(formData.isSystem)"
          />
        </ElFormItem>
        <template v-if="[2].includes(formData.dataType)">
          <ElFormItem label="适用数据" prop="fileTypeId">
            <el-cascader
              :disabled="[2].includes(optType)"
              :props="catalogProps"
              :options="globalData.catalogMenuList"
              style="width: 100%"
              v-model="formData.fileTypeId"
              clearable
              placeholder="请选择适用数据"
              @change="handleChangeFileType"
            />
          </ElFormItem>
          <ElFormItem label="模型组成" prop="modelRuleList">
            <ElSelect
              v-model="formData.modelRuleList"
              collapse-tags
              collapse-tags-tooltip
              value-key="qaRuleId"
              multiple
              style="width: 100%"
            >
              <el-option
                v-for="item in options"
                :key="item.qaRuleId"
                :label="item.ruleName"
                :value="item"
              />
            </ElSelect>
          </ElFormItem>
          <ElFormItem label="权重设置">
            <ElFormItem
              v-for="(p, i) in formData.modelRuleList"
              :key="p.tid"
              :label="p.ruleName"
              label-width="auto"
              :prop="'modelRuleList.' + i + '.weight'"
              :rules="[{ required: true, message: '权重设置必填', trigger: 'blur' }]"
            >
              <ElInputNumber
                v-model="p.weight"
                controls-position="right"
                :min="0"
                :max="1"
                :step="0.01"
              />
            </ElFormItem>
          </ElFormItem>
        </template>

        <ElFormItem label="健康度评分标准" prop="qaScoreId">
          <ElSelect v-model="formData.qaScoreId" placeholder="请选择健康度评分标准">
            <ElOption v-for="p in tableData" :key="p.tid" :label="p.scoreName" :value="p.tid" />
          </ElSelect>
        </ElFormItem>
        <ElFormItem label="模型描述" prop="notes">
          <ElInput
            :rows="3"
            type="textarea"
            v-model="formData.notes"
            placeholder="请输入规则详情"
          />
        </ElFormItem>
      </ElForm>
    </ElScrollbar>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="setDialogFormVisible(false)">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </span>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
import {
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElMessage,
  ElInputNumber,
  ElScrollbar,
  ElOption,
  CascaderProps,
} from 'element-plus'
import useDialogForm from '@/hooks/useDialogForm'
import { ref, reactive } from 'vue'
import {
  uqaRuleListApi,
  qaModelUpdateApi,
  qaModelGetByIdApi,
  qaScoreListApi,
  qaModelAddApi,
} from '@/api/data_governance'
import { configQueryListApi } from '@/api/catalog_config'
import useGlobalData from '@/store/useGlobalData'

const catalogProps: CascaderProps = {
  expandTrigger: 'hover',
  value: 'tid',
  label: 'fileTypeName',
  children: 'childVOList',
  emitPath: false,
}
const globalData = useGlobalData()

const dataSource = ref<any[]>([])
const getTableData = async (dataType: number) => {
  try {
    const { data, status, message } = await configQueryListApi({ dataType })
    if (status === 200) {
      dataSource.value = data
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}

const options = ref<any>([])
const getOptions = async (fileTypeId: any) => {
  try {
    const { data, message, status } = await uqaRuleListApi({ fileTypeId })
    if (status === 200) {
      options.value = data.map((item: any) => ({ ...item, qaRuleId: item.tid, tid: undefined }))
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}

const tableData = ref<any[]>([])
const getScoreList = async (form: any = {}) => {
  try {
    const { data, status, message } = await qaScoreListApi(form)
    if ([200].includes(status)) {
      tableData.value = data
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}
getScoreList()

const { dialogFormVisible, formRef, setDialogFormVisible, handleDialogClose, validateForm } =
  useDialogForm()

const handleChangeFileType = (fileTypeId: any) => {
  formData.value.modelRuleList = []
  getOptions(fileTypeId)
}

const formData = ref<any>({})
const rules = reactive<any>({
  modelName: [{ required: true, message: '模型名称必填', trigger: 'blur' }],
  dataCatalogId: [{ required: true, message: '适用数据目录必填', trigger: 'blur' }],
  qaScoreId: [{ required: true, message: '健康度评分标准必填', trigger: 'blur' }],
  fileTypeId: [{ required: true, message: '适用数据必填', trigger: 'blur' }],
  modelRuleList: [{ required: true, message: '模型组成必填', trigger: 'blur' }],
  weight: [{ required: true, message: '权重设置必填', trigger: 'blur' }],
})

const optType = ref<number>(1)

// type = 1 新增 2 编辑 3详情
const handleOpen = async (row: any, type: number) => {
  optType.value = type
  if (type === 1) {
    formData.value = { dataType: 2 }
    await getTableData(2)
    setDialogFormVisible(true)
    return
  }
  try {
    const { data, message, status } = await qaModelGetByIdApi({ tid: row.tid })
    if ([200].includes(status)) {
      getTableData(row.dataType)
      getOptions(data.fileTypeId)
      formData.value = data
      setDialogFormVisible(true)
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}

const handleSubmit = async () => {
  if (optType.value === 3) {
    setDialogFormVisible(false)
    return
  }
  await validateForm()
  console.log(formData.value, 'ssssssss')
  const fraction = (formData.value.modelRuleList as any[]).reduce((pre: number, item: any) => {
    return pre + (item.weight || 0) * 100
  }, 0)
  if (fraction !== 100 && 1 !== formData.value.dataType) {
    ElMessage.warning('权重设置加起来必须等于1')
    return
  }
  const { message, status } = formData.value.tid
    ? await qaModelUpdateApi(formData.value)
    : await qaModelAddApi(formData.value)
  if ([200].includes(status)) {
    ElMessage.success(message)
    emit('handleRefresh')
    setDialogFormVisible(false)
  } else {
    ElMessage.error(message)
  }
}

const emit = defineEmits<{
  (e: 'handleRefresh'): void
}>()
defineExpose({ handleOpen })
</script>

<style scoped lang="less">
:deep(.el-form-item .el-form-item) {
  margin-bottom: 18px;
}
</style>
