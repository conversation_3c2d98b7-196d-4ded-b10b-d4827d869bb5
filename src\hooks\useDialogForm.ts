import { ref } from 'vue'
import { FormInstance } from 'element-plus'

export default function useDialogForm() {
  // 弹框控制变量
  const dialogFormVisible = ref<boolean>(false)
  const setDialogFormVisible = (visible: boolean) => {
    dialogFormVisible.value = visible
  }

  // form表单实例
  const formRef = ref<FormInstance>()

  // 打开弹框的时候重置表单数据
  const handleDialogOpen = () => {
    if (!formRef.value) return
    formRef.value.resetFields()
  }

  // 关闭弹框回调重置表单数据
  const handleDialogClose = () => {
    if (!formRef.value) return
    formRef.value.resetFields()
    setDialogFormVisible(false)
  }

  // 表单校验
  const validateForm = () => {
    return new Promise(async (resolve, reject) => {
      if (!formRef.value) return
      await formRef.value.validate((valid) => {
        if (valid) {
          resolve(valid)
        } else {
          reject()
        }
      })
    })
  }

  return {
    dialogFormVisible,
    setDialogFormVisible,
    formRef,
    handleDialogClose,
    validateForm,
    handleDialogOpen
  }
}
