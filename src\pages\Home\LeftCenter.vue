<template>
  <div class="LeftCenter">
    <Echarts v-if="options" :options="options" />
  </div>
</template>

<script setup lang="ts">
import * as echarts from 'echarts'
import { EChartsOption } from 'echarts'
import Echarts from '@/components/Echarts/index.vue'
import { ref, watchEffect } from 'vue'

const props = defineProps<{ rasterData: any[] }>()

const getMaxNumber = () => {
  const arr: number[] = props.rasterData.map((item: any) => Number(item.realValue))
  const max = Math.max(...arr) > 0 ? Math.max(...arr) : 1
  arr.fill(max)
  return arr
}

const options = ref<EChartsOption>()
watchEffect(() => {
  if (props.rasterData.length) {
    options.value = {
      title: {
        show: false,
      },
      tooltip: {
        trigger: 'item',
        show: false,
      },
      grid: {
        borderWidth: 0,
        top: 10,
        left: 10,
        right: 10,
        bottom: 0,
      },
      yAxis: [
        {
          type: 'category',
          inverse: true,
          axisTick: {
            show: false,
          },
          axisLine: {
            show: false,
          },
          axisLabel: {
            show: false,
            inside: false,
          },
          data: props.rasterData.map((item: any) => item.desc),
        },
        {
          type: 'category',
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            show: true,
            inside: false,
            verticalAlign: 'bottom',
            lineHeight: 0,
            formatter: function (val: any) {
              return `${val}`
            },
            fontSize: 11,
          },
          splitArea: {
            show: false,
          },
          splitLine: {
            show: false,
          },
          // data: props.rasterData.map((item: any) => item.data).reverse()
        },
      ],
      xAxis: {
        type: 'value',
        axisTick: {
          show: false,
        },
        axisLine: {
          show: false,
        },
        splitLine: {
          show: false,
        },
        axisLabel: {
          show: false,
        },
      },
      series: [
        {
          name: 'total',
          type: 'bar',
          zlevel: 1,
          barGap: '-100%',
          barWidth: 7,
          data: getMaxNumber(),
          legendHoverLink: false,
          itemStyle: {
            color: '#E8EDF4',
            borderRadius: 10,
          },
          emphasis: {
            itemStyle: {
              color: '#E8EDF4',
            },
          },
        },
        {
          name: 'bar',
          type: 'bar',
          zlevel: 2,
          barWidth: 7,
          data: props.rasterData.map((item: any) => ({ value: item.realValue, ...item })),
          itemStyle: {
            color: new echarts.graphic.LinearGradient(
              0,
              0,
              1,
              0,
              [
                {
                  offset: 0,
                  color: 'rgba(89, 139, 253, 1)',
                },
                {
                  offset: 1,
                  color: 'rgba(123, 219, 255, 1)',
                },
              ],
              false,
            ),
            borderRadius: 10,
          },
          label: {
            show: true,
            position: [0, -12],
            formatter: function (a: any) {
              const str = `{color|${a.name}} (${a.data.data}${a.data.unit})`
              return str
            },
            fontSize: 10,
            rich: {
              color: {
                color: '#656565',
                fontSize: 10,
              },
            },
          },
        },
      ],
    }
  }
})
</script>

<style scoped lang="less">
.LeftCenter {
  flex: 1;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}
</style>
