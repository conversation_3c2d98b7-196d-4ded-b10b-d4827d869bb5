import { ref } from 'vue'
import { baseUrl } from '@/utils/request'
import useUserInfo from '@/store/useUserInfo'
export default function useFetchStream() {
  const displayedText = ref<any[]>([])
  const permmission = useUserInfo()
  const fetchStream = async (uuid: string) => {
    try {
      const url = `${baseUrl}/sqlEdit/log/${uuid}`
      const response: any = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'text/plain; charset=utf-8',
          token: permmission.token,
        },
      })
      const reader: any = response.body.getReader()
      while (true) {
        const { done, value } = await reader.read()
        if (done) break
        // 解析接收到的数据
        const data = new TextDecoder().decode(value)
        const formattedContent = data.replace(/\n/g, '<br>')
        // 解码流数据并逐步添加到 displayedText
        displayedText.value.push(formattedContent)
      }
      console.log(displayedText.value)
    } catch (error) {}
  }

  const clearDisplayedText = () => {
    displayedText.value = []
  }

  return { displayedText, fetchStream, clearDisplayedText }
}
