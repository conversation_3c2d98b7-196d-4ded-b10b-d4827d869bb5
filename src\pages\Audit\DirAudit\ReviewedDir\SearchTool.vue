<template>
  <el-form ref="ruleFormRef" :model="ruleForm">
    <div class="search-content">
      <div class="search-input">
        <div class="search-item">
          <el-form-item prop="authStatus">
            <el-select
              style="width: 100%"
              @change="handleSearch"
              v-model="ruleForm.authStatus"
              placeholder="请选择审核状态"
              clearable
            >
              <el-option
                v-for="(p, i) in statusOptions"
                :key="i"
                :label="p.dictName"
                :value="p.dictValue"
              />
            </el-select>
          </el-form-item>
        </div>
        <div class="search-item">
          <el-form-item prop="release">
            <el-select
              style="width: 100%"
              @change="handleSearch"
              v-model="ruleForm.release"
              placeholder="请选择发布状态"
              clearable
            >
              <el-option label="未发布" :value="0" />
              <el-option label="已发布" :value="1" />
            </el-select>
          </el-form-item>
        </div>
      </div>
      <div v-if="permission.hasButton('reviewedDir_audit')" class="search-btn">
        <el-button type="primary" @click="emit('handleApply')"> 批量审核 </el-button>
      </div>
    </div>
  </el-form>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'
import type { FormInstance } from 'element-plus'
import useDictData from '@/hooks/useDictData'
import useUserInfo from '@/store/useUserInfo'

const permission = useUserInfo()

const ruleFormRef = ref<FormInstance>()
const ruleForm = reactive<any>({})

const statusOptions = useDictData('IMPORT_AUDIT_STATUS')

const handleSearch = () => {
  emit('handleSearch', ruleForm)
}

const emit = defineEmits<{
  (e: 'handleSearch', form: any): void
  (e: 'handleApply'): void
}>()
</script>
