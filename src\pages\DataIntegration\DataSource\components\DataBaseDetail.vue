<template>
  <div class="DataDetail">
    <div class="header">
      <span v-if="['ELASTICSEARCH'].includes(rowData.adapterCode)">{{
        rowData.datasourceName
      }}</span>
      <template v-else>
        <span>总数据（表/份）</span>
        <span>{{ tableList && tableList.length }}</span>
      </template>
    </div>
    <div class="main">
      <el-form ref="formRef" class="form-item" :model="rowData" label-width="120px">
        <el-form-item label="数据源类型" prop="adapterName">
          <el-input readonly v-model="rowData.adapterName" placeholder="" />
        </el-form-item>
        <el-form-item label="数据源名称" prop="datasourceName">
          <el-input readonly v-model="rowData.datasourceName" placeholder="" />
        </el-form-item>
        <el-form-item label="共享服务器路径" prop="ports">
          <el-input readonly v-model="rowData.hosts" placeholder="" />
        </el-form-item>
        <el-form-item
          v-if="['POSTGRESQL'].includes(rowData.adapterCode)"
          label="数据库"
          prop="dbName"
        >
          <el-input readonly v-model="rowData.dbName" placeholder="" />
        </el-form-item>
        <el-form-item
          v-if="['ELASTICSEARCH'].includes(rowData.adapterCode)"
          label="索引"
          prop="dbName"
        >
          <el-input readonly v-model="rowData.dbName" placeholder="" />
        </el-form-item>
        <el-form-item label="用户名" prop="usernames">
          <el-input readonly v-model="rowData.usernames" placeholder="" />
        </el-form-item>
        <el-form-item label="描述" prop="notes">
          <el-input readonly :rows="6" type="textarea" v-model="rowData.notes" placeholder="" />
        </el-form-item>
        <el-form-item v-if="['ELASTICSEARCH'].includes(rowData.adapterCode)">
          <el-button type="primary" style="width: 100%" @click.stop="handleSee">查看数据</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter, useRoute } from 'vue-router'
defineProps<{ rowData: any; tableList?: any[] }>()

const router = useRouter()
const route = useRoute()

const handleSee = () => {
  router.push({ query: { ...route.query, dbname: 'true' } })
}
</script>

<style scoped lang="less">
.DataDetail {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  .header {
    height: 60px;
    box-sizing: border-box;
    border-bottom: 1px solid #eeeeee;
    line-height: 60px;
    padding-left: 30px;
    span {
      font-size: 16px;
      font-weight: 500;
      color: #333333;
    }
    :nth-child(2) {
      color: var(--el-color-primary);
    }
    span + span {
      margin-left: 20px;
    }
  }
  .main {
    flex: 1;
    overflow-y: auto;
    .no-scrollbar(0);
    box-sizing: border-box;
    padding: 30px 15px;

    .form-item {
      width: 440px;
    }
  }
}
</style>
