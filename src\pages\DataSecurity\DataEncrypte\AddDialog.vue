<template>
  <ElDialog
    v-model="dialogFormVisible"
    top="10vh"
    title="新增加密"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="120px">
      <div class="table-main">
        <SearchTool @handle-search="handleSearch" />
        <div class="main">
          <el-table
            v-loading="pageData.loading"
            header-cell-class-name="common-table-header"
            cell-class-name="common-table-cell"
            ref="multipleTableRef"
            :data="tableData"
            height="100%"
            style="width: 100%"
            row-key="tid"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" reserve-selection width="30" />
            <el-table-column property="fileName" label="文件名称" show-overflow-tooltip>
              <template #default="scope">
                <span class="fileNmae">{{ getFileNameComplete(scope.row) }}</span>
              </template>
            </el-table-column>
            <el-table-column
              property="suffix"
              label="数据格式"
              :width="100"
              show-overflow-tooltip
            />
          </el-table>
        </div>
        <div class="footer">
          <el-pagination
            v-model:currentPage="pageData.page"
            v-model:page-size="pageData.limit"
            :page-sizes="pageData.pageSizes"
            background
            layout="total, sizes, prev, pager, next, jumper"
            :total="pageData.total"
            @size-change="pageData.handleSizeChange"
            @current-change="pageData.handleCurrentChange"
          />
        </div>
      </div>
      <el-form-item label="设置加密密码" prop="password">
        <ElInput
          style="width: 300px"
          type="password"
          autocomplete="off"
          v-model="formData.password"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="setDialogFormVisible(false)">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </span>
    </template>
  </ElDialog>
</template>
<script setup lang="ts">
import useDialogForm from '@/hooks/useDialogForm'
import { ElDialog, ElInput, ElMessage } from 'element-plus'
import { ref } from 'vue'
import SearchTool from './SearchTool.vue'
import { getFileNameComplete } from '@/utils/fileUtils'
import { userFileListApi } from '@/api/data_catalog'
import usePageData from '@/hooks/usePageData'
import { addBatchApi } from '@/api/data_security'

const { dialogFormVisible, setDialogFormVisible, formRef, handleDialogClose, validateForm } =
  useDialogForm()

const params = { encryptIsNull: true, isDir: 0 }
const { tableData, pageData } = usePageData(userFileListApi, false)
pageData.handleSearch(params, 1)

const handleSearch = (data: any) => {
  pageData.handleSearch({ ...params, ...(data || {}) }, 1)
}

const multipleTableRef = ref<any>()
const handleClose = () => {
  multipleTableRef.value!.clearSelection()
  handleDialogClose()
}
const handleSubmit = async () => {
  const userFileIds = selectData.value.map((item: any) => item.tid)
  if (!userFileIds.length) return ElMessage.warning('请选择要加密的文件')
  try {
    await validateForm()
    const { message, status } = await addBatchApi({ userFileIds, ...formData.value })
    if ([200].includes(status)) {
      ElMessage.success(message)
      setDialogFormVisible(false)
      emit('handleRefresh')
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}

const handleOpen = () => {
  setDialogFormVisible(true)
}

const selectData = ref<any[]>([])
const handleSelectionChange = (list: any[]) => {
  selectData.value = list
}

const formData = ref<any>({})
const rules = ref({
  password: [{ required: true, message: '加密密码必填!', trigger: 'blur' }],
})

defineExpose({ handleOpen })

const emit = defineEmits<{
  (e: 'handleRefresh'): void
}>()
</script>
<style lang="less" scoped>
.table-main {
  height: 60vh;
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;
  .main {
    flex: 1;
    overflow: auto;
    box-sizing: border-box;
    margin: 15px 0;
  }
  .footer {
    display: flex;
    justify-content: flex-end;
  }
}
</style>
