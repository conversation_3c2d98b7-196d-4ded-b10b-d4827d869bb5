<template>
  <ul class="data-storage">
    <li class="item">
      <img :src="storage" alt="storage" />
      <el-button class="viewEchartsBtn" type="primary" text size="small"
        >数据资源占用空间趋势图</el-button
      >
      <div class="total">
        <p>
          <span>{{ dataTotal.allSize }}</span
          ><span>{{ dataTotal.allSizeUnit }}</span>
        </p>
        <p>数据存储总量-接入率</p>
      </div>
      <div class="progress">
        <img src="../img/line.webp" alt="line" />
        <div class="progress-total">
          <div class="progress-item">
            <p>
              <span>数据湖</span
              ><span
                >{{ totalStorageDisplay.file.value
                }}<span class="percentage-small">{{
                  totalStorageDisplay.file.percentage
                }}</span></span
              >
            </p>
            <div class="progress-bar">
              <div
                class="progress-bar-item"
                :style="{ width: totalStorageDisplay.file.progress }"
              ></div>
            </div>
          </div>
          <div class="progress-item">
            <p>
              <span>数据仓</span
              ><span
                >{{ totalStorageDisplay.table.value
                }}<span class="percentage-small">{{
                  totalStorageDisplay.table.percentage
                }}</span></span
              >
            </p>
            <div class="progress-bar">
              <div
                class="progress-bar-item blue"
                :style="{ width: totalStorageDisplay.table.progress }"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </li>
    <li class="item">
      <el-button class="viewEchartsBtn" type="primary" text size="small">下载趋势图</el-button>
      <img :src="todayStorage" alt="todayStorage" />
      <div class="total">
        <p>
          <span>{{ dataTotal.intervalAllSize }}</span
          ><span>{{ dataTotal.intervalAllSizeUnit }}</span>
        </p>
        <p>今日新增数据量</p>
      </div>
      <div class="progress">
        <img src="../img/line.webp" alt="line" />
        <div class="progress-total">
          <div class="progress-item">
            <p>
              <span>数据湖</span
              ><span
                >{{ intervalStorageDisplay.file.value
                }}<span class="percentage-small">{{
                  intervalStorageDisplay.file.percentage
                }}</span></span
              >
            </p>
            <div class="progress-bar">
              <div
                class="progress-bar-item"
                :style="{ width: intervalStorageDisplay.file.progress }"
              ></div>
            </div>
          </div>
          <div class="progress-item">
            <p>
              <span>数据仓</span
              ><span
                >{{ intervalStorageDisplay.table.value
                }}<span class="percentage-small">{{
                  intervalStorageDisplay.table.percentage
                }}</span></span
              >
            </p>
            <div class="progress-bar">
              <div
                class="progress-bar-item blue"
                :style="{ width: intervalStorageDisplay.table.progress }"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </li>
  </ul>
</template>
<script lang="ts" setup>
import storage from '../img/storage.webp'
import todayStorage from '../img/todayStorage.webp'
import { computed } from 'vue'

const props = defineProps<{ dataTotal: any }>()

// 单位转换工具函数
const convertToMB = (size: number, unit: string): number => {
  const sizeNum = Number(size) || 0
  const unitMap = { GB: 1024, TB: 1024 * 1024, KB: 1 / 1024, MB: 1 }
  return sizeNum * (unitMap[unit?.toUpperCase() as keyof typeof unitMap] || 1)
}

// 通用百分比计算函数
const calculatePercentage = (
  fileSize: number,
  fileUnit: string,
  tableSize: number,
  tableUnit: string,
) => {
  const fileMB = convertToMB(fileSize, fileUnit)
  const tableMB = convertToMB(tableSize, tableUnit)
  const total = fileMB + tableMB

  if (total === 0) return { file: 0, table: 0 }

  const formatPercent = (percent: number) =>
    percent < 1 && percent > 0 ? percent : Math.round(percent * 10) / 10

  return {
    file: formatPercent((fileMB / total) * 100),
    table: formatPercent((tableMB / total) * 100),
  }
}

// 计算总存储量的百分比
const totalStoragePercentage = computed(() =>
  calculatePercentage(
    props.dataTotal?.allFileSize,
    props.dataTotal?.allFileSizeUnit,
    props.dataTotal?.allTableSize,
    props.dataTotal?.allTableSizeUnit,
  ),
)

// 计算今日新增的百分比
const intervalStoragePercentage = computed(() =>
  calculatePercentage(
    props.dataTotal?.intervalFileSize,
    props.dataTotal?.intervalFileSizeUnit,
    props.dataTotal?.intervalTableSize,
    props.dataTotal?.intervalTableSizeUnit,
  ),
)

// 工具函数：进度条宽度（最小10%）
const getProgressWidth = (percentage: number) => `${Math.min(Math.max(percentage, 10), 100)}%`

// 工具函数：格式化百分比显示
const formatPercentage = (percentage: number) =>
  percentage === 0 ? '(0%)' : percentage < 1 ? '(<1%)' : `(${percentage}%)`

// 创建显示数据的工具函数
const createDisplayData = (
  fileSize: number,
  fileUnit: string,
  tableSize: number,
  tableUnit: string,
  percentages: { file: number; table: number },
) => ({
  file: {
    value: `${fileSize}${fileUnit}`,
    percentage: formatPercentage(percentages.file),
    progress: getProgressWidth(percentages.file),
  },
  table: {
    value: `${tableSize}${tableUnit}`,
    percentage: formatPercentage(percentages.table),
    progress: getProgressWidth(percentages.table),
  },
})

// 计算总存储量的显示数据
const totalStorageDisplay = computed(() =>
  createDisplayData(
    props.dataTotal?.allFileSize,
    props.dataTotal?.allFileSizeUnit,
    props.dataTotal?.allTableSize,
    props.dataTotal?.allTableSizeUnit,
    totalStoragePercentage.value,
  ),
)

// 计算今日新增的显示数据
const intervalStorageDisplay = computed(() =>
  createDisplayData(
    props.dataTotal?.intervalFileSize,
    props.dataTotal?.intervalFileSizeUnit,
    props.dataTotal?.intervalTableSize,
    props.dataTotal?.intervalTableSizeUnit,
    intervalStoragePercentage.value,
  ),
)
</script>
<style scoped lang="less">
.data-storage {
  margin: 0 0.5208vw;
  display: flex;
  box-sizing: border-box;
  .item {
    position: relative;
    height: 7.8125vw;
    flex: 1;
    box-sizing: border-box;
    background-color: rgba(255, 255, 255, 0.5);
    box-shadow: 0px 0px 7px 1px rgba(0, 0, 0, 0.05);
    border-radius: 6px;
    padding: 0.7813vw 1.5625vw;
    overflow: hidden;
    display: flex;
    align-items: center;
    > img {
      width: 4.1667vw;
    }
    .viewEchartsBtn {
      position: absolute;
      font-size: 0.7292vw;
      color: #333333;
      bottom: 0.3vw;
      // margin-top: 0.5vw;
      // margin-left: -2vw;
    }
    > .total {
      margin-left: 1.0417vw;
      text-align: left;
      > p:nth-child(1) {
        color: var(--el-color-primary);
        > :nth-child(1) {
          font-size: 1.875vw;
          font-weight: 700;
        }
        > :nth-child(2) {
          margin-left: 0.2604vw;
          font-size: 0.7292vw;
          font-weight: 700;
        }
      }
      > p:nth-child(2) {
        // margin-top: 0.2604vw;
        font-size: 0.7292vw;
        color: #333333;
      }
    }
    > .progress {
      flex: 1;
      margin-left: 3.125vw;
      display: flex;
      box-sizing: border-box;
      > img {
        height: 5.5208vw;
        padding-top: 0.4167vw;
        box-sizing: border-box;
      }
      .progress-total {
        margin-left: 0.4167vw;
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        box-sizing: border-box;
        .progress-item {
          > p:nth-child(1) {
            display: flex;
            align-items: center;
            > :nth-child(1) {
              font-size: 0.7292vw;
              color: #333333;
            }
            > :nth-child(2) {
              margin-left: 0.7292vw;
              color: #91d7ff;
              font-size: 0.9375vw;
              .percentage-small {
                margin-left: 5px;
                font-size: 0.625vw;
                color: #666666;
              }
            }
          }
          .progress-bar {
            display: flex;
            margin-top: 0.2604vw;
            height: 4px;
            background: rgba(76, 76, 76, 0.53);
            border-radius: 2px;
            .progress-bar-item {
              height: 100%;
              background: #91d7ff;
              border-radius: 2px;
              position: relative;
              transition: width 0.3s ease;
              &::before {
                content: '';
                width: 6px;
                height: 6px;
                position: absolute;
                right: 0;
                top: 50%;
                transform: translateY(-50%);
                background-color: @withe;
                border-radius: 50%;
                border: 1px solid #91d7ff;
              }
            }
            .blue {
              background: var(--el-color-primary);
              &::before {
                border-color: var(--el-color-primary);
              }
            }
          }
        }
      }
    }
  }
  li + li {
    margin-left: 0.5208vw;
  }
}
</style>
