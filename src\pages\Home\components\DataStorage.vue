<template>
  <ul class="data-storage">
    <li class="item">
      <img :src="storage" alt="storage" />
      <el-button
        class="viewEchartsBtn"
        type="primary"
        text
        size="small"
        @click="openStorageTrendDialog"
      >
        数据资源占用空间趋势图
      </el-button>
      <div class="total">
        <p>
          <span>{{ dataTotal.allSize }}</span
          ><span>{{ dataTotal.allSizeUnit }}</span>
        </p>
        <p>数据存储总量-接入率</p>
      </div>
      <div class="progress">
        <img src="../img/line.webp" alt="line" />
        <div class="progress-total">
          <div class="progress-item">
            <p>
              <span>数据湖</span
              ><span
                >{{ totalStorageDisplay.file.value
                }}<span class="percentage-small">{{
                  totalStorageDisplay.file.percentage
                }}</span></span
              >
            </p>
            <div class="progress-bar">
              <div
                class="progress-bar-item"
                :style="{ width: totalStorageDisplay.file.progress }"
              ></div>
            </div>
          </div>
          <div class="progress-item">
            <p>
              <span>数据仓</span
              ><span
                >{{ totalStorageDisplay.table.value
                }}<span class="percentage-small">{{
                  totalStorageDisplay.table.percentage
                }}</span></span
              >
            </p>
            <div class="progress-bar">
              <div
                class="progress-bar-item blue"
                :style="{ width: totalStorageDisplay.table.progress }"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </li>
    <li class="item">
      <el-button
        class="viewEchartsBtn"
        type="primary"
        text
        size="small"
        @click="openDownloadTrendDialog"
      >
        下载趋势图
      </el-button>
      <img :src="todayStorage" alt="todayStorage" />
      <div class="total">
        <p>
          <span>{{ dataTotal.intervalAllSize }}</span
          ><span>{{ dataTotal.intervalAllSizeUnit }}</span>
        </p>
        <p>今日新增数据量</p>
      </div>
      <div class="progress">
        <img src="../img/line.webp" alt="line" />
        <div class="progress-total">
          <div class="progress-item">
            <p>
              <span>数据湖</span
              ><span
                >{{ intervalStorageDisplay.file.value
                }}<span class="percentage-small">{{
                  intervalStorageDisplay.file.percentage
                }}</span></span
              >
            </p>
            <div class="progress-bar">
              <div
                class="progress-bar-item"
                :style="{ width: intervalStorageDisplay.file.progress }"
              ></div>
            </div>
          </div>
          <div class="progress-item">
            <p>
              <span>数据仓</span
              ><span
                >{{ intervalStorageDisplay.table.value
                }}<span class="percentage-small">{{
                  intervalStorageDisplay.table.percentage
                }}</span></span
              >
            </p>
            <div class="progress-bar">
              <div
                class="progress-bar-item blue"
                :style="{ width: intervalStorageDisplay.table.progress }"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </li>
  </ul>

  <!-- 数据资源占用空间趋势图弹窗 -->
  <el-dialog
    v-model="storageTrendDialogVisible"
    title="数据资源占用空间趋势图"
    width="800px"
    :before-close="handleCloseDialog"
    @opened="handleDialogOpened"
  >
    <div
      class="chart-container"
      ref="storageChartContainer"
      v-loading="chartLoading"
      element-loading-text="图表加载中..."
    >
      <Echarts v-if="storageTrendOptions" :options="storageTrendOptions" />
    </div>
  </el-dialog>

  <!-- 下载趋势图弹窗 -->
  <el-dialog
    v-model="downloadTrendDialogVisible"
    title="下载趋势图"
    width="800px"
    :before-close="handleCloseDialog"
    @opened="handleDialogOpened"
  >
    <div
      class="chart-container"
      ref="downloadChartContainer"
      v-loading="chartLoading"
      element-loading-text="图表加载中..."
    >
      <Echarts v-if="downloadTrendOptions" :options="downloadTrendOptions" />
    </div>
  </el-dialog>
</template>
<script lang="ts" setup>
import storage from '../img/storage.webp'
import todayStorage from '../img/todayStorage.webp'
import { computed, onMounted, ref } from 'vue'
import Echarts from '@/components/Echarts/index.vue'
import type { EChartsOption } from 'echarts'

const props = defineProps<{ dataTotal: any }>()

// 弹窗控制
const storageTrendDialogVisible = ref(false)
const downloadTrendDialogVisible = ref(false)
const chartLoading = ref(false)

// 图表容器引用
const storageChartContainer = ref<HTMLElement>()
const downloadChartContainer = ref<HTMLElement>()

// 单位转换工具函数
const convertToMB = (size: number, unit: string): number => {
  const sizeNum = Number(size) || 0
  const unitMap = { GB: 1024, TB: 1024 * 1024, KB: 1 / 1024, MB: 1 }
  return sizeNum * (unitMap[unit?.toUpperCase() as keyof typeof unitMap] || 1)
}

// 通用百分比计算函数
const calculatePercentage = (
  fileSize: number,
  fileUnit: string,
  tableSize: number,
  tableUnit: string,
) => {
  const fileMB = convertToMB(fileSize, fileUnit)
  const tableMB = convertToMB(tableSize, tableUnit)
  const total = fileMB + tableMB

  if (total === 0) return { file: 0, table: 0 }

  const formatPercent = (percent: number) =>
    percent < 1 && percent > 0 ? percent : Math.round(percent * 10) / 10

  return {
    file: formatPercent((fileMB / total) * 100),
    table: formatPercent((tableMB / total) * 100),
  }
}

// 计算总存储量的百分比
const totalStoragePercentage = computed(() =>
  calculatePercentage(
    props.dataTotal?.allFileSize,
    props.dataTotal?.allFileSizeUnit,
    props.dataTotal?.allTableSize,
    props.dataTotal?.allTableSizeUnit,
  ),
)

// 计算今日新增的百分比
const intervalStoragePercentage = computed(() =>
  calculatePercentage(
    props.dataTotal?.intervalFileSize,
    props.dataTotal?.intervalFileSizeUnit,
    props.dataTotal?.intervalTableSize,
    props.dataTotal?.intervalTableSizeUnit,
  ),
)

// 工具函数：进度条宽度（最小10%）
const getProgressWidth = (percentage: number) => `${Math.min(Math.max(percentage, 10), 100)}%`

// 工具函数：格式化百分比显示
const formatPercentage = (percentage: number) =>
  percentage === 0 ? '(0%)' : percentage < 1 ? '(<1%)' : `(${percentage}%)`

// 创建显示数据的工具函数
const createDisplayData = (
  fileSize: number,
  fileUnit: string,
  tableSize: number,
  tableUnit: string,
  percentages: { file: number; table: number },
) => ({
  file: {
    value: `${fileSize}${fileUnit}`,
    percentage: formatPercentage(percentages.file),
    progress: getProgressWidth(percentages.file),
  },
  table: {
    value: `${tableSize}${tableUnit}`,
    percentage: formatPercentage(percentages.table),
    progress: getProgressWidth(percentages.table),
  },
})

// 计算总存储量的显示数据
const totalStorageDisplay = computed(() =>
  createDisplayData(
    props.dataTotal?.allFileSize,
    props.dataTotal?.allFileSizeUnit,
    props.dataTotal?.allTableSize,
    props.dataTotal?.allTableSizeUnit,
    totalStoragePercentage.value,
  ),
)

// 计算今日新增的显示数据
const intervalStorageDisplay = computed(() =>
  createDisplayData(
    props.dataTotal?.intervalFileSize,
    props.dataTotal?.intervalFileSizeUnit,
    props.dataTotal?.intervalTableSize,
    props.dataTotal?.intervalTableSizeUnit,
    intervalStoragePercentage.value,
  ),
)

// 生成近七天的日期
const generateLastSevenDays = () => {
  const days = []
  for (let i = 6; i >= 0; i--) {
    const date = new Date()
    date.setDate(date.getDate() - i)
    days.push(date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' }))
  }
  return days
}

// 生成模拟数据 - 确保总和为100%
const generateMockData = () => {
  return Array.from({ length: 7 }, () => {
    // 数据湖占比在30%-70%之间
    const dataLake = Math.floor(Math.random() * 40) + 30
    // 数据仓占比为剩余部分，确保总和为100%
    const dataWarehouse = 100 - dataLake
    return {
      dataLake,
      dataWarehouse,
    }
  })
}

// 数据资源占用空间趋势图配置
const storageTrendOptions = computed<EChartsOption>(() => {
  const xAxisData = generateLastSevenDays()
  const mockData = generateMockData()

  return {
    title: {
      text: '数据资源占用空间趋势',
      left: 'center',
      textStyle: {
        color: '#333',
        fontSize: 16,
      },
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        let result = `${params[0].axisValue}<br/>`
        params.forEach((item: any) => {
          result += `${item.marker}${item.seriesName}: ${item.value}%<br/>`
        })
        return result
      },
    },
    legend: {
      data: ['数据湖', '数据仓'],
      top: 30,
      textStyle: {
        color: '#666',
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: xAxisData,
      axisLine: {
        lineStyle: {
          color: '#ddd',
        },
      },
      axisLabel: {
        color: '#666',
      },
    },
    yAxis: {
      type: 'value',
      name: '占比(%)',
      nameTextStyle: {
        color: '#666',
      },
      axisLine: {
        lineStyle: {
          color: '#ddd',
        },
      },
      axisLabel: {
        color: '#666',
        formatter: '{value}%',
      },
      splitLine: {
        lineStyle: {
          color: '#f0f0f0',
        },
      },
    },
    series: [
      {
        name: '数据湖',
        type: 'line',
        smooth: true,
        data: mockData.map((item) => item.dataLake),
        lineStyle: {
          color: '#91d7ff',
          width: 2,
        },
        itemStyle: {
          color: '#91d7ff',
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(145, 215, 255, 0.3)' },
              { offset: 1, color: 'rgba(145, 215, 255, 0.1)' },
            ],
          },
        },
      },
      {
        name: '数据仓',
        type: 'line',
        smooth: true,
        data: mockData.map((item) => item.dataWarehouse),
        lineStyle: {
          color: '#409eff',
          width: 2,
        },
        itemStyle: {
          color: '#409eff',
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
              { offset: 1, color: 'rgba(64, 158, 255, 0.1)' },
            ],
          },
        },
      },
    ],
  }
})

// 下载趋势图配置
const downloadTrendOptions = computed<EChartsOption>(() => {
  const xAxisData = generateLastSevenDays()
  const mockData = generateMockData()

  return {
    title: {
      text: '下载趋势图',
      left: 'center',
      textStyle: {
        color: '#333',
        fontSize: 16,
      },
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        let result = `${params[0].axisValue}<br/>`
        params.forEach((item: any) => {
          result += `${item.marker}${item.seriesName}: ${item.value}%<br/>`
        })
        return result
      },
    },
    legend: {
      data: ['数据湖', '数据仓'],
      top: 30,
      textStyle: {
        color: '#666',
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: xAxisData,
      axisLine: {
        lineStyle: {
          color: '#ddd',
        },
      },
      axisLabel: {
        color: '#666',
      },
    },
    yAxis: {
      type: 'value',
      name: '下载量(%)',
      nameTextStyle: {
        color: '#666',
      },
      axisLine: {
        lineStyle: {
          color: '#ddd',
        },
      },
      axisLabel: {
        color: '#666',
        formatter: '{value}%',
      },
      splitLine: {
        lineStyle: {
          color: '#f0f0f0',
        },
      },
    },
    series: [
      {
        name: '数据湖',
        type: 'line',
        smooth: true,
        data: mockData.map((item) => item.dataLake),
        lineStyle: {
          color: '#91d7ff',
          width: 2,
        },
        itemStyle: {
          color: '#91d7ff',
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(145, 215, 255, 0.3)' },
              { offset: 1, color: 'rgba(145, 215, 255, 0.1)' },
            ],
          },
        },
      },
      {
        name: '数据仓',
        type: 'line',
        smooth: true,
        data: mockData.map((item) => item.dataWarehouse),
        lineStyle: {
          color: '#409eff',
          width: 2,
        },
        itemStyle: {
          color: '#409eff',
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
              { offset: 1, color: 'rgba(64, 158, 255, 0.1)' },
            ],
          },
        },
      },
    ],
  }
})

// 弹窗方法
const openStorageTrendDialog = () => {
  storageTrendDialogVisible.value = true
  chartLoading.value = true
}

const openDownloadTrendDialog = () => {
  downloadTrendDialogVisible.value = true
  chartLoading.value = true
}

// 弹窗打开后的处理
const handleDialogOpened = () => {
  // 短暂延迟后停止loading，让图表正常显示
  chartLoading.value = false
  // setTimeout(() => {

  //   // 确保图表正确渲染
  //   setTimeout(() => {
  //     window.dispatchEvent(new Event('resize'))
  //   }, 50)
  // }, 300)
}

const handleCloseDialog = () => {
  storageTrendDialogVisible.value = false
  downloadTrendDialogVisible.value = false
  chartLoading.value = false
}

// 组件挂载时的处理
onMounted(() => {
  // 可以在这里做一些初始化工作
})
</script>
<style scoped lang="less">
.data-storage {
  margin: 0 0.5208vw;
  display: flex;
  box-sizing: border-box;
  .item {
    position: relative;
    height: 7.8125vw;
    flex: 1;
    box-sizing: border-box;
    background-color: rgba(255, 255, 255, 0.5);
    box-shadow: 0px 0px 7px 1px rgba(0, 0, 0, 0.05);
    border-radius: 6px;
    padding: 0.7813vw 1.5625vw;
    overflow: hidden;
    display: flex;
    align-items: center;
    > img {
      width: 4.1667vw;
    }
    .viewEchartsBtn {
      position: absolute;
      font-size: 0.7292vw;
      color: var(--el-color-primary) !important;
      bottom: 0.3vw;
      border: none !important;
      background: transparent !important;
      padding: 0 !important;
      height: auto !important;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        color: var(--el-color-primary-light-3) !important;
        transform: translateY(-1px);
      }

      &:active {
        transform: translateY(0);
      }
    }
    > .total {
      margin-left: 1.0417vw;
      text-align: left;
      > p:nth-child(1) {
        color: var(--el-color-primary);
        > :nth-child(1) {
          font-size: 1.875vw;
          font-weight: 700;
        }
        > :nth-child(2) {
          margin-left: 0.2604vw;
          font-size: 0.7292vw;
          font-weight: 700;
        }
      }
      > p:nth-child(2) {
        // margin-top: 0.2604vw;
        font-size: 0.7292vw;
        color: #333333;
      }
    }
    > .progress {
      flex: 1;
      margin-left: 3.125vw;
      display: flex;
      box-sizing: border-box;
      > img {
        height: 5.5208vw;
        padding-top: 0.4167vw;
        box-sizing: border-box;
      }
      .progress-total {
        margin-left: 0.4167vw;
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        box-sizing: border-box;
        .progress-item {
          > p:nth-child(1) {
            display: flex;
            align-items: center;
            > :nth-child(1) {
              font-size: 0.7292vw;
              color: #333333;
            }
            > :nth-child(2) {
              margin-left: 0.7292vw;
              color: #91d7ff;
              font-size: 0.9375vw;
              .percentage-small {
                margin-left: 5px;
                font-size: 0.625vw;
                color: #666666;
              }
            }
          }
          .progress-bar {
            display: flex;
            margin-top: 0.2604vw;
            height: 4px;
            background: rgba(76, 76, 76, 0.53);
            border-radius: 2px;
            .progress-bar-item {
              height: 100%;
              background: #91d7ff;
              border-radius: 2px;
              position: relative;
              transition: width 0.3s ease;
              &::before {
                content: '';
                width: 6px;
                height: 6px;
                position: absolute;
                right: 0;
                top: 50%;
                transform: translateY(-50%);
                background-color: @withe;
                border-radius: 50%;
                border: 1px solid #91d7ff;
              }
            }
            .blue {
              background: var(--el-color-primary);
              &::before {
                border-color: var(--el-color-primary);
              }
            }
          }
        }
      }
    }
  }
  li + li {
    margin-left: 0.5208vw;
  }
}

// 弹窗样式
.chart-container {
  height: 450px;
  width: 100%;
  min-height: 400px;
  position: relative;
  box-sizing: border-box;
}

:deep(.el-dialog) {
  .el-dialog__header {
    padding: 20px 20px 10px;
    border-bottom: 1px solid #ebeef5;

    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }
  }

  .el-dialog__body {
    padding: 20px;
    height: auto;
    min-height: 450px;
  }

  .el-dialog__wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
