<template>
  <MainLayout>
    <template #header>
      <SearchTool
        @handleSearch="getTableData"
        @handle-create="EditDetailDialogRef.handleOpen(null, true)"
      />
    </template>
    <el-table
      header-cell-class-name="common-table-header"
      cell-class-name="common-table-cell"
      ref="multipleTableRef"
      :data="tableData"
      height="100%"
      style="width: 100%"
      row-key="tid"
    >
      <el-table-column
        property="scoreName"
        width="200px"
        label="健康度评分标准名称"
        show-overflow-tooltip
      />
      <el-table-column property="createTime" label="健康度评分标准" show-overflow-tooltip>
        <template #default="scope">
          {{
            `总体评分大于等于${scope.row.excellentMin}分为优秀，大于等于${scope.row.goodMin}分小于${scope.row.excellentMin}分为良好，分值大于等于${scope.row.mediumMin}分小于${scope.row.goodMin}分为一般，小于${scope.row.mediumMin}分不合格`
          }}
        </template>
      </el-table-column>
      <el-table-column
        v-if="
          permission.hasButton(['scoreCriteria_details', 'scoreCriteria_edit', 'scoreCriteria_del'])
        "
        label="操作"
        width="180"
        fixed="right"
      >
        <template #default="scope">
          <el-button
            v-if="permission.hasButton('scoreCriteria_edit')"
            @click="EditDetailDialogRef.handleOpen(scope.row, true)"
            class="common-icon-btn"
            type="primary"
            text
            link
            title="编辑"
          >
            <template #icon>
              <SvgIcon name="edit" />
            </template>
          </el-button>
          <el-button
            v-if="permission.hasButton('scoreCriteria_details')"
            @click="EditDetailDialogRef.handleOpen(scope.row, false)"
            title="详情"
            class="common-icon-btn"
            type="primary"
            text
            link
          >
            <template #icon>
              <SvgIcon name="details" />
            </template>
          </el-button>
          <el-button
            v-if="permission.hasButton('scoreCriteria_del')"
            @click="handleDel(scope.row.tid)"
            title="删除"
            class="common-icon-btn"
            type="danger"
            plain
            link
          >
            <template #icon>
              <SvgIcon name="delete" />
            </template>
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <EditDetailDialog ref="EditDetailDialogRef" @handle-refresh="getTableData" />
  </MainLayout>
</template>

<script setup lang="ts">
import MainLayout from '@/components/MainLayout/index.vue'
import SearchTool from './SearchTool.vue'
import { qaScoreListApi, qaScoreRemoveByIdApi } from '@/api/data_governance'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ref } from 'vue'
import EditDetailDialog from './EditDetailDialog.vue'
import useUserInfo from '@/store/useUserInfo'

const permission = useUserInfo()

const tableData = ref<any[]>([])

const EditDetailDialogRef = ref<any>()

const getTableData = async (form: any = {}) => {
  try {
    const { data, status, message } = await qaScoreListApi(form)
    if ([200].includes(status)) {
      tableData.value = data
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}

getTableData()

// 删除
const handleDel = (tid: string) => {
  ElMessageBox.confirm('确认要删除该数据嘛？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    try {
      const { status, message } = await qaScoreRemoveByIdApi({ tid })
      if ([200].includes(status)) {
        ElMessage.success(message)
        getTableData()
      } else {
        ElMessage.error(message)
      }
    } catch (error) {
      console.error(error)
    }
  })
}
</script>

<style scoped></style>
