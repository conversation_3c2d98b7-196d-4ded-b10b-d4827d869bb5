<template>
  <div class="LeftTop">
    <ul class="list">
      <li v-for="(p, i) in data" :key="i">
        <SvgIcon class="img" :name="p.code" />
        <div class="text">
          <span>{{ p.data }}</span>
          <p>{{ p.desc }}/{{ p.unit }}</p>
        </div>
      </li>
    </ul>
  </div>
</template>

<script setup lang="ts">
defineProps<{ data: any[] }>()
</script>

<style scoped lang="less">
.LeftTop {
  flex: 1;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  .list {
    width: 100%;
    box-sizing: border-box;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    li {
      width: 50%;
      display: flex;
      align-items: center;
      box-sizing: border-box;
      .img {
        width: 2.0833vw;
        height: 2.0833vw;
      }
      .text {
        margin-left: 0.7813vw;
        span {
          color: var(--el-color-primary);
          font-size: 1.0417vw;
        }
        p {
          font-size: 0.625vw;
          color: #656565;
        }
      }
    }
    li + li {
      margin-top: 0.7813vw;
    }
  }
}
</style>
