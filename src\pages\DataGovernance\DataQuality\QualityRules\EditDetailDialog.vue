<template>
  <ElDialog
    v-model="dialogFormVisible"
    title="规则详情"
    top="10vh"
    width="850px"
    :close-on-click-modal="false"
    @close="handleDialogClose"
  >
    <ElForm ref="formRef" :model="formData" label-width="100px">
      <ElRow :gutter="20">
        <ElCol :span="12">
          <ElFormItem label="规则名称" prop="ruleName">
            <span>{{ formData.ruleName }}</span>
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="分类" prop="isSystem">
            <span>{{ formData.isSystem ? '系统内置' : '自定义' }}</span>
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="规则类型" prop="dataTypeName">
            <span>{{ formData.dataTypeName }}</span>
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="维度" prop="dimension">
            <span>{{ formData.dimension }}</span>
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="适用数据" prop="fileTypeName">
            <span>{{ formData.fileTypeName }}</span>
          </ElFormItem>
        </ElCol>
        <ElCol :span="24">
          <ElFormItem label="规则说明" prop="ruleContent">
            <span>{{ formData.ruleContent }}</span>
          </ElFormItem>
        </ElCol>
        <ElCol :span="24">
          <ElFormItem label="评分标准" prop="standardContent">
            <span>{{ formData.standardContent }}</span>
          </ElFormItem>
        </ElCol>

        <ElCol :span="24">
          <ElFormItem label="自动治理规则" prop="standardContent">
            <span>{{ formData.autoRuleContent }}</span>
          </ElFormItem>
        </ElCol>
      </ElRow>
    </ElForm>
  </ElDialog>
</template>

<script setup lang="ts">
import { ElDialog, ElForm, ElRow, ElCol, ElFormItem, ElMessage } from 'element-plus'
import useDialogForm from '@/hooks/useDialogForm'
import { ref } from 'vue'
import { uqaRuleGetByIdApi } from '@/api/data_governance'

const { dialogFormVisible, formRef, setDialogFormVisible, handleDialogClose } = useDialogForm()

const formData = ref<any>({})

const handleOpen = async ({ tid }: any) => {
  try {
    const { data, status, message } = await uqaRuleGetByIdApi({ tid })
    if ([200].includes(status)) {
      formData.value = data
      setDialogFormVisible(true)
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}

defineExpose({ handleOpen })
</script>

<style scoped></style>
