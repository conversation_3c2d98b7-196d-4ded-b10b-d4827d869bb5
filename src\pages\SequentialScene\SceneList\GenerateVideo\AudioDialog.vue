<template>
  <ElDialog
    v-model="dialogFormVisible"
    title="音频选择"
    align-center
    :close-on-click-modal="false"
    width="600px"
    @close="handleDialogClose"
  >
    <ElScrollbar height="40vh" v-loading="page.loading">
      <ul
        class="file-list"
        v-infinite-scroll="page.handleScroll"
        :infinite-scroll-disabled="page.end"
        :infinite-scroll-immediate="false"
      >
        <li v-for="p in page.fileList" :key="p.tid">
          <div class="file-name">
            <el-radio v-model="audioObj.audioId" :value="p.tid" size="large">&nbsp;</el-radio>
            <span>{{ getFileNameComplete(p) }}</span>
          </div>
          <div @click="audioObj.handleAudioPlay(p)">
            <SvgIcon
              class="play-icon"
              :name="
                [p.tid].includes(audioObj.activeAudio.tid) && audioObj.isPlay
                  ? 'videoPause'
                  : 'videoPlay'
              "
            />
          </div>
        </li>
      </ul>
      <audio
        ref="audioRef"
        loop
        :src="audioObj.activeAudio.url"
        controls
        style="display: none"
      ></audio>
    </ElScrollbar>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="setDialogFormVisible(false)">取消</el-button>
        <el-button type="primary" @click="handleSave">确定</el-button>
      </span>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
import useDialogForm from '@/hooks/useDialogForm'
import { ElDialog, ElMessage, ElScrollbar } from 'element-plus'
import { userFileListApi } from '@/api/data_catalog'
import { reactive, ref, nextTick } from 'vue'
import { getFileNameComplete } from '@/utils/fileUtils'
import { linkPreviewApi } from '@/api/data_catalog'

const { dialogFormVisible, setDialogFormVisible } = useDialogForm()

const handleDialogClose = () => {
  if (audioObj.isPlay) {
    audioObj.isPlay = false
    audioRef.value.pause()
  }
}

const page = reactive<any>({
  limit: 25,
  page: 1,
  loading: false,
  end: false,
  fileList: [],
  getFileList: async () => {
    try {
      page.loading = true
      const params = {
        page: page.page,
        limit: page.limit,
        fileTypeId: 43,
      }
      const { data, status } = await userFileListApi(params)
      if ([200].includes(status)) {
        if (data.length) {
          page.fileList = page.fileList.concat(data)
          page.page++
          if (data.length < page.limit) {
            page.end = true
          }
        } else {
          page.end = true
        }
      }
      page.loading = false
    } catch (error) {
      page.loading = false
    }
  },
  // 下来刷新
  handleScroll: () => {
    if (page.end) {
      return
    }
    page.getFileList()
  },
})

// 音频相关的对象数据
const audioRef = ref<any>(null)
const audioObj = reactive<any>({
  audioId: '',
  isPlay: false,
  activeAudio: {},
  // 处理音频播放
  handleAudioPlay(audio: any) {
    if (audio.tid === audioObj.activeAudio.tid) {
      if (audioObj.isPlay) {
        audioRef.value.pause()
      } else {
        audioRef.value.play()
      }
      audioObj.isPlay = !audioObj.isPlay
    } else {
      audioObj.activeAudio = { ...audio, url: linkPreviewApi(audio.tid) }
      nextTick(() => {
        audioObj.isPlay = true
        audioRef.value.play()
      })
    }
  },
})

const handleOpen = async () => {
  page.page = 1
  page.fileList = []
  page.end = false
  page.loading = false
  await page.getFileList()
  setDialogFormVisible(true)
}

const handleSave = () => {
  if (!audioObj.audioId) {
    ElMessage.warning('请选择音频')
    return
  }
  const row: any = page.fileList.find((item: any) => item.tid === audioObj.audioId)
  emit('getAudioData', { ...row, url: linkPreviewApi(row.tid) })
  setDialogFormVisible(false)
}

const emit = defineEmits<{
  getAudioData: ['row']
}>()
defineExpose({ handleOpen })
</script>

<style lang="less" scoped>
.file-list {
  li {
    height: 40px;
    display: flex;
    align-items: center;
    .file-name {
      flex: 1;
      height: 100%;
      display: flex;
      align-items: center;
      span {
        flex: 1;
        margin-right: 15px;
        .ellipseLine();
      }
    }
    .play-icon {
      font-size: 24px;
      color: RGBA(102, 102, 102, 1);
      cursor: pointer;
    }
  }
  :deep(.el-radio) {
    margin-right: 0;
  }
}
</style>
