<template>
  <div class="fileList">
    <div class="fileNav">
      <el-breadcrumb class="crumb" separator="/">
        <el-breadcrumb-item v-for="p in breadCrumbList" :key="p.filePath"
          ><span class="title" @click="handleBreadcrumb(p)">{{ p.name }}</span></el-breadcrumb-item
        >
      </el-breadcrumb>
      <el-button-group size="small">
        <el-button
          @click="handlePage"
          :disabled="formData.end || !tableData.length"
          type="primary"
          link
          >下一页</el-button
        >
      </el-button-group>
    </div>
    <el-table
      @selection-change="handleSelectionChange"
      header-cell-class-name="common-table-header"
      cell-class-name="common-table-cell"
      height="250px"
      :data="tableData"
      ref="multipleTableRef"
      row-key="fullPath"
      v-loading="formData.loading"
      border
    >
      <el-table-column type="selection" reserve-selection width="50" />
      <el-table-column width="50">
        <template #default="scope">
          <img
            @click="handleFilePath(scope.row)"
            :src="scope.row.dir ? fileDir : file"
            width="30"
            height="30"
            alt="img"
            style="cursor: pointer"
          />
        </template>
      </el-table-column>
      <el-table-column property="tableName" label="名称" show-overflow-tooltip>
        <template #default="scope">
          <span @click="handleFilePath(scope.row)" style="cursor: pointer">
            {{ scope.row.fileName || scope.row.filePath }}
          </span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup lang="ts">
import fileDir from '@/assets/fileIconImg/file_dir.png'
import file from '@/assets/commonImg/file.png'
import { computed, reactive, ref, watch } from 'vue'
import { queryListApi } from '@/api/data_source'
import { ElMessage } from 'element-plus'

const props = defineProps<{ dataSourceId: any; modelValue: any[]; fileShow: boolean }>()
const emit = defineEmits<{ (e: 'update:modelValue', value: any[]): void }>()

const breadcrumb = ref<any[]>([{ name: '全部', filePath: '' }])
const breadCrumbList = computed<any[]>(() => {
  let tabs: any = []
  const arr: any[] = [...breadcrumb.value]
  if (arr.length > 4) {
    tabs = [...arr.splice(-3)]
    tabs.unshift({ name: '...', filePath: null })
    tabs.unshift(arr[0])
  } else {
    tabs = arr
  }
  return tabs
})
const handleBreadcrumb = async (p: any) => {
  if (p.filePath === null || !tableData.value.length) {
    return
  }
  const index: number = breadcrumb.value.findIndex((item: any) => item.filePath === p.filePath)
  breadcrumb.value.splice(index + 1)
  formData.filePath = p.filePath
  formData.nextMarker = null
  formData.end = false
  await getListData((data: any) => {
    tableData.value = data.list
    formData.nextMarker = data.nextMarker
    formData.end = data.end
  })
}

const handleSelectionChange = (list: any[]) => {
  emit('update:modelValue', list)
}

const tableData = ref<any[]>([])
const formData = reactive<any>({
  end: false,
  filePath: '',
  page: 1,
  nextMarker: null,
  loading: false,
})

const handlePage = () => {
  getListData((data: any) => {
    tableData.value = tableData.value.concat(data.list)
    formData.nextMarker = data.nextMarker
    formData.end = data.end
  })
}

const multipleTableRef = ref<any>()
const handleToggleRowSelection = (list: any[]) => {
  list.forEach((item: any) => {
    multipleTableRef.value?.toggleRowSelection({ fullPath: item }, undefined)
  })
}
const handleClearSelection = () => {
  multipleTableRef.value?.clearSelection()
}

const getListData = async (callback: (data: any) => void) => {
  try {
    formData.loading = true
    const { status, data, message } = await queryListApi({
      dataSourceId: props.dataSourceId,
      filePath: formData.filePath,
      pageSize: 30,
      nextMarker: formData.nextMarker,
    })
    if ([200].includes(status)) {
      callback(data)
    } else {
      ElMessage.error(message)
    }
    formData.loading = false
  } catch (e) {
    formData.loading = false
  }
}

watch(
  () => props.dataSourceId,
  () => {
    if (props.dataSourceId && props.fileShow) {
      breadcrumb.value = [{ name: '全部', filePath: '' }]
      formData.filePath = ''
      formData.nextMarker = null
      formData.end = false
      getListData((data: any) => {
        tableData.value = data.list
        formData.nextMarker = data.nextMarker
        formData.end = data.end
      })
    }
  },
  {
    immediate: true,
  },
)

const handleFilePath = async (p: any) => {
  if (p.dir) {
    breadcrumb.value = [
      ...breadcrumb.value,
      { name: p.fileName || p.filePath, filePath: p.fullPath },
    ]
    formData.filePath = p.fullPath
    formData.nextMarker = null
    formData.end = false
    await getListData((data: any) => {
      tableData.value = data.list
      formData.nextMarker = data.nextMarker
      formData.end = data.end
    })
  }
}

defineExpose({ handleToggleRowSelection, handleClearSelection })
</script>

<style scoped lang="less">
.fileList {
  width: 100%;
  .fileNav {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    .crumb {
      flex: 1;
    }
  }
  .title {
    line-height: 32px;
    cursor: pointer;
  }
  .list {
    margin-top: 10px;
    .list-item {
      display: flex;
      align-items: center;
      img {
        width: 30px;
        margin: 0 5px;
        cursor: pointer;
      }
      span {
        flex: 1;
        cursor: pointer;
        .ellipseLine();
      }
    }
    li + li {
      margin-top: 10px;
    }
  }
}
</style>
