import { onBeforeUnmount, reactive } from 'vue'

export default function useScenePreview(setPreviewLayers: any, isMapbox: boolean = true) {
  const sceneData = reactive<any>({
    timer: null,
    currentIndex: 0,
    rowList: [],
    isPlay: false,
    interval: 0,
  })

  // 初始化轮播组
  const handleScenePreview = (list: any[], time: any) => {
    resetInterval()
    sceneData.rowList = list
    sceneData.interval = parseInt(time) * 1000
    sceneData.currentIndex = 0
    sceneData.isPlay = false
    handlePreview(sceneData.rowList[sceneData.currentIndex])
  }

  // 播放
  const hanldeIsPaly = () => {
    if (!sceneData.isPlay) {
      sceneData.timer = setInterval(() => {
        sceneData.currentIndex = ++sceneData.currentIndex % sceneData.rowList.length
        handlePreview(sceneData.rowList[sceneData.currentIndex])
      }, sceneData.interval)
    } else {
      resetInterval()
    }
    sceneData.isPlay = !sceneData.isPlay
  }

  // 单个预览
  const handlePreview = (row: any) => {
    const { gisPreviewVO, metadataVO } = row
    const data = {
      bbox: JSON.parse(`[${gisPreviewVO.bbox}]`),
      maxzoom: gisPreviewVO.maxZoom,
      minzoom: gisPreviewVO.minZoom,
      url: isMapbox ? gisPreviewVO.mapBoxUrl : gisPreviewVO.url,
      srid: metadataVO.srid,
    }
    setPreviewLayers(data)
  }

  // 清空定时器
  const resetInterval = () => {
    if (sceneData.timer) {
      clearInterval(sceneData.timer)
      sceneData.timer = null
    }
  }

  // 初始化数据
  const clearData = () => {
    resetInterval()
    sceneData.currentIndex = 0
    sceneData.rowList = []
    sceneData.isPlay = false
    sceneData.interval = 0
  }

  onBeforeUnmount(() => {
    resetInterval()
  })

  return { handlePreview, hanldeIsPaly, handleScenePreview, sceneData, resetInterval, clearData }
}
