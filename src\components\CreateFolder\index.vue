<template>
  <el-dialog
    v-model="dialogFormVisible"
    :title="formData.tid ? '编辑文件夹' : '新建文件夹'"
    width="580px"
    :close-on-click-modal="false"
    @close="handleDialogClose"
  >
    <el-form class="add-folder-form" ref="formRef" :model="formData" :rules="rules">
      <el-form-item label="文件夹名称" prop="fileName">
        <el-input
          v-model="formData.fileName"
          placeholder="请输入文件夹名称"
          type="textarea"
          :autosize="{ minRows: 3, maxRows: 3 }"
          maxlength="255"
          show-word-limit
        ></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="setDialogFormVisible(false)">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import useDialogForm from '@/hooks/useDialogForm'
import { mkdirApi, updateDirApi } from '@/api/data_catalog'
import { ElMessage } from 'element-plus'
import { useRoute } from 'vue-router'

const route = useRoute()
const filePath = computed<any>(() => route.query?.filePath || '/')
const dataCatalogId = computed<any>(() => route.query?.dataCatalogId)

const validateFileName = (_rule: any, value: any, callback: any) => {
  const fileNameReg = new RegExp(`[\\\\/:*?"<>|]`)
  if (value && fileNameReg.test(value)) {
    callback(new Error(`文件夹名称不能包含下列任何字符：\\/:*?"<>|`))
  } else {
    callback()
  }
}
const { dialogFormVisible, handleDialogClose, setDialogFormVisible, formRef } = useDialogForm()

const formData = ref<any>({})
const rules = reactive<any>({
  fileName: [
    { required: true, message: '请输入文件夹名称', trigger: 'blur' },
    { validator: validateFileName, trigger: ['blur', 'change'] },
  ],
})

const handleSubmit = async () => {
  try {
    const { status, message } = formData.value.tid
      ? await updateDirApi(formData.value)
      : await mkdirApi({
          filePath: filePath.value,
          dataCatalogId: dataCatalogId.value,
          ...formData.value,
        })
    if ([200].includes(status)) {
      ElMessage.success(message)
      setDialogFormVisible(false)
      emit('handleRefresh')
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}

const handleOpen = (row?: any) => {
  if (row) {
    formData.value = { tid: row.tid, fileName: row.fileName }
  } else {
    formData.value = {}
  }
  setDialogFormVisible(true)
}

const emit = defineEmits<{
  (e: 'handleRefresh'): void
}>()
defineExpose({ handleOpen })
</script>

<style scoped lang="less">
.add-folder-form {
  :deep(.el-form-item) {
    display: block;
  }
}
</style>
