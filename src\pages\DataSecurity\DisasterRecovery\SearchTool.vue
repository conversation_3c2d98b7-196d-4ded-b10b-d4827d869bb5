<template>
  <el-form ref="ruleFormRef" :model="ruleForm">
    <div class="search-content">
      <div class="search-input">
        <div class="search-item">
          <el-form-item prop="taskName">
            <el-input
              class="form-item"
              v-model="ruleForm.taskName"
              placeholder="请输入任务名称"
              @keydown.enter="handleSearch"
            >
              <template #suffix>
                <SvgIcon @click="handleSearch" style="cursor: pointer" name="search" />
              </template>
            </el-input>
          </el-form-item>
        </div>
        <div class="search-item">
          <el-form-item prop="taskStatus">
            <el-select
              class="form-item"
              v-model="ruleForm.taskStatus"
              placeholder="请选择任务状态"
              clearable
              @change="handleSearch"
            >
              <el-option
                v-for="p in taskStatusCodeOptions"
                :key="p.key"
                :label="p.name"
                :value="p.key"
              />
            </el-select>
          </el-form-item>
        </div>
      </div>
      <div v-if="permission.hasButton('dataSecurity_add')" class="search-btn">
        <el-button type="primary" @click="emit('handleCreate')">
          <template #icon>
            <SvgIcon name="add" />
          </template>
          新建灾备任务
        </el-button>
      </div>
    </div>
  </el-form>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'
import type { FormInstance } from 'element-plus'
import { DicType } from '@/utils/constant'
import useGlobalData from '@/store/useGlobalData'
import useUserInfo from '@/store/useUserInfo'

const permission = useUserInfo()

// 字典值
const global = useGlobalData()
const taskStatusCodeOptions = global.getTypeData(DicType.TaskBackUpStatusCode)

const ruleFormRef = ref<FormInstance>()
const ruleForm = reactive<any>({
  taskStatus: '',
})

const handleSearch = () => {
  emit('handleSearch', ruleForm)
}

const emit = defineEmits<{
  (e: 'handleSearch', form: any): void
  (e: 'handleCreate'): void
}>()
</script>
