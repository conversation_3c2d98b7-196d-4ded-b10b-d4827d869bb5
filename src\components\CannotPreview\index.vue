<template>
  <el-dialog
    v-model="dialogFormVisible"
    title="查看"
    width="500px"
    :close-on-click-modal="false"
    @close="handleDialogClose"
  >
    <div class="fileSee" v-if="dialogFormVisible">
      <div class="file-top">
        <img :src="getFileIcon(row)" alt="" style="width: 30px; height: 30px" />
        <span class="fileName" :title="getFileNameComplete(row)">
          {{ getFileNameComplete(row) }}
        </span>
        <span style="line-height: 30px">{{ calculateFileSize(Number(row.fileSize)) }}</span>
      </div>
      <div class="file-center">此文件类型暂无法预览，请下载后查看</div>
      <el-button type="primary" @click="handleDownload">下载</el-button>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import useDialogForm from '@/hooks/useDialogForm'
import { downloadFileApi } from '@/api/common'
import {
  getFileIcon,
  getFileNameComplete,
  calculateFileSize,
  downloadFile,
} from '@/utils/fileUtils'
import useUserInfo from '@/store/useUserInfo'

const userInfo = useUserInfo()

import { ref } from 'vue'

const { dialogFormVisible, handleDialogClose, setDialogFormVisible } = useDialogForm()
const row = ref<any>(null)

const handleOpen = (file: any) => {
  row.value = file
  setDialogFormVisible(true)
}

const handleDownload = () => {
  downloadFile(`${downloadFileApi()}?tid=${row.value.tid}&token=${userInfo.token}`)
}

defineExpose({ handleOpen })
</script>

<style scoped lang="less">
.fileSee,
.file-center {
  margin-top: 30px;
  text-align: center;
}
.file-center {
  margin-bottom: 30px;
}
.file-top {
  display: flex;
  justify-content: center;
  margin: 0 auto;
  height: 30px;
}
.fileName {
  max-width: 300px;
  padding: 0px 10px;
  line-height: 30px;
  .setEllipsis(1);
}
</style>
