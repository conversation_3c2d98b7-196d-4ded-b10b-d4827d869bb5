<template>
  <div class="addEditDetail">
    <div class="header">{{ title }}</div>
    <div class="form-data">
      <el-form ref="ruleFormRef" :disabled="!isEdit" :model="ruleForm" :rules="rules" size="large">
        <el-row :gutter="60">
          <el-col :span="12">
            <el-form-item label="适配器" prop="adapterCode">
              <el-select
                style="width: 100%"
                v-model="ruleForm.adapterCode"
                placeholder="请选择适配器"
              >
                <el-option
                  v-for="p in adapterCodeOptions"
                  :key="p.key"
                  :label="p.name"
                  :value="p.key"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <template v-if="isFormShow.dataSourceShow">
            <el-col :span="12">
              <el-form-item label="存储类型" prop="region">
                <el-select
                  style="width: 100%"
                  v-model="ruleForm.region"
                  multiple
                  value-key="key"
                  placeholder="请选择存储类型"
                  @change="handleChange"
                >
                  <el-option
                    v-for="p in storeAdapterOssDsCodeOptions"
                    :key="p.key"
                    :label="p.name"
                    :value="p"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="对象存储"
                prop="name"
                style="overflow-x: auto; white-space: nowrap"
              >
                <el-scrollbar>
                  <div style="display: inline-block">
                    <ul class="cardList">
                      <li class="card">
                        <div class="card-left">
                          <span> 阿里云oss </span>
                          <div class="green"></div>
                        </div>
                        <div class="card-right">
                          <el-button
                            type="primary"
                            link
                            @click="TestDataDialogRef.handleOpen(null)"
                            class="icon"
                          >
                            <template #icon>
                              <SvgIcon name="editPen" />
                            </template>
                          </el-button>
                        </div>
                      </li>
                    </ul>
                  </div>
                </el-scrollbar>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="文件存储"
                prop="name"
                style="overflow-x: auto; white-space: nowrap"
              >
                <el-scrollbar>
                  <div style="display: inline-block">
                    <ul class="cardList">
                      <li class="card">
                        <div class="card-left">
                          <span> 阿里云oss </span>
                          <div class="green"></div>
                        </div>
                        <div class="card-right">
                          <el-button
                            type="primary"
                            link
                            @click="TestDataDialogRef.handleOpen(null)"
                            class="icon"
                          >
                            <template #icon>
                              <SvgIcon name="editPen" />
                            </template>
                          </el-button>
                        </div>
                      </li>
                    </ul>
                  </div>
                </el-scrollbar>
              </el-form-item>
            </el-col>
          </template>
          <el-col :span="12">
            <el-form-item label="数据源名称" prop="datasourceName">
              <el-input v-model="ruleForm.datasourceName" placeholder="请输入数据源名称" />
            </el-form-item>
          </el-col>
          <template v-if="isFormShow.dataBaseShow">
            <el-col :span="12">
              <el-form-item label="IP" prop="hosts">
                <el-input v-model="ruleForm.hosts" placeholder="请输入IP" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="端口" prop="ports">
                <el-input v-model="ruleForm.ports" placeholder="请输入端口" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                :label="['ORACLE'].includes(ruleForm.adapterCode) ? '服务名' : '数据库'"
                prop="dbName"
              >
                <el-input v-model="ruleForm.dbName" placeholder="请输入数据库" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="用户名">
                <el-input v-model="ruleForm.usernames" placeholder="请输入用户名" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="密码">
                <el-input v-model="ruleForm.passwords" placeholder="请输入密码" />
              </el-form-item>
            </el-col>
          </template>
          <template v-if="isFormShow.EsShow">
            <el-col :span="12">
              <el-form-item label="连接协议" prop="protocol">
                <el-input v-model="ruleForm.protocol" placeholder="请输入连接协议" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="IP" prop="hosts">
                <el-input v-model="ruleForm.hosts" placeholder="请输入IP" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="端口" prop="ports">
                <el-input v-model="ruleForm.ports" placeholder="请输入端口" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="索引" prop="dbName">
                <el-input v-model="ruleForm.dbName" placeholder="请输入索引" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="用户名">
                <el-input v-model="ruleForm.usernames" placeholder="请输入用户名" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="密码">
                <el-input v-model="ruleForm.passwords" placeholder="请输入密码" />
              </el-form-item>
            </el-col>
          </template>
          <template v-if="isFormShow.objectShow">
            <el-col :span="12">
              <el-form-item label="域名" prop="hosts">
                <el-input v-model="ruleForm.hosts" placeholder="请输入域名" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="访问秘钥" prop="accessKey">
                <el-input v-model="ruleForm.accessKey" placeholder="请输入访问秘钥" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="安全秘钥" prop="secretKey">
                <el-input v-model="ruleForm.secretKey" placeholder="请输入安全秘钥" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="桶名" prop="dbName">
                <el-input v-model="ruleForm.dbName" placeholder="请输入桶名" />
              </el-form-item>
            </el-col>
          </template>
          <template v-if="isFormShow.minIoShow">
            <el-col :span="12">
              <el-form-item label="连接协议" prop="protocol">
                <el-input v-model="ruleForm.protocol" placeholder="请输入连接协议" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="IP" prop="hosts">
                <el-input v-model="ruleForm.hosts" placeholder="请输入IP" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="端口" prop="ports">
                <el-input v-model="ruleForm.ports" placeholder="请输入端口" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="访问秘钥" prop="accessKey">
                <el-input v-model="ruleForm.accessKey" placeholder="请输入访问秘钥" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="安全秘钥" prop="secretKey">
                <el-input v-model="ruleForm.secretKey" placeholder="请输入安全秘钥" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="桶名" prop="dbName">
                <el-input v-model="ruleForm.dbName" placeholder="请输入桶名" />
              </el-form-item>
            </el-col>
          </template>
          <template v-if="isFormShow.fileShow">
            <el-col :span="12">
              <el-form-item label="IP" prop="hosts">
                <el-input v-model="ruleForm.hosts" placeholder="请输入IP" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="端口号" prop="ports">
                <el-input v-model="ruleForm.ports" placeholder="请输入端口号" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="账号" prop="usernames">
                <el-input v-model="ruleForm.usernames" placeholder="请输入安全秘钥" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="密码" prop="passwords">
                <el-input v-model="ruleForm.passwords" placeholder="请输入密码" />
              </el-form-item>
            </el-col>
          </template>
          <template v-if="isFormShow.ftpShow">
            <el-col :span="12">
              <el-form-item label="IP" prop="hosts">
                <el-input v-model="ruleForm.hosts" placeholder="请输入IP" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="端口">
                <el-input v-model="ruleForm.ports" placeholder="请输入端口" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="文件路径">
                <el-input v-model="ruleForm.dbName" placeholder="请输入文件路径" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="用户名">
                <el-input v-model="ruleForm.usernames" placeholder="请输入用户名" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="密码">
                <el-input v-model="ruleForm.passwords" placeholder="请输入密码" />
              </el-form-item>
            </el-col>
          </template>
          <template v-if="isFormShow.nfsShow">
            <el-col :span="12">
              <el-form-item label="IP" prop="hosts">
                <el-input v-model="ruleForm.hosts" placeholder="请输入IP" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="端口">
                <el-input v-model="ruleForm.ports" placeholder="请输入端口" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="数据路径" prop="dbName">
                <el-input v-model="ruleForm.dbName" placeholder="请输入数据路径" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="用户名">
                <el-input v-model="ruleForm.usernames" placeholder="请输入用户名" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="密码">
                <el-input v-model="ruleForm.passwords" placeholder="请输入密码" />
              </el-form-item>
            </el-col>
          </template>
          <el-col :span="24">
            <el-form-item label="描述" prop="notes">
              <el-input
                :rows="6"
                v-model="ruleForm.notes"
                placeholder="请输入描述"
                type="textarea"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="footer-btn">
      <el-button size="large" @click="router.replace({ name: 'dataSource' })">取消</el-button>
      <el-button
        :loading="btnDisabled"
        type="primary"
        size="large"
        @click="handleConnectTest"
        :disabled="!isEdit || btnDisabled"
        >测试连接</el-button
      >
      <el-button
        type="primary"
        size="large"
        @click="handleSubmit"
        :disabled="!isEdit || btnDisabled"
        >确定</el-button
      >
    </div>
    <TestDataDialog ref="TestDataDialogRef" />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import TestDataDialog from './TestDataDialog.vue'
import { DicType } from '@/utils/constant'
import useGlobalData from '@/store/useGlobalData'
import { AddByParamApi, connectByParamApi, queryByIdApi, modifyByIdApi } from '@/api/data_source'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()

const global = useGlobalData()
const adapterCodeOptions = global.getTypeData(DicType.AdapterCode)
const storeAdapterOssDsCodeOptions = global.getTypeData(DicType.AdapterOssDsCode)

const title = computed(() => {
  if (!isEdit.value) {
    return '数据源详情'
  }
  return route.query.id ? '编辑数据源' : '新增数据源'
})

// 表单数据
const ruleFormRef = ref<FormInstance>()
const ruleForm = ref<any>({})

// 编辑详情数据加载
const isEdit = ref<boolean>(true)
const getDataDetail = async () => {
  if (!route.query.id) {
    return
  }
  try {
    const { data, status, message } = await queryByIdApi(route.query.id)
    if ([200].includes(status)) {
      ruleForm.value = { ...data }
      isEdit.value = JSON.parse(route.query?.isEdit as string)
    } else {
      ElMessage.error(message)
    }
  } catch (error) {
    console.error(error)
  }
}
onMounted(() => {
  getDataDetail()
})

// 提交测试
const btnDisabled = ref<boolean>(false)
const handleConnectTest = async () => {
  try {
    btnDisabled.value = true
    const { status, message } = await connectByParamApi({ ...ruleForm.value })
    if ([200].includes(status)) {
      ElMessage.success(message)
    } else {
      ElMessage.error(message)
    }
    btnDisabled.value = false
  } catch (error) {
    btnDisabled.value = false
    console.error(error)
  }
}

// 提交
const router = useRouter()
const handleSubmit = async () => {
  try {
    btnDisabled.value = true
    const { status, message } = ruleForm.value.tid
      ? await modifyByIdApi({ ...ruleForm.value })
      : await AddByParamApi({ ...ruleForm.value })
    if ([200].includes(status)) {
      ElMessage.success(message)
      router.replace({ name: 'dataSource' })
      btnDisabled.value = false
    } else {
      btnDisabled.value = false
      ElMessage.error(message)
    }
  } catch (error) {
    btnDisabled.value = false
    console.error(error)
  }
}

// 判断表单的显示
const isFormShow = computed<any>(() => {
  return {
    // PostgreSQL MongoDB 达梦数据库
    dataBaseShow: ['POSTGRESQL', 'MONGO_DB', 'DM', 'ORACLE', 'MYSQL'].includes(
      ruleForm.value.adapterCode,
    ),
    // Elasticsearch
    EsShow: ['ELASTICSEARCH'].includes(ruleForm.value.adapterCode),
    // 对象存储  七牛云OSS  曙光ParaStor
    objectShow: ['QINIU', 'PARA_STOR'].includes(ruleForm.value.adapterCode),
    //  MinIO 阿里云OSS显示
    minIoShow: ['MINIO', 'ALIYUN'].includes(ruleForm.value.adapterCode),
    // 文件存储 FTP
    fileShow: ['FAST_DFS'].includes(ruleForm.value.adapterCode),
    // FTP显示
    ftpShow: ['FTP'].includes(ruleForm.value.adapterCode),
    // NFS显示
    nfsShow: ['NFS'].includes(ruleForm.value.adapterCode),
    // 多源数据源
    dataSourceShow: ['JUICE_FS'].includes(ruleForm.value.adapterCode),
  }
})

// 判断默认值
const handleDefaultValue = () => {
  if (route.query.id) return
  if (ruleForm.value.adapterCode) {
    if (['POSTGRESQL'].includes(ruleForm.value.adapterCode)) {
      ruleForm.value.ports = 5432
      ruleForm.value.usernames = 'postgres'
      ruleForm.value.passwords = 'postgres'
    } else {
      ruleForm.value.ports = ''
      ruleForm.value.usernames = ''
      ruleForm.value.passwords = ''
    }
  }
}
watch(
  () => ruleForm.value.adapterCode,
  () => {
    handleDefaultValue()
  },
)

// 表单数据
const TestDataDialogRef = ref<any>()
const handleChange = () => {
  console.log(ruleForm.value)
}

const rules = reactive<FormRules<any>>({
  adapterCode: [{ required: true, message: '请选择数据源类型', trigger: 'change' }],
  datasourceName: [{ required: true, message: '请填写数据源名称', trigger: 'change' }],
  protocol: [{ required: true, message: '必填', trigger: 'change' }],
  hosts: [{ required: true, message: '必填', trigger: 'change' }],
  ports: [{ required: true, message: '请填写端口', trigger: 'change' }],
  dbName: [{ required: true, message: '必填', trigger: 'change' }],
  usernames: [{ required: true, message: '必填', trigger: 'change' }],
  passwords: [{ required: true, message: '必填', trigger: 'change' }],
  accessKey: [{ required: true, message: '必填', trigger: 'change' }],
  secretKey: [{ required: true, message: '必填', trigger: 'change' }],
})
</script>

<style scoped lang="less">
.addEditDetail {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  background: #eaeaea;
  box-shadow: 3px 3px 12px 3px #ebebeb;
  display: flex;
  flex-direction: column;
  .header {
    width: 100%;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    background-color: @withe;
    box-shadow: 0 8px 8px 8px rgba(0, 0, 0, 0.01);
    padding: 20px;
    font-size: 16px;
    font-weight: bold;
    color: #333333;
  }
  .form-data {
    flex: 1;
    box-sizing: border-box;
    padding: 40px 5% 0 5%;
    overflow-y: auto;
    overflow-x: hidden;
    .no-scrollbar(6px);
    :deep(.el-form-item) {
      display: block;
    }
    :deep(.el-scrollbar) {
      overflow: auto;
    }
    .cardList {
      display: flex;
      box-sizing: border-box;
      overflow: hidden;
      .card {
        width: 155px;
        height: 40px;
        border: 1px solid #d8d8d8;
        border-radius: 2px 2px 2px 2px;
        background-color: @withe;
        display: flex;
        box-sizing: border-box;
        .card-left {
          flex: 1;
          box-sizing: border-box;
          padding: 0 15px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          span {
            font-size: 14px;
            color: #333333;
            .setEllipsis(1);
          }
          div {
            margin-left: 10px;
            width: 8px;
            height: 8px;
            border-radius: 50%;
          }
          .green {
            background: #6fb503;
          }
          .red {
            background: rgba(217, 99, 99, 1);
          }
          .grey {
            background: rgba(216, 216, 216, 1);
          }
        }
        .card-right {
          width: 36px;
          border-left: 1px solid #d8d8d8;
          box-sizing: border-box;
          text-align: center;
          .icon {
            font-size: 16px;
          }
        }
      }
      li + li {
        margin-left: 20px;
      }
    }
  }
  .footer-btn {
    text-align: right;
    margin-top: 20px;
    padding-bottom: 40px;
    padding-right: 5%;
  }
}
</style>
