<template>
  <transition name="fade">
    <div v-if="showViewer" class="common-preview-wrapper">
      <div class="tip-wrapper">
        <div></div>
        <p>{{ rowData.serverName }}</p>
        <div class="tip-right">
          <el-select
            size="small"
            style="width: 100px; margin-right: 20px"
            v-model="basemapType"
            placeholder="底图"
            @change="handleChangeMap"
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
          <SvgIcon name="closeBold" @click="handleClose" class="close-icon" />
        </div>
      </div>
      <div class="map-wrapper">
        <div class="olMap" ref="mapRef">
          <div class="formData">
            <el-radio-group @change="handleChangeType" v-model="formData.type">
              <el-radio :value="0">实时</el-radio>
              <el-radio :value="1">回放</el-radio>
            </el-radio-group>
            <template v-if="formData.type">
              <el-date-picker
                @change="handlechangeTime"
                style="margin-left: 20px"
                v-model="formData.dateRange"
                value-format="YYYY-MM-DD HH:mm:ss"
                :shortcuts
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
              />
              <el-select style="width: 120px; margin-left: 20px" v-model="formData.speed">
                <el-option v-for="(p, i) in speedList" :key="i" :label="p.label" :value="p.value" />
              </el-select>
              <el-button v-if="playData.timer" @click="handleStop" type="primary"> 暂停 </el-button>
              <el-button v-else @click="handlePaly" type="primary"> 播放 </el-button>
            </template>
          </div>
          <div v-if="formData.dateRange && formData.dateRange.length" class="map-siler">
            <ElSlider
              :format-tooltip="(value: number) => dayjs(value).format('YYYY-MM-DD HH:mm:ss')"
              :min="playData.start"
              :max="playData.end"
              v-model="playData.targetTime"
            />
          </div>
        </div>
      </div>
    </div>
  </transition>
</template>
<script setup lang="ts">
import { IotPlaybackApi } from '@/api/stream_data'
import useMapbox from '@/hooks/useMapbox'
import usePositionMap from '@/hooks/usePositionMap'
import dayjs from 'dayjs'
import { ElSlider } from 'element-plus'
import { Marker, Popup } from 'mapbox-gl'
import { nextTick, ref } from 'vue'

const shortcuts = [
  {
    text: '最近一周',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setDate(start.getDate() - 7)
      return [start, end]
    },
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setMonth(start.getMonth() - 1)
      return [start, end]
    },
  },
  {
    text: '最近三个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setMonth(start.getMonth() - 3)
      return [start, end]
    },
  },
]

const speedList = [
  { label: '1倍速', value: 1 },
  { label: '2倍速', value: 2 },
  { label: '4倍速', value: 4 },
  { label: '8倍速', value: 8 },
  { label: '50倍速', value: 50 },
  { label: '100倍速', value: 100 },
  { label: '1000倍速', value: 1000 },
]

const { handleChangeMap, mapRef, initMap, map, mapOperate } = useMapbox('mercator')

const showViewer = ref<boolean>(false)

const { initSocket, closeSocket } = usePositionMap()

const rowData = ref<any>({})
const formData = ref<any>({ type: 0, speed: 1, dateRange: [] })

const handleChangeType = () => {
  if (formData.value.type) {
    closeSocket()
    handleClearReal()
  } else {
    formData.value.dateRange = []
    formData.value.speed = 1
    handleClearMarkers()
    handleStop()
    playData.value = {
      start: 0,
      end: 0,
      targetTime: 0,
      timer: null,
      marker: [],
      popup: [],
    }

    handleRealPreview(rowData.value)
  }
}

const handleClose = () => {
  showViewer.value = false
  formData.value.type = 0
  closeSocket()
  handleClearReal()
  formData.value.dateRange = []
  formData.value.speed = 1
  handleClearMarkers()
  handleStop()
  playData.value = {
    start: 0,
    end: 0,
    targetTime: 0,
    timer: null,
    marker: [],
    popup: [],
  }
}

// 实时数据
const marker = ref<Marker | null>(null)
const popup = ref<Popup | null>(null)

const handleRealPreview = (row: any) => {
  initSocket(row.serverName, ({ lon, lat, json }: any) => {
    if (marker.value) {
      marker.value?.setLngLat([lon, lat])
    } else {
      marker.value = new Marker({ color: '#ff0000' }).setLngLat([lon, lat]).addTo(map.value)
    }
    const keys = Object.values(json)
    if (json && keys.length) {
      if (popup.value) {
        popup.value?.setLngLat([lon, lat]).setText(keys.join(','))
      } else {
        popup.value = new Popup({ offset: 30 })
          .setText(keys.join(','))
          .setLngLat([lon, lat]) // Popup位置
          .addTo(map.value) // 将Popup添加到地图
      }
    }
    mapOperate.resetMap([lon, lat])
  })
}

const handleClearReal = () => {
  if (marker.value) {
    marker.value?.remove()
    marker.value = null
  }
  if (popup.value) {
    popup.value?.remove()
    popup.value = null
  }
}

// 回放数据
const playData = ref<any>({
  start: 0,
  end: 0,
  targetTime: 0,
  timer: null,
  marker: [],
  popup: [],
})
const handlechangeTime = (dateRange: any) => {
  handleClearMarkers()
  handleStop()
  playData.value = {
    start: 0,
    end: 0,
    targetTime: 0,
    timer: null,
    marker: [],
    popup: [],
  }
  playData.value.start = dateRange && dateRange.length ? dayjs(dateRange[0]).valueOf() : 0
  playData.value.end = dateRange && dateRange.length ? dayjs(dateRange[1]).valueOf() : 0
  playData.value.targetTime = dateRange && dateRange.length ? dayjs(dateRange[0]).valueOf() : 0
}

const handleClearMarkers = () => {
  if (playData.value.marker.length) {
    playData.value.marker.forEach((item: any) => {
      item.remove()
    })
    playData.value.marker = []
  }
  if (playData.value.popup.length) {
    playData.value.popup.forEach((item: any) => {
      item.remove()
    })
    playData.value.popup = []
  }
}

const getPlayData = async () => {
  try {
    const params = {
      tid: rowData.value.tid,
      duration: formData.value.speed * 1500,
      targetTime: playData.value.targetTime,
    }
    const { data, status } = await IotPlaybackApi(params)
    if ([200].includes(status)) {
      const { targets, centerPoint } = data
      if (targets && targets.length) {
        handleClearMarkers()
        targets.forEach((item: any) => {
          const marker = new Marker({ color: '#ff0000' })
            .setLngLat([item.lon, item.lat])
            .addTo(map.value)
          playData.value.marker.push(marker)
          if (item.extend) {
            const keys = Object.values(item.extend)
            if (keys.length) {
              playData.value.popup.push(
                new Popup({ offset: 30 })
                  .setText(keys.join(','))
                  .setLngLat([item.lon, item.lat]) // Popup位置
                  .addTo(map.value),
              )
            }
          }
        })
        mapOperate.resetMap(centerPoint.split(','))
      }
    }
  } catch (error) {}
}

const handlePaly = () => {
  if (playData.value.targetTime >= playData.value.end) {
    playData.value.targetTime = playData.value.start
  }
  playData.value.timer = setInterval(() => {
    playData.value.targetTime += formData.value.speed * 1500
    if (playData.value.targetTime > playData.value.end) {
      playData.value.targetTime = playData.value.end
      getPlayData()
      handleStop()
    }
    getPlayData()
  }, 1500)
}

const handleStop = () => {
  if (playData.value.timer) {
    clearInterval(playData.value.timer)
    playData.value.timer = null
  }
}

const handleOpenPreview = (row: any) => {
  rowData.value = row
  showViewer.value = true
  nextTick(() => {
    initMap(() => {
      handleRealPreview(row)
    })
  })
}

const basemapType = ref<string>('img')
const options: any[] = [
  { value: 'img', label: '星图影像' },
  { value: 'vec', label: '星图矢量' },
]

defineExpose({ handleOpenPreview })
</script>
<style lang="less" scoped>
.common-preview-wrapper {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  .tip-wrapper {
    background: rgba(0, 0, 0, 0.5);
    padding: 0 48px;
    width: 100%;
    height: 48px;
    line-height: 48px;
    color: @withe;
    font-size: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-sizing: border-box;
    .tip-right {
      display: flex;
      height: 100%;
      align-items: center;
      .close-icon {
        cursor: pointer;
      }
    }
  }
  .map-wrapper {
    flex: 1;
    box-sizing: border-box;
    padding: 30px 40px;
    position: relative;
    overflow: hidden;
    .olMap {
      height: 100%;
      width: 100%;
      box-sizing: border-box;
      background-color: rgba(0, 0, 0, 0.9);
      position: relative;
      ::v-deep(.mapboxgl-popup-close-button) {
        display: none;
      }
      .map-siler {
        position: absolute;
        bottom: 30px;
        left: 15%;
        right: 15%;
        z-index: 10000;
      }
    }
    .formData {
      position: absolute;
      height: 40px;
      left: 0;
      top: 0;
      background-color: @withe;
      display: flex;
      align-items: center;
      z-index: 10000;
      padding: 0 15px;
    }
  }
}
</style>
