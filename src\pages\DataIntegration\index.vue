<template>
  <div class="common-layout">
    <CommonSiderMenu />
    <div class="common-layout-main">
      <router-view v-slot="{ Component, route }">
        <keep-alive>
          <component v-if="route.meta.keepAlive" :is="Component" :key="route.path" />
        </keep-alive>
        <component v-if="!route.meta.keepAlive" :is="Component" :key="route.path" />
      </router-view>
    </div>
  </div>
</template>

<script setup lang="ts">
import CommonSiderMenu from '@/components/CommonSiderMenu/index.vue'
import { RouterView } from 'vue-router'
</script>

<style lang="less" scoped></style>
