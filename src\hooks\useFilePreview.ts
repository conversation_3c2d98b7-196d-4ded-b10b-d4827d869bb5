import { ref } from 'vue'
import ImagePreview from '@/components/ImagePreview/index.vue'
import OlMapPreview from '@/components/OlMapPreview/index.vue'
import MapBoxPreview from '@/components/MapBoxPreview/index.vue'
import CesiumPreview from '@/components/CesiumPreview/index.vue'
import MarkdownPreview from '@/components/MarkdownPreview/index.vue'
import CodePreview from '@/components/CodePreview/index.vue'
import VideoPreview from '@/components/VideoPreview/index.vue'
import AudioPreview from '@/components/AudioPreview/index.vue'
import CannotPreview from '@/components/CannotPreview/index.vue'
import { useRouter } from 'vue-router'
import {
  fileSuffixCodeModeMap,
  markdownFileType,
  gisPreview,
  cesiumPreview,
  officeFileType,
} from '@/utils/fileMap'
import { ElMessage } from 'element-plus'
import { linkPreviewApi } from '@/api/data_catalog'

// 文件无法预览
export const useCannotPreview = () => {
  const CannotPreviewRef = ref<any>()

  const handleCannotPreview = (row: any) => {
    CannotPreviewRef.value?.handleOpen(row)
  }

  return { CannotPreview, CannotPreviewRef, handleCannotPreview }
}

// 音频预览
export const useAudioPreview = () => {
  const AudioPreviewRef = ref<any>()

  const handleAudioPreview = (rowList: any, index?: number) => {
    AudioPreviewRef.value?.handleOpenPreview(rowList, index)
  }

  return { AudioPreview, AudioPreviewRef, handleAudioPreview }
}

// 视频预览
export const useVideoPreview = () => {
  const VideoPreviewRef = ref<any>()

  const handleVideoPreview = (rowList: any, index?: number) => {
    VideoPreviewRef.value?.handleOpenPreview(rowList, index)
  }

  return { VideoPreview, VideoPreviewRef, handleVideoPreview }
}

// 代码文件预览
export const useCodePreview = () => {
  const CodePreviewRef = ref<any>()

  const handleCodePreview = (row: any, edit?: boolean) => {
    CodePreviewRef.value?.handleOpenPreview(row, edit)
  }

  return { CodePreview, CodePreviewRef, handleCodePreview }
}

// markdown文件预览
export const useMarkdownPreview = () => {
  const MarkdownPreviewRef = ref<any>()

  const handleMarkdownPreview = (row: any, edit?: boolean) => {
    MarkdownPreviewRef.value?.handleOpenPreview(row, edit)
  }

  return { MarkdownPreview, MarkdownPreviewRef, handleMarkdownPreview }
}

// 服务地图预览
export const useOlMapPreview = () => {
  const OlMapPreviewRef = ref<any>()

  const handleMapPreview = (row: any) => {
    OlMapPreviewRef.value?.handleOpenPreview(row)
  }

  return { OlMapPreview, OlMapPreviewRef, handleMapPreview }
}

// mapbox地图预览
export const useMapboxPreview = () => {
  const MapBoxPreviewRef = ref<any>()
  const handleMapboxPreview = (row: any, isapi?: boolean) => {
    MapBoxPreviewRef.value?.handleOpenPreview(row, isapi)
  }
  return { MapBoxPreview, MapBoxPreviewRef, handleMapboxPreview }
}

// cesium地图预览
export const useCesiumPreview = () => {
  const CesiumPreviewRef = ref<any>()

  const handleCesiumPreview = (row: any) => {
    CesiumPreviewRef.value?.handleOpenPreview(row)
  }

  return { CesiumPreview, CesiumPreviewRef, handleCesiumPreview }
}

// 文件图片预览
export const useFileImgPreview = () => {
  const ImagePreviewRef = ref<any>()

  const handleImgPreview = (urlList: any[], index?: number) => {
    ImagePreviewRef.value?.handleOpenPreview(urlList, index)
  }

  return { ImagePreview, ImagePreviewRef, handleImgPreview }
}

// 文件列表各种文件类型的预览
export default function useFilePreview() {
  const router = useRouter()
  //音频预览
  const { AudioPreviewRef, handleAudioPreview } = useAudioPreview()
  // 视频预览
  const { VideoPreviewRef, handleVideoPreview } = useVideoPreview()
  // 代码文件预览
  const { CodePreviewRef, handleCodePreview } = useCodePreview()
  // 图片预览
  const { ImagePreviewRef, handleImgPreview } = useFileImgPreview()
  // 地图预览
  // const { OlMapPreviewRef, handleMapPreview } = useOlMapPreview()
  // mapbox地图预览
  const { MapBoxPreviewRef, handleMapboxPreview } = useMapboxPreview()
  // cesium地图预览
  const { CesiumPreviewRef, handleCesiumPreview } = useCesiumPreview()
  // md文件预览
  const { MarkdownPreviewRef, handleMarkdownPreview } = useMarkdownPreview()
  // 文件无法预览
  const { CannotPreviewRef, handleCannotPreview } = useCannotPreview()
  // 所有文件一起的预览判断
  const handleFilePreview = (row: any, fileList: any[] = []) => {
    const list: any[] = fileList.length ? fileList : [row]
    const initialIndex = list.findIndex((item: any) => [item.tid].includes(row.tid))
    // 文件夹预览情况
    if ([1].includes(row.isDir)) {
      router.push({
        query: {
          filePath: `${['/'].includes(row.filePath) ? '' : row.filePath}/${row.fileName}`,
          dataCatalogId: row.dataCatalogId,
        },
      })
      return
    }
    // 文件无法预览
    if ([0].includes(row.previewState)) {
      handleCannotPreview(row)
      return
    }
    // 图片预览
    if (['45'].includes(row.fileTypeId)) {
      const urlList: string[] = list.map((item: any) => linkPreviewApi(item.tid))
      handleImgPreview(urlList, initialIndex)
      return
    }
    // 视频预览
    if (['44'].includes(row.fileTypeId)) {
      const urlList: string[] = list.map((item: any) => ({
        ...item,
        url: linkPreviewApi(item.tid),
      }))
      handleVideoPreview(urlList, initialIndex)
      return
    }
    // 音频预览
    if (['43'].includes(row.fileTypeId)) {
      const urlList: string[] = list.map((item: any) => ({
        ...item,
        url: linkPreviewApi(item.tid),
      }))
      handleAudioPreview(urlList, initialIndex)
      return
    }
    // gis数据预览
    if (gisPreview.includes(row.fileTypeId)) {
      if (![1].includes(row.serverStatus)) {
        ElMessage.warning(`服务${row.serverStatusName}，暂时无法预览！`)
        return
      }

      handleMapboxPreview(row)
      return
    }
    // cesium地图预览
    if (cesiumPreview.includes(row.fileTypeId)) {
      if (![1].includes(row.serverStatus)) {
        ElMessage.warning(`服务${row.serverStatusName}，暂时无法预览！`)
        return
      }
      handleCesiumPreview(row)
      return
    }
    // 代码类预览
    if (fileSuffixCodeModeMap.includes(row.suffix.toLowerCase())) {
      handleCodePreview(row)
      return
    }
    // markdown文件预览
    if (markdownFileType.includes(row.suffix.toLowerCase())) {
      handleMarkdownPreview(row)
      return
    }
    // onlyOffice文件预览
    if (officeFileType.includes(row.suffix.toLowerCase())) {
      const { href } = router.resolve({
        name: 'onlyOffice',
        query: {
          userFileId: row.tid,
          isEdit: 0,
        },
      })
      window.open(href, '_blank')
      return
    }
    // 文件无法预览
    handleCannotPreview(row)
  }

  return {
    handleFilePreview,
    ImagePreview,
    ImagePreviewRef,
    // OlMapPreview,
    // OlMapPreviewRef,
    MapBoxPreview,
    MapBoxPreviewRef,
    CesiumPreview,
    CesiumPreviewRef,
    MarkdownPreview,
    MarkdownPreviewRef,
    handleMarkdownPreview,
    CodePreview,
    CodePreviewRef,
    handleCodePreview,
    VideoPreview,
    VideoPreviewRef,
    AudioPreview,
    AudioPreviewRef,
    CannotPreview,
    CannotPreviewRef,
  }
}
