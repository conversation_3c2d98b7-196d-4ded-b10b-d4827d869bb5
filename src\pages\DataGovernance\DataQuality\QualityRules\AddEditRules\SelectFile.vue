<template>
  <ElDialog
    title="选择文件"
    v-model="dialogFormVisible"
    top="10vh"
    width="900px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="tableList">
      <div class="search">
        <el-input
          class="form-item"
          v-model="ruleForm.fileName"
          placeholder="请输入规则名称"
          @keydown.enter="handleSearch"
        >
          <template #suffix>
            <SvgIcon @click="handleSearch" style="cursor: pointer" name="search" />
          </template>
        </el-input>
      </div>
      <div class="table">
        <el-table
          header-cell-class-name="common-table-header"
          cell-class-name="common-table-cell"
          ref="multipleTableRef"
          :data="tableData"
          height="100%"
          style="width: 100%"
          row-key="tid"
          default-expand-all
        >
          <el-table-column width="50">
            <template #default="scope">
              <ElRadio v-model="ruleForm.userFileId" :value="scope.row.tid" />
            </template>
          </el-table-column>
          <el-table-column width="50">
            <template #default="scope">
              <div
                v-if="scope.row"
                :style="{ backgroundImage: `url(${getFileIcon(scope.row)})` }"
                class="imgUrl"
                style="cursor: pointer"
              ></div>
            </template>
          </el-table-column>
          <el-table-column property="fileName" label="文件名称" show-overflow-tooltip>
            <template #default="scope">
              <span class="fileNmae">{{ getFileNameComplete(scope.row) }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="pagination">
        <el-pagination
          v-model:currentPage="pageData.page"
          v-model:page-size="pageData.limit"
          :page-sizes="pageData.pageSizes"
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="pageData.total"
          @size-change="pageData.handleSizeChange"
          @current-change="pageData.handleCurrentChange"
        />
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="setDialogFormVisible(false)">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </span>
    </template>
  </ElDialog>
</template>
<script setup lang="ts">
import { userFileListApi } from '@/api/data_catalog'
import { uqaRuleValidateApi } from '@/api/data_governance'
import useDialogForm from '@/hooks/useDialogForm'
import usePageData from '@/hooks/usePageData'
import { getFileIcon, getFileNameComplete } from '@/utils/fileUtils'
import { ElDialog, ElMessage, ElRadio } from 'element-plus'
import { ref } from 'vue'

const { pageData, tableData } = usePageData(userFileListApi, false)
const { dialogFormVisible, setDialogFormVisible } = useDialogForm()
const ruleForm = ref<any>({})

const handleClose = () => {
  ruleForm.value = {}
}

const handleSubmit = async () => {
  if (!ruleForm.value.userFileId) return ElMessage.warning('请选择文件!')
  try {
    const { message, status } = await uqaRuleValidateApi(ruleForm.value)
    if ([200].includes(status)) {
      ElMessage.success(message)
      emit('getFileId', ruleForm.value.userFileId)
      setDialogFormVisible(false)
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
  try {
  } catch (error) {}
}

const handleOpen = async (fileTypeId: any, url: string) => {
  if (!fileTypeId) return ElMessage.warning('请选择使用数据!')
  if (!url) return ElMessage.warning('请输入规则接口地址!')
  ruleForm.value.fileTypeId = fileTypeId
  ruleForm.value.url = url
  await pageData.handleSearch({ fileTypeId }, 1)
  setDialogFormVisible(true)
}

const handleSearch = () => {
  pageData.handleSearch(ruleForm.value, 1)
}

defineExpose({ handleOpen })
const emit = defineEmits<{ getFileId: [fileId: string] }>()
</script>
<style lang="less" scoped>
.tableList {
  height: 60vh;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  .search {
    margin-bottom: 15px;
    width: 300px;
  }
  .table {
    flex: 1;
    box-sizing: border-box;
    overflow: hidden;
    .imgUrl {
      height: 30px;
      width: 30px;
      background-size: 45px 45px;
      // background-size: contain;
      background-position: center;
      background-repeat: no-repeat;
    }
  }
  .pagination {
    display: flex;
    justify-content: flex-end;
    margin-top: 15px;
  }
}
</style>
