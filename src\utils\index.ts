import dayjs from 'dayjs'
import duration from 'dayjs/plugin/duration'

dayjs.extend(duration)

/**
 * 将秒转化为时分秒
 * @param {number} duration 总秒数
 */

export const transferSecondsToTime = (durationInMs: number, timestamp: any = 'seconds') => {
  return dayjs.duration(durationInMs, timestamp).format('HH:mm:ss')
}

// 通过 id 查询数组中带有 children 的数据对应的值
export const findValueByIdWithChildren = (data: any[], id: string | number) => {
  for (let i = 0; i < data.length; i++) {
    const node = data[i]
    if (node.tid === id) {
      return node
    } else if (Array.isArray(node.childVOList) && node.childVOList.length > 0) {
      const value: any = findValueByIdWithChildren(node.childVOList, id)
      if (value) {
        return value
      }
    }
  }
  return null
}

// 将具有父子关系的数据数组转换为树形结构
export function arrayToTree(items: any[]) {
  const result: any[] = [] // 存储最终的树形结构数据
  const itemMap: any = {} // 用于快速查找每个节点的映射表
  // 先遍历一次，将每个节点以其 id 作为 key 存储在 itemMap 中
  items.forEach((item) => {
    itemMap[item.id] = item
  })
  // 再次遍历数据数组，构建树形结构
  items.forEach((item) => {
    const parentItem = itemMap[item.pid] // 查找当前节点的父节点
    if (parentItem) {
      // 如果存在父节点，则将当前节点添加到父节点的 children 数组中
      ;(parentItem.children || (parentItem.children = [])).push(item)
    } else {
      // 如果不存在父节点，则将当前节点添加到结果数组中
      result.push(item)
    }
  })
  return result
}

// 获取菜单列表
export const getMenuList = (list: any[]): any[] => {
  const menuArr: Array<any> = []
  list.forEach((item) => {
    const { meta, children, name } = item
    if (meta && meta.show) {
      const { title, icon } = meta
      const route: any = { name: title, path: name, icon }
      if (children && children.length > 0) {
        route.children = getMenuList(children)
      }
      menuArr.push(route)
    }
  })
  return menuArr
}

// 处理关系图的node节点数据
export const getFilterNodeData = (nodes: any[]) => {
  return nodes.map((node: any) => {
    return {
      id: node.nodePkId,
      text: node.nodeName,
      data: {
        tid: node.tid,
        icon: `data:image/jpeg;base64,${node.icon}`,
        nodeId: node.nodeId,
        nodeParam: node.nodeParam,
        nodeStatus: node.nodeStatus,
        nodeCode: node.nodeCode,
        nodeTypeName: node.nodeTypeName,
        runTimesValue: node.runTimesValue,
        startTimeValue: node.startTimeValue,
      },
      nodeShape: node.nodeShape,
      x: node.nodeX,
      y: node.nodeY,
      fixed: node.fixed,
      width: node.width,
      height: node.height,
      color: node.color,
      borderColor: node.borderColor,
      fontColor: node.fontColor,
    }
  })
}

// 处理关系图的lines线数据
export const getFilterLineData = (lines: any[]) => {
  return lines.map((line: any) => {
    return {
      from: line.fromId,
      to: line.toId,
      text: line.lineName,
      color: line.color,
      animation: [3].includes(line.lineStatus) ? 1 : undefined,
    }
  })
}

// 健康度颜色
export const healthColor = (val: string) => {
  switch (val) {
    case '优秀':
      return '#409eff'
    case '良好':
      return '#67c23a'
    case '一般':
      return '#e6a23c'
    case '不合格':
      return '#f56c6c'
    default:
      return ''
  }
}

// 获取url参数
export const getQueryString = (serverUrl: string) => {
  const url = new URL(serverUrl)
  const params = url.searchParams
  return { url, params }
}

// 获取url指定参数
export const getUrlParam = (urlParams: string, name: string) => {
  const url = new URL(urlParams)
  const params = new URLSearchParams(url.search)
  return params.get(name)
}
