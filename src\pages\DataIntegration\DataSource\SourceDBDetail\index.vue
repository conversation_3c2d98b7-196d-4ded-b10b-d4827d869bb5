<script setup lang="ts">
import TableList from '@/pages/DataIntegration/DataSource/components/TableList.vue'
import Breadcrumb from '@/pages/DataIntegration/DataSource/components/Breadcrumb.vue'
import AsideMenu from '@/pages/DataIntegration/DataSource/components/AsideMenu.vue'
import DataBaseDetail from '@/pages/DataIntegration/DataSource/components/DataBaseDetail.vue'
import { ref } from 'vue'
import { queryByIdApi, queryListCollectionApi } from '@/api/data_source'
import { ElMessage } from 'element-plus'
import { useRoute } from 'vue-router'
import useUserInfo from '@/store/useUserInfo'

const permission = useUserInfo()

const route = useRoute()

// 获取详情
const rowData = ref<any>({})
const getDataDetail = async () => {
  try {
    const { data, status, message } = await queryByIdApi(route.query.id)
    if ([200].includes(status)) {
      rowData.value = data
    } else {
      ElMessage.error(message)
    }
  } catch (error) {
    console.error(error)
  }
}
getDataDetail()

const tableList = ref<any[]>([])
const getPgListTable = async () => {
  try {
    const { data, message, status } = await queryListCollectionApi({ dataSourceId: route.query.id })
    if ([200].includes(status)) {
      tableList.value = data
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}

getPgListTable()
</script>

<template>
  <div class="SourceDBDetail">
    <Breadcrumb />
    <div class="SourceDBDetail-main">
      <AsideMenu :table-list="tableList" />
      <div class="main-content">
        <TableList
          v-if="route.query.dbname"
          :permission="permission.hasButton('sourceDBDetail_edit')"
        />
        <DataBaseDetail v-else :row-data="rowData" :table-list="tableList" />
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.SourceDBDetail {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  &-main {
    margin-top: 15px;
    flex: 1;
    display: flex;
    box-sizing: border-box;
    overflow: hidden;
    .main-content {
      width: calc(100% - 215px);
      height: 100%;
      box-sizing: border-box;
      margin-left: 15px;
      background-color: @withe;
    }
  }
}
</style>
