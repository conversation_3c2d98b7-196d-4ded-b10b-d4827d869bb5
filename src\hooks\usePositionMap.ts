import useSocket from '@/store/useSocket'

export default function usePositionMap() {
  // 实时服务
  const socket: any = useSocket()

  const initSocket = (name: string, callback: any) => {
    const url = `/api/iot/api/v1/webSocket/target/${name}`
    socket.handleCreateSocket({
      url,
      callback: (data: any) => {
        console.log('socket', data)
        callback(data)
      },
    })
  }
  const closeSocket = () => {
    socket.handleCloseSocket()
  }

  return { initSocket, closeSocket }
}
