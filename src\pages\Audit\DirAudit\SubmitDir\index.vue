<template>
  <MainLayout>
    <template #header>
      <SearchTool @handle-search="handleSearch" />
    </template>
    <el-table
      header-cell-class-name="common-table-header"
      cell-class-name="common-table-cell"
      ref="multipleTableRef"
      :data="tableData"
      height="100%"
      style="width: 100%"
      row-key="tid"
    >
      <el-table-column :width="60">
        <template #default="scope">
          <ElTag v-if="scope.row.useStatus" effect="dark" type="primary">使用中</ElTag>
        </template>
      </el-table-column>
      <el-table-column property="version" label="目录版本" show-overflow-tooltip>
        <template #default="scope">
          {{ scope.row.version ? `V${scope.row.version}` : '--' }}
        </template>
      </el-table-column>
      <el-table-column property="applyDesc" label="描述" show-overflow-tooltip />
      <el-table-column property="authStatusStr" label="审核状态" show-overflow-tooltip />
      <el-table-column property="authDesc" label="发布状态" show-overflow-tooltip>
        <template #default="scope">
          {{ scope.row.release ? '已发布' : '未发布' }}
        </template>
      </el-table-column>
      <el-table-column property="applyUname" label="申请人" show-overflow-tooltip>
        <template #default="scope">
          {{ scope.row.applyUname || '--' }}
        </template>
      </el-table-column>
      <el-table-column property="applyTime" label="申请时间" show-overflow-tooltip />
      <el-table-column property="authUname" label="审核人" show-overflow-tooltip>
        <template #default="scope">
          {{ scope.row.authUname ? scope.row.authUname : '--' }}
        </template>
      </el-table-column>
      <el-table-column property="authTime" label="审核时间" show-overflow-tooltip>
        <template #default="scope">
          {{ scope.row.authTime ? scope.row.authTime : '--' }}
        </template>
      </el-table-column>
      <el-table-column
        v-if="permission.hasButton(['submitDir_details', 'submitDir_cancelAudit', 'submitDir_del'])"
        label="操作"
        width="130"
        fixed="right"
      >
        <template #default="scope">
          <el-button
            v-if="permission.hasButton('submitDir_details')"
            @click="VersionInfoDialogRef?.handleOpen(scope.row)"
            title="详情"
            class="common-icon-btn"
            type="primary"
            plain
            link
          >
            <template #icon>
              <SvgIcon name="details" />
            </template>
          </el-button>
          <el-button
            v-if="
              [0].includes(scope.row.authStatus) && permission.hasButton('submitDir_cancelAudit')
            "
            @click="CancelDialogRef?.handleOpen(scope.row.tid)"
            title="取消审核"
            class="common-icon-btn"
            type="primary"
            plain
            link
          >
            <template #icon>
              <SvgIcon name="cancelAudit" />
            </template>
          </el-button>
          <el-button
            v-if="[3].includes(scope.row.authStatus) && permission.hasButton('submitDir_del')"
            @click="handleDel(scope.row.tid)"
            title="删除"
            class="common-icon-btn"
            type="danger"
            plain
            link
          >
            <template #icon>
              <SvgIcon name="delete" />
            </template>
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <template #footer>
      <el-pagination
        v-model:currentPage="pageData.page"
        v-model:page-size="pageData.limit"
        :page-sizes="pageData.pageSizes"
        background
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageData.total"
        @size-change="pageData.handleSizeChange"
        @current-change="pageData.handleCurrentChange"
      />
    </template>
    <CancelDialog @handle-refresh="pageData.handleSearch(null, 1)" ref="CancelDialogRef" />
    <VersionInfoDialog is-approval ref="VersionInfoDialogRef" />
  </MainLayout>
</template>

<script setup lang="ts">
import MainLayout from '@/components/MainLayout/index.vue'
import SearchTool from './SearchTool.vue'
import usePageData from '@/hooks/usePageData'
import { dataCatalogRemoveByIdApi, queryApplyPageByParamApi } from '@/api/lake_catalog'
import CancelDialog from './CancelDialog.vue'
import { ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import VersionInfoDialog from '@/pages/DataCatalog/CatalogConfig/CatalogVersion/VersionInfoDialog.vue'
import useUserInfo from '@/store/useUserInfo'

const permission = useUserInfo()

const { tableData, pageData } = usePageData(queryApplyPageByParamApi)

const CancelDialogRef = ref<InstanceType<typeof CancelDialog>>()

const VersionInfoDialogRef = ref<InstanceType<typeof VersionInfoDialog>>()

const handleSearch = (data: any) => {
  pageData.handleSearch({ ...data }, 1)
}

const handleDel = (tid: any) => {
  ElMessageBox.confirm('确定要删除该条申请记录吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    try {
      const { message, status } = await dataCatalogRemoveByIdApi(tid)
      if ([200].includes(status)) {
        ElMessage.success(message)
        pageData.handleSearch(null, 1)
      } else {
        ElMessage.error(message)
      }
    } catch (error) {}
  })
}
</script>

<style lang="less" scoped></style>
