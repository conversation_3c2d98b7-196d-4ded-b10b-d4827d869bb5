import { ref, shallowRef } from 'vue'
import * as THREE from 'three'
import { FBXLoader } from 'three/addons/loaders/FBXLoader.js'
import { OBJLoader } from 'three/addons/loaders/OBJLoader.js'
import { GLTFLoader } from 'three/addons/loaders/GLTFLoader.js'
import { TDSLoader } from 'three/addons/loaders/TDSLoader.js'
import { ColladaLoader } from 'three/addons/loaders/ColladaLoader.js'
import { OrbitControls } from 'three/addons/controls/OrbitControls.js'
import { PLYLoader } from 'three/addons/loaders/PLYLoader.js'
import { ElLoading } from 'element-plus'
export default function useThree() {
  const tipText = '模型正在加载中，请稍等...'
  const threeRef = ref<HTMLDivElement>()
  const scene = shallowRef<THREE.Scene>()
  const camera = shallowRef<THREE.PerspectiveCamera>()
  const renderer = shallowRef<THREE.WebGLRenderer>()

  const initThree = async (url: string, format: string) => {
    scene.value = new THREE.Scene()

    scene.value.background = new THREE.Color(0x383838)

    camera.value = new THREE.PerspectiveCamera(
      75,
      window.innerWidth / window.innerHeight,
      0.1,
      1000,
    )
    renderer.value = new THREE.WebGLRenderer()
    renderer.value.setSize(window.innerWidth, window.innerHeight)
    threeRef.value?.appendChild(renderer.value.domElement)

    // 添加立体效果
    var controls = new OrbitControls(camera.value, renderer.value.domElement)
    // controls.enableRotate = true // 启用旋转功能
    // controls.enableZoom = true // 启用缩放功能
    // controls.enablePan = true // 禁用平移功能
    camera.value.position.z = 5

    // 添加光源
    var ambientLight = new THREE.AmbientLight(0xffffff)
    scene.value.add(ambientLight)
    var light = new THREE.DirectionalLight(0xffffff)
    light.position.set(0, 1, 1).normalize()
    scene.value.add(light)

    // 加载相应的文件服务
    if (['dae'].includes(format)) {
      loadCollada(url)
    } else if (['fbx'].includes(format)) {
      loadFbx(url)
    } else if (['obj'].includes(format)) {
      loadObj(url)
    } else if (['gltf', 'glb'].includes(format)) {
      loadGltf(url)
    } else if (['3ds'].includes(format)) {
      load3ds(url)
    } else if (['ply'].includes(format)) {
      loadPly(url)
    }

    function animate() {
      requestAnimationFrame(animate)
      controls.update()
      renderer.value?.render(scene.value as THREE.Scene, camera.value as THREE.PerspectiveCamera)
    }

    animate()
  }

  const loadPly = (url: string) => {
    const loading = ElLoading.service({
      lock: true,
      text: tipText,
      background: 'rgba(0, 0, 0, 0.7)',
    })
    const loader = new PLYLoader()
    loader.load(
      `${import.meta.env.VITE_BASE_SERVER}/${url}`,
      (geometry) => {
        const material: any = new THREE.PointsMaterial({ size: 0.01 })
        material.vertexColors = geometry.attributes && geometry.attributes['color']
        const points = new THREE.Points(geometry, material)
        scene.value?.add(points)
        const bbox = new THREE.Box3().setFromObject(points)
        const center = bbox.getCenter(new THREE.Vector3())
        const size = bbox.getSize(new THREE.Vector3())
        if (camera.value) {
          camera.value.position.copy(center)
          camera.value.position.x += size.x / 2.0
          camera.value.position.y += size.y / 2.0
          camera.value.position.z += size.z / 2.0
          camera.value.lookAt(center)
        }
        loading.close()
      },
      undefined,
      () => {
        loading.close()
      },
    )
  }

  const load3ds = (url: string) => {
    const loading = ElLoading.service({
      lock: true,
      text: tipText,
      background: 'rgba(0, 0, 0, 0.7)',
    })
    const loader = new TDSLoader()
    loader.load(
      `${import.meta.env.VITE_BASE_SERVER}/${url}`,
      (obj) => {
        // obj.position.set(0, 0, 0)
        // obj.scale.set(0.1, 0.1, 0.1)

        // 计算模型的包围盒
        var box = new THREE.Box3().setFromObject(obj)

        // 计算模型的尺寸和中心
        var size = new THREE.Vector3()
        box.getSize(size)
        var center = new THREE.Vector3()
        box.getCenter(center)

        // 设置模型中心为原点
        obj.position.sub(center)

        // 计算模型的最大尺寸
        var maxDim = Math.max(size.x, size.y, size.z)
        var scaleFactor = 1 // 这里选择10作为调整的基准值，可以根据实际情况调整
        if (maxDim > 1000) {
          scaleFactor = scaleFactor / 300
        } else {
          scaleFactor = 0.1
        }
        obj.position.set(0, 0, 0)
        // 设置缩放因子使模型适应视口
        obj.scale.set(scaleFactor, scaleFactor, scaleFactor)

        scene.value?.add(obj)
        loading.close()
      },
      undefined,
      () => {
        loading.close()
      },
    )
  }

  const loadCollada = (url: string) => {
    const loading = ElLoading.service({
      lock: true,
      text: tipText,
      background: 'rgba(0, 0, 0, 0.7)',
    })
    const loader = new ColladaLoader()
    loader.load(
      `${import.meta.env.VITE_BASE_SERVER}/${url}`,
      (obj) => {
        obj.scene.position.set(0, 0, 0)
        scene.value?.add(obj.scene)
        loading.close()
      },
      undefined,
      () => {
        loading.close()
      },
    )
  }

  const loadGltf = (url: string) => {
    const loading = ElLoading.service({
      lock: true,
      text: tipText,
      background: 'rgba(0, 0, 0, 0.7)',
    })
    const loader = new GLTFLoader()
    loader.load(
      `${import.meta.env.VITE_BASE_SERVER}/${url}`,
      (gltf) => {
        scene.value?.add(gltf.scene)
        gltf.scene.position.set(0, 0, 0)
        loading.close()
      },
      undefined,
      () => {
        loading.close()
      },
    )
  }

  const loadObj = (url: string) => {
    const loading = ElLoading.service({
      lock: true,
      text: tipText,
      background: 'rgba(0, 0, 0, 0.7)',
    })
    const loader = new OBJLoader()
    loader.load(
      `${import.meta.env.VITE_BASE_SERVER}/${url}`,
      (obj) => {
        scene.value?.add(obj)
        obj.position.set(0, 0, 0)
        loading.close()
      },
      undefined,
      () => {
        loading.close()
      },
    )
  }

  const loadFbx = (url: string) => {
    const loading = ElLoading.service({
      lock: true,
      text: tipText,
      background: 'rgba(0, 0, 0, 0.7)',
    })
    const loader = new FBXLoader()
    loader.load(
      `${import.meta.env.VITE_BASE_SERVER}/${url}`,
      (fbx) => {
        scene.value?.add(fbx)
        fbx.position.set(0, 0, 0)
        fbx.scale.set(0.1, 0.1, 0.1)
        loading.close()
      },
      undefined,
      () => {
        loading.close()
      },
    )
  }

  function destroyThree() {
    scene.value?.children?.forEach((child) => {
      scene.value?.remove(child)
    })

    // Dispose renderer resources
    renderer.value?.dispose()
  }

  return { threeRef, initThree, destroyThree }
}
