<template>
  <el-form ref="ruleFormRef" :model="ruleForm">
    <div class="search-content">
      <div class="search-input">
        <div class="search-item">
          <el-form-item prop="dataSourceId">
            <SelectDataSource
              :value="ruleForm.dataSourceId"
              :name="ruleForm.datasourceName"
              :onChange="handleDataSourceChange"
            />
          </el-form-item>
        </div>
        <div class="search-item">
          <el-form-item prop="taskStatus">
            <el-select
              class="form-item"
              style="width: 100%"
              v-model="ruleForm.taskStatus"
              placeholder="请选择采集状态"
              clearable
              @change="handleSearch"
            >
              <el-option
                v-for="p in taskStatusCodeOptions"
                :key="p.key"
                :label="p.name"
                :value="p.key"
              />
            </el-select>
          </el-form-item>
        </div>
        <div class="search-item">
          <el-form-item prop="dataType">
            <el-select
              class="form-item"
              style="width: 100%"
              v-model="ruleForm.dataType"
              placeholder="请选择入库方式"
              clearable
              @change="handleSearch"
            >
              <el-option label="数据入仓" :value="1" />
              <el-option label="数据入湖" :value="2" />
            </el-select>
          </el-form-item>
        </div>
        <div class="search-item">
          <el-form-item prop="uid">
            <el-select
              class="form-item"
              style="width: 100%"
              v-model="ruleForm.uid"
              placeholder="请选择创建用户"
              clearable
              @change="handleSearch"
            >
              <el-option v-for="p in userData" :key="p.tid" :label="p.name" :value="p.tid" />
            </el-select>
          </el-form-item>
        </div>
        <div class="search-item">
          <el-form-item prop="dateRange">
            <el-date-picker
              @change="handleSearch"
              style="width: 100%"
              value-format="YYYY-MM-DD HH:mm:ss"
              v-model="ruleForm.dateRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              clearable
            />
          </el-form-item>
        </div>
      </div>
      <div v-if="permission.hasButton('dataGather_add')" class="search-btn">
        <el-button type="primary" @click="emit('handleCreate')"> 数据采集 </el-button>
      </div>
    </div>
  </el-form>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'
import { ElMessage, FormInstance } from 'element-plus'
import { DicType } from '@/utils/constant'
import useGlobalData from '@/store/useGlobalData'
import SelectDataSource from '@/components/SelectDataSource/index.vue'
import useUserInfo from '@/store/useUserInfo'
import { queryCreateUsersApi } from '@/api/online_model.ts'

const permission = useUserInfo()

// 获取用户
const userData = ref<any[]>([])
const getUserData = async () => {
  try {
    const { data, status, message } = await queryCreateUsersApi({ taskTypes: 1 })
    if ([200].includes(status)) {
      userData.value = data
    } else {
      ElMessage.error(message)
    }
  } catch (e) {}
}
getUserData()

// 字典值
const global = useGlobalData()
const taskStatusCodeOptions = global.getTypeData(DicType.TaskStatusCode)

const ruleFormRef = ref<FormInstance>()
const ruleForm = reactive<any>({})

const handleDataSourceChange = (p: any) => {
  ruleForm.dataSourceId = p?.tid
  ruleForm.datasourceName = p?.datasourceName
  handleSearch()
}

const handleSearch = () => {
  emit('handleSearch', {
    ...ruleForm,
    startTime: ruleForm.dateRange && ruleForm.dateRange.length ? ruleForm.dateRange[0] : undefined,
    endTime: ruleForm.dateRange && ruleForm.dateRange.length ? ruleForm.dateRange[1] : undefined,
  })
}

const emit = defineEmits<{
  (e: 'handleSearch', form: any): void
  (e: 'handleCreate'): void
}>()
</script>
