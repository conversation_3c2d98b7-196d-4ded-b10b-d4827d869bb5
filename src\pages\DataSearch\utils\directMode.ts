import MapboxDraw from '@mapbox/mapbox-gl-draw'

import distance from '@turf/distance'

import * as turfHelpers from '@turf/helpers'

import circle from '@turf/circle'

import { createSupplementaryPointsForCircle } from './common'

const createSupplementaryPoints = MapboxDraw.lib.createSupplementaryPoints

const moveFeatures = MapboxDraw.lib.moveFeatures

const Constants = MapboxDraw.constants

const constrainFeatureMovement = MapboxDraw.lib.constrainFeatureMovement

const DirectModeOverride: any = MapboxDraw.modes.direct_select

DirectModeOverride.dragFeature = function (state: any, e: any, delta: any) {
  moveFeatures(this.getSelected(), delta)
  this.getSelected()
    .filter((feature: any) => feature.properties.isCircle)
    .map((circle: any) => circle.properties.center)
    .forEach((center: any) => {
      center[0] += delta.lng
      center[1] += delta.lat
    })
  state.dragMoveLocation = e.lngLat
}

DirectModeOverride.dragVertex = function (state: any, e: any, delta: any) {
  if (state.feature.properties.isCircle) {
    const center = state.feature.properties.center
    const movedVertex = [e.lngLat.lng, e.lngLat.lat]
    const radius: any = distance(turfHelpers.point(center), turfHelpers.point(movedVertex), {
      units: 'kilometers',
    })
    const circleFeature = circle(center, radius)
    state.feature.incomingCoords(circleFeature.geometry.coordinates)
    state.feature.properties.radiusInKm = radius
  } else {
    const selectedCoords = state.selectedCoordPaths.map((coord_path: any) =>
      state.feature.getCoordinate(coord_path),
    )
    const selectedCoordPoints = selectedCoords.map((coords: any) => ({
      type: Constants.geojsonTypes.FEATURE,
      properties: {},
      geometry: {
        type: Constants.geojsonTypes.POINT,
        coordinates: coords,
      },
    }))

    const constrainedDelta: any = constrainFeatureMovement(selectedCoordPoints, delta)
    for (let i = 0; i < selectedCoords.length; i++) {
      const coord = selectedCoords[i]
      state.feature.updateCoordinate(
        state.selectedCoordPaths[i],
        coord[0] + constrainedDelta.lng,
        coord[1] + constrainedDelta.lat,
      )
    }
  }
}

DirectModeOverride.toDisplayFeatures = function (state: any, geojson: any, push: any) {
  if (state.featureId === geojson.properties.id) {
    geojson.properties.active = Constants.activeStates.ACTIVE
    push(geojson)
    const supplementaryPoints: any = geojson.properties.user_isCircle
      ? createSupplementaryPointsForCircle(geojson)
      : createSupplementaryPoints(geojson, {
          midpoints: true,
          selectedPaths: state.selectedCoordPaths,
        })
    supplementaryPoints.forEach(push)
  } else {
    geojson.properties.active = Constants.activeStates.INACTIVE
    push(geojson)
  }
  this.fireActionable(state)
}

export default DirectModeOverride
