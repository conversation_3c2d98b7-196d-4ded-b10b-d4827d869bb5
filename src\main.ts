import { createApp } from 'vue'
import 'virtual:svg-icons-register'
import '@/styles/index.less'
import App from '@/App.vue'
import router from '@/router'
import store from '@/store'
import SvgIcon from '@/components/SvgIcon/index.vue'
import handleRouterPermission from '@/utils/routerAuth'

const app = createApp(App)
app.use(router)
app.use(store)
app.component('SvgIcon', SvgIcon)

// 处理权限登录逻辑
handleRouterPermission(router)

app.mount('#app')
