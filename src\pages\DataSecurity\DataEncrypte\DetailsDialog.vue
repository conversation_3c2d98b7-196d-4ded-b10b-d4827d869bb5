<template>
  <ElDialog
    v-model="dialogFormVisible"
    top="10vh"
    title="查看密码"
    width="400px"
    :close-on-click-modal="false"
    @close="handleDialogClose"
  >
    <el-form ref="formRef" :model="formData" label-width="auto">
      <el-form-item label="密码" prop="password">
        {{ formData.password }}
      </el-form-item>
    </el-form>
  </ElDialog>
</template>
<script setup lang="ts">
import { queryByUserFileIdApi } from '@/api/data_security'
import useDialogForm from '@/hooks/useDialogForm'
import { ElDialog, ElMessage } from 'element-plus'
import { ref } from 'vue'

const { dialogFormVisible, setDialogFormVisible, formRef, handleDialogClose } = useDialogForm()

const formData = ref<any>({})
const handleOpen = async (row: any) => {
  try {
    const { data, message, status } = await queryByUserFileIdApi(row.tid)
    if ([200].includes(status)) {
      formData.value = data
      setDialogFormVisible(true)
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}

defineExpose({ handleOpen })
</script>
<style lang="less" scoped></style>
