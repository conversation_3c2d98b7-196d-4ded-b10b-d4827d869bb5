<template>
  <ElDialog
    v-model="dialogFormVisible"
    top="10vh"
    width="500px"
    title="取消申请"
    :close-on-click-modal="false"
  >
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="auto">
      <el-form-item label="审核人">
        {{ permission.userInfo?.name }}
      </el-form-item>
      <el-form-item label="取消描述" prop="cancelDesc">
        <el-input
          v-model="formData.cancelDesc"
          :rows="3"
          type="textarea"
          placeholder="请填写取消描述信息"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="setDialogFormVisible(false)">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </span>
    </template>
  </ElDialog>
</template>
<script setup lang="ts">
import { modifyCancelApplyApi } from '@/api/lake_catalog'
import useDialogForm from '@/hooks/useDialogForm'
import useUserInfo from '@/store/useUserInfo'
import { ElMessage } from 'element-plus'
import { ref } from 'vue'

const permission = useUserInfo()

const { dialogFormVisible, setDialogFormVisible, formRef, validateForm } = useDialogForm()
const formData = ref<any>({ cancelDesc: '' })

const rules = ref<any>({
  cancelDesc: [{ required: true, message: '取消描述信息必填！', trigger: 'blur' }],
})

const handleSubmit = async () => {
  try {
    await validateForm()
    const { status, message } = await modifyCancelApplyApi({
      ...formData.value,
    })
    if ([200].includes(status)) {
      ElMessage.success(message)
      setDialogFormVisible(false)
      emit('handleRefresh')
    } else {
      ElMessage.error(message)
    }
  } catch (error) {
    console.error(error)
  }
}

const handleOpen = (tid: string) => {
  formData.value = { tid }
  setDialogFormVisible(true)
}

defineExpose({ handleOpen })

const emit = defineEmits<{
  (e: 'handleRefresh'): void
}>()
</script>
