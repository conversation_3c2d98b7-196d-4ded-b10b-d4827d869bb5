<template>
  <el-dialog
    v-model="dialogFormVisible"
    top="10vh"
    title="新增会员"
    width="500px"
    :close-on-click-modal="false"
    @close="handleDialogClose"
  >
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="80px">
      <el-form-item label="域名" prop="customerName">
        <el-input v-model="formData.customerName" placeholder="请输入域名" />
      </el-form-item>
      <el-form-item label="访问秘钥" prop="customerName">
        <el-input v-model="formData.customerName" placeholder="请输入访问秘钥" />
      </el-form-item>
      <el-form-item label="安全秘钥" prop="customerName">
        <el-input v-model="formData.customerName" placeholder="请输入安全秘钥" />
      </el-form-item>
      <el-form-item label="桶名" prop="customerName">
        <el-input v-model="formData.customerName" placeholder="请输入桶名" />
      </el-form-item>
      <el-form-item label="描述" prop="customerName">
        <el-input
          :rows="4"
          type="textarea"
          v-model="formData.customerName"
          placeholder="请输入桶名"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" @click="handleSubmit">测试</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { reactive } from 'vue'
import useDialogForm from '@/hooks/useDialogForm'

const { dialogFormVisible, setDialogFormVisible, handleDialogClose, formRef, validateForm } =
  useDialogForm()

const formData = reactive<any>({})
const handleOpen = (row: any) => {
  if (row) {
    formData.value = { ...row }
  }
  setDialogFormVisible(true)
}

const handleSubmit = async () => {
  try {
    await validateForm()
  } catch (error) {
    console.error(error)
  }
}

const rules = reactive<any>({})

defineExpose({ handleOpen })
</script>

<style scoped lang="less"></style>
