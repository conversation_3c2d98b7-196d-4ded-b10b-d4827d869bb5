<template>
  <el-form ref="ruleFormRef" :model="ruleForm" label-width="80px">
    <div class="search-content">
      <div class="search-input">
        <div class="search-item">
          <el-form-item label="任务名称" prop="likeTaskName">
            <el-input
              class="form-item"
              v-model="ruleForm.likeTaskName"
              placeholder="请输入任务名称"
              @keydown.enter="emit('handleSearch', ruleForm)"
            >
              <template #suffix>
                <SvgIcon
                  @click="emit('handleSearch', ruleForm)"
                  style="cursor: pointer"
                  name="search"
                />
              </template>
            </el-input>
          </el-form-item>
        </div>
        <div class="search-item">
          <el-form-item label="任务状态" prop="taskStatus">
            <el-select
              class="form-item"
              style="width: 100%"
              v-model="ruleForm.taskStatus"
              placeholder="请选择任务状态"
              clearable
              @change="emit('handleSearch', ruleForm)"
            >
              <el-option
                v-for="p in taskStatusCodeOptions"
                :key="p.key"
                :label="p.name"
                :value="p.key"
              />
            </el-select>
          </el-form-item>
        </div>
        <div class="search-item">
          <el-form-item label="创建时间" prop="mobile">
            <el-date-picker
              @change="emit('handleSearch', ruleForm)"
              style="width: 100%"
              value-format="YYYY-MM-DD HH:mm:ss"
              v-model="ruleForm.dateRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              clearable
            />
          </el-form-item>
        </div>
      </div>
    </div>
  </el-form>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'
import type { FormInstance } from 'element-plus'
import { DicType } from '@/utils/constant'
import useGlobalData from '@/store/useGlobalData'

// 字典值
const global = useGlobalData()
const taskStatusCodeOptions = global.getTypeData(DicType.TaskStatusCode)

const ruleFormRef = ref<FormInstance>()
const ruleForm = reactive<any>({})

const emit = defineEmits<{
  (e: 'handleSearch', form: any): void
}>()
</script>
