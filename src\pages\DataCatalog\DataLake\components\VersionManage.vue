<template>
  <transition name="fade">
    <div v-if="showViewer" class="version-manage common-preview-wrapper">
      <div class="tip-wrapper">
        <div></div>
        <p>版本管理</p>
        <div class="tip-right">
          <SvgIcon name="closeBold" @click="handleClose" class="close-icon" />
        </div>
      </div>
      <div class="main-wrapper">
        <el-table header-cell-class-name="common-table-header" cell-class-name="common-table-cell"
          ref="multipleTableRef" :data="tableData" height="100%" style="width: 100%" row-key="tid">
          <el-table-column width="50">
            <template #default="scope">
              <img v-if="scope.row" :src="getFileIcon(scope.row)" width="30" height="30" alt="img"
                style="cursor: pointer" />
            </template>
          </el-table-column>
          <el-table-column property="fileName" label="文件名称" show-overflow-tooltip>
            <template #default="scope">
              <span class="fileNmae">{{ getFileNameComplete(scope.row) }}</span>
            </template>
          </el-table-column>
          <el-table-column property="versionNo" label="版本号" width="70">
            <template #default="scope">
              {{ `v${scope.row.versionNo}` }}
            </template>
          </el-table-column>
          <el-table-column prop="serverStatusName" label="健康度" width="70">
            <template #default="scope">
              <span :style="{ color: healthColor(scope.row.scoreLevel) }">{{
                scope.row.scoreLevel || '--'
                }}</span>
            </template>
          </el-table-column>
          <el-table-column property="suffix" label="数据格式" width="80" />
          <el-table-column property="isPublic" label="是否公开" width="80">
            <template #default="scope">{{
              [1].includes(scope.row.isPublic) ? '是' : '否'
              }}</template>
          </el-table-column>
          <el-table-column property="fileSize" label="文件大小" show-overflow-tooltip width="100">
            <template #default="scope">{{
              [1].includes(scope.row.isDir) ? '/' : calculateFileSize(Number(scope.row.fileSize))
              }}</template>
          </el-table-column>
          <el-table-column property="createTime" label="生成时间" width="180" />
          <el-table-column label="操作" width="150" fixed="right">
            <template #default="scope">
              <el-button @click="emit('handleClick', 'handleEdit', scope.row, false)" class="common-icon-btn"
                type="primary" plain link title="编辑">
                <template #icon>
                  <SvgIcon name="edit" />
                </template>
              </el-button>
              <el-button v-if="[1].includes(scope.row.serverStatus)"
                @click="emit('handleClick', 'handleShareServe', scope.row)" class="common-icon-btn" type="primary" plain
                link title="服务地址">
                <template #icon>
                  <SvgIcon name="address" />
                </template>
              </el-button>
              <el-button @click="emit('handleClick', 'handleDownload', scope.row)" class="common-icon-btn"
                type="primary" plain link title="下载">
                <template #icon>
                  <SvgIcon name="download" />
                </template>
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </transition>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { getFileIcon, calculateFileSize, getFileNameComplete } from '@/utils/fileUtils'
import { healthColor } from '@/utils'
import { userFileVersionListApi } from '@/api/data_catalog'
import { ElMessage } from 'element-plus'

const showViewer = ref<boolean>(false)
const handleClose = () => {
  showViewer.value = false
}

const tableData = ref<any[]>([])
const handleOpen = async ({ tid }: any) => {
  try {
    const { data, status, message } = await userFileVersionListApi({ userFileId: tid })
    if ([200].includes(status)) {
      tableData.value = data
      showViewer.value = true
    } else {
      ElMessage.error(message)
    }
  } catch (error) { }
}

const emit = defineEmits<{
  handleClick: [name: string, row: any, isEdit?: boolean]
}>()

defineExpose({ handleOpen })
</script>

<style scoped lang="less">
.version-manage {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;

  .tip-wrapper {
    background: rgba(0, 0, 0, 1);
    padding: 0 48px;
    width: 100%;
    height: 48px;
    line-height: 48px;
    color: @withe;
    font-size: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-sizing: border-box;

    .tip-right {
      display: flex;
      height: 100%;
      align-items: center;

      .close-icon {
        cursor: pointer;
      }
    }
  }

  .main-wrapper {
    flex: 1;
    background-color: @withe;
    margin: 20px 190px;
    box-sizing: border-box;
    padding: 20px;
  }
}
</style>
