<template>
  <ElDialog
    v-model="dialogFormVisible"
    top="10vh"
    width="500px"
    :title="formData.type ? '复制目录' : '移动目录'"
    :close-on-click-modal="false"
    @close="handleDialogClose"
  >
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="auto">
      <ElFormItem label="数据目录" prop="dataCatalogId">
        <ElTreeSelect
          @change="handleChange"
          v-model="formData.dataCatalogId"
          :data="dataSource"
          check-strictly
          :props="{
            label: 'catalogAlias',
            value: 'tid',
            children: 'childVOList',
            disabled: 'disabled',
          }"
          value-key="tid"
          style="width: 100%"
        />
      </ElFormItem>
      <ElFormItem label="文件夹" prop="filePath">
        <ElTreeSelect
          v-model="formData.filePath"
          :data="dirList"
          check-strictly
          :props="{
            label: 'filePath',
            value: 'filePath',
            children: 'childList',
          }"
          value-key="filePath"
          style="width: 100%"
        />
      </ElFormItem>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="setDialogFormVisible(false)">取 消</el-button>
        <el-button type="primary" :loading @click="handleSubmit">确 定</el-button>
      </span>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
import { configQueryListApi } from '@/api/catalog_config'
import { batchCopyApi, batchMoveApi, getDirTreeApi } from '@/api/data_catalog'
import useDialogForm from '@/hooks/useDialogForm'
import { ElDialog, ElMessage } from 'element-plus'
import { ref } from 'vue'

const dataSource = ref<any[]>([])

const getTableData = async () => {
  try {
    const { data, status, message } = await configQueryListApi({ dataType: 2 })
    if (status === 200) {
      dataSource.value = data
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}
getTableData()

const handleChange = (val: any) => {
  formData.value.filePath = ''
  getDirList(val)
}

const dirList = ref<any[]>([])
const getDirList = async (dataCatalogId: string) => {
  try {
    const { data, message, status } = await getDirTreeApi({ dataCatalogId })
    if (status === 200) {
      dirList.value = [data]
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}

const { dialogFormVisible, handleDialogClose, setDialogFormVisible, formRef, validateForm } =
  useDialogForm()

const formData = ref<any>({})

const rules = ref<any>({
  dataCatalogId: [{ required: true, message: '数据目录必填！', trigger: 'blur' }],
  filePath: [{ required: true, message: '文件路径必填！', trigger: 'blur' }],
})

const handleOpen = (tidList: any[], type: number) => {
  formData.value = { tidList, type }
  setDialogFormVisible(true)
}

const loading = ref<boolean>(false)
const handleSubmit = async () => {
  try {
    await validateForm()
    const { status, message } = formData.value.type
      ? await batchCopyApi(formData.value)
      : await batchMoveApi(formData.value)
    if (status === 200) {
      ElMessage.success('操作成功！')
      emit('handleRefresh')
      setDialogFormVisible(false)
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}

defineExpose({ handleOpen })

const emit = defineEmits<{
  (e: 'handleRefresh'): void
}>()
</script>

<style lang="less" scoped></style>
