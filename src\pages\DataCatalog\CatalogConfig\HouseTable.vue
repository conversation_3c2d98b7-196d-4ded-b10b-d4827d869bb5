<template>
  <div class="houseTable">
    <div v-if="permission.hasButton('catalogConfig_houseAdd')" class="btn">
      <ElButton @click="AddEditDialogRef.handleOpen(null, 0)" type="primary">新建一级目录</ElButton>
    </div>
    <div class="table">
      <el-table
        v-loading="loading"
        header-cell-class-name="common-table-header"
        cell-class-name="common-table-cell"
        ref="multipleTableRef"
        :data="tableData"
        height="100%"
        style="width: 100%"
        row-key="tid"
        :tree-props="treeProps"
        default-expand-all
      >
        <el-table-column property="catalogAlias" label="目录名称" show-overflow-tooltip />
        <el-table-column property="level" label="目录级别" show-overflow-tooltip />
        <el-table-column property="catalogDesc" label="描述" show-overflow-tooltip />
        <el-table-column property="createTime" label="创建时间" show-overflow-tooltip />
        <el-table-column
          v-if="
            permission.hasButton([
              'catalogConfig_houseAdd',
              'catalogConfig_houseEdit',
              'catalogConfig_houseDel',
            ])
          "
          label="操作"
          width="130"
          fixed="right"
        >
          <template #default="scope">
            <el-button
              v-if="permission.hasButton('catalogConfig_houseAdd')"
              @click="AddEditDialogRef.handleOpen(scope.row, 2)"
              title="新建子目录"
              class="common-icon-btn"
              type="primary"
              plain
              link
            >
              <template #icon>
                <SvgIcon name="CirclePlus" />
              </template>
            </el-button>
            <el-button
              v-if="permission.hasButton('catalogConfig_houseEdit')"
              @click="AddEditDialogRef.handleOpen(scope.row, 1)"
              title="编辑"
              class="common-icon-btn"
              type="primary"
              plain
              link
            >
              <template #icon>
                <SvgIcon name="edit" />
              </template>
            </el-button>
            <el-button
              v-if="permission.hasButton('catalogConfig_houseDel')"
              @click="handleDel(scope.row.tid)"
              title="删除"
              class="common-icon-btn"
              type="danger"
              plain
              link
            >
              <template #icon>
                <SvgIcon name="delete" />
              </template>
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <AddEditDialog
      :tree-data="tableData"
      ref="AddEditDialogRef"
      :dataType="1"
      @handle-refresh="getTableData"
    />
  </div>
</template>

<script lang="ts" setup>
import usePageData from './usePageData'
import AddEditDialog from './AddEditDialog.vue'
import { ref } from 'vue'
import useUserInfo from '@/store/useUserInfo'

const permission = useUserInfo()

const { treeProps, tableData, loading, getTableData, handleDel } = usePageData(1)
const AddEditDialogRef = ref<any>()
</script>

<style lang="less" scoped>
.btn {
  text-align: right;
  background-color: @withe;
  padding: 10px 15px 0 15px;
}
.houseTable {
  height: 100%;
  display: flex;
  flex-direction: column;
  .table {
    flex: 1;
    box-sizing: border-box;
    padding: 15px;
    background-color: @withe;
  }
}
</style>
