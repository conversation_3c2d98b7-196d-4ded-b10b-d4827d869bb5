<template>
  <el-form ref="ruleFormRef" :model="ruleForm">
    <div class="search-content">
      <div class="search-input">
        <div class="search-item">
          <el-form-item prop="taskName">
            <el-input
              class="form-item"
              v-model="ruleForm.taskName"
              placeholder="请输入任务名称"
              @keydown.enter="handleSearch"
            >
              <template #suffix>
                <SvgIcon @click="handleSearch" style="cursor: pointer" name="search" />
              </template>
            </el-input>
          </el-form-item>
        </div>
        <div class="search-item">
          <el-form-item prop="taskStatus">
            <el-select
              style="width: 100%"
              @change="handleSearch"
              v-model="ruleForm.taskStatus"
              placeholder="请选择任务状态"
              clearable
            >
              <el-option v-for="p in options" :key="p.key" :label="p.name" :value="p.key" />
            </el-select>
          </el-form-item>
        </div>
        <div class="search-item">
          <el-form-item prop="dateRange">
            <el-date-picker
              @change="handleSearch"
              style="width: 100%"
              value-format="YYYY-MM-DD HH:mm:ss"
              v-model="ruleForm.dateRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              clearable
            />
          </el-form-item>
        </div>
      </div>
    </div>
  </el-form>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import type { FormInstance } from 'element-plus'
import useGlobalData from '@/store/useGlobalData'
import { DicType } from '@/utils/constant'

const global = useGlobalData()
const options: any[] = global.getTypeData(DicType.TaskStatusCode)

const ruleFormRef = ref<FormInstance>()
const ruleForm = ref<any>({})

const handleSearch = () => {
  const { dateRange, taskStatus, taskName } = ruleForm.value
  emit('handleSearch', {
    taskStatus,
    taskName,
    startTime: dateRange && dateRange.length ? dateRange[0] : undefined,
    endTime: dateRange && dateRange.length ? dateRange[1] : undefined,
  })
}

const emit = defineEmits<{
  (e: 'handleSearch', form: any): void
}>()
</script>
