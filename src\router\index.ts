import { createRouter, createWebHistory, RouteRecordRaw } from 'vue-router'
import routerPage from './routerPage'

export const mainRoutes: RouteRecordRaw[] = [
  // 首页
  {
    path: '/home',
    name: 'dm_home',
    component: routerPage.home,
    meta: {
      keepAlive: false,
      sider: true,
      requiresAuth: true,
      title: '首页',
    },
  },
  // 数据目录
  {
    path: '/dataCatalog',
    name: 'dataCatalog',
    component: routerPage.dataCatalog,
    meta: {
      sider: true,
      requiresAuth: false,
      title: '数据目录',
    },
  },
  // 数据集成
  {
    path: '/dataIntegration',
    name: 'dataIntegration',
    component: routerPage.dataIntegration,
    redirect: '/dataIntegration/dataSource',
    meta: {
      sider: true,
      requiresAuth: false,
      title: '数据集成',
    },
    children: [
      {
        path: '/dataIntegration/dataSource',
        name: 'dataSource',
        component: routerPage.dataSource,
        meta: {
          keepAlive: true,
          sider: true,
          requiresAuth: false,
          title: '数据源',
        },
      },
      {
        path: '/dataIntegration/dataSource/addEditDetail',
        name: 'dataSourceAddEditDetail',
        component: routerPage.dataSourceAddEditDetail,
        meta: {
          sider: false,
          requiresAuth: false,
          title: '数据源操作',
        },
      },
      {
        path: '/dataIntegration/dataSource/sourceDataDetail',
        name: 'sourceDataDetail',
        component: routerPage.sourceDataDetail,
        meta: {
          sider: false,
          requiresAuth: false,
          title: '数据源数据库详情',
        },
      },
      {
        path: '/dataIntegration/dataSource/sourceFileDetail',
        name: 'sourceFileDetail',
        component: routerPage.sourceFileDetail,
        meta: {
          sider: false,
          requiresAuth: false,
          title: '数据源数据库详情',
        },
      },
      {
        path: '/dataIntegration/dataSource/sourceEsDetail',
        name: 'sourceEsDetail',
        component: routerPage.sourceEsDetail,
        meta: {
          sider: false,
          requiresAuth: false,
          title: '数据源数据库详情',
        },
      },
      {
        path: '/dataIntegration/dataSource/sourceDBDetail',
        name: 'sourceDBDetail',
        component: routerPage.sourceDBDetail,
        meta: {
          sider: false,
          requiresAuth: false,
          title: '数据源数据库详情',
        },
      },
      {
        path: '/dataIntegration/dataGather',
        name: 'dataGather',
        component: routerPage.dataGather,
        meta: {
          sider: true,
          requiresAuth: false,
          title: '数据采集',
        },
      },
      {
        path: '/dataIntegration/dataGather/detail',
        name: 'dataGatherDetail',
        component: routerPage.dataGatherDetail,
        meta: {
          sider: false,
          requiresAuth: false,
          title: '数据采集详情',
        },
      },
    ],
  },
  // 时序场景
  {
    path: '/sequentialScene',
    name: 'sequentialScene',
    component: routerPage.sequentialScene,
    redirect: '/sequentialScene/sceneList',
    meta: {
      sider: true,
      requiresAuth: false,
      title: '时序场景',
    },
    children: [
      {
        path: '/sequentialScene/sceneList',
        name: 'sceneList',
        component: routerPage.sceneList,
        meta: {
          sider: true,
          requiresAuth: false,
          title: '场景列表',
        },
      },
      {
        path: '/sequentialScene/sceneStructure',
        name: 'sceneStructure',
        component: routerPage.sceneStructure,
        meta: {
          sider: true,
          requiresAuth: false,
          title: '场景构建',
        },
      },
      {
        path: '/sequentialScene/sceneList/generateVideo',
        name: 'generateVideo',
        component: routerPage.generateVideo,
        meta: {
          sider: false,
          requiresAuth: false,
          title: '生成视频',
        },
      },
    ],
  },
  // 在线建模
  {
    path: '/onlineModel',
    name: 'onlineModel',
    component: routerPage.onlineModel,
    redirect: '/onlineModel/modelBase',
    meta: {
      sider: true,
      requiresAuth: false,
      title: '在线建模',
    },
    children: [
      {
        path: '/onlineModel/modelBase',
        name: 'modelBase',
        component: routerPage.modelBase,
        meta: {
          sider: true,
          requiresAuth: false,
          title: '模型库',
        },
      },
      {
        path: '/onlineModel/createModel',
        name: 'createModel',
        component: routerPage.createModel,
        meta: {
          sider: true,
          requiresAuth: false,
          title: '在线建模',
        },
      },
      {
        path: '/onlineModel/taskManage',
        name: 'taskManage',
        component: routerPage.taskManage,
        meta: {
          sider: true,
          requiresAuth: false,
          title: '任务管理',
        },
      },
      {
        path: '/onlineModel/taskManage/taskManageDetail',
        name: 'taskManageDetail',
        component: routerPage.taskManageDetail,
        meta: {
          sider: false,
          requiresAuth: false,
          title: '任务详情',
        },
      },
      {
        path: '/onlineModel/customModel',
        name: 'customModel',
        component: routerPage.customModel,
        meta: {
          sider: true,
          requiresAuth: false,
          title: '自定义算子',
        },
      },
    ],
  },
  // 数据查询
  {
    path: '/dataSearch',
    name: 'dataSearch',
    component: routerPage.dataSearch,
    meta: {
      sider: true,
      requiresAuth: false,
      title: '数据查询',
    },
  },
  // 数据安全
  {
    path: '/dataSecurity',
    name: 'dataSecurity',
    component: routerPage.dataSecurity,
    meta: {
      sider: true,
      requiresAuth: false,
      title: '数据安全',
    },
  },
  // 数据治理
  {
    path: '/dataGovernance',
    name: 'dataGovernance',
    component: routerPage.dataGovernance,
    redirect: '/dataGovernance/dataQuality',
    meta: {
      sider: true,
      requiresAuth: false,
      title: '数据治理',
    },
    children: [
      {
        path: '/dataGovernance/dataQuality',
        name: 'dataQuality',
        component: routerPage.dataQuality,
        redirect: '/dataGovernance/dataQuality/qualityMonitor',
        meta: {
          sider: true,
          requiresAuth: false,
          title: '数据质量',
        },
        children: [
          {
            path: '/dataGovernance/dataQuality/qualityMonitor',
            name: 'qualityMonitor',
            component: routerPage.qualityMonitor,
            meta: {
              sider: true,
              requiresAuth: false,
              title: '质量监控',
            },
          },
          {
            path: '/dataGovernance/dataQuality/qualityRules',
            name: 'qualityRules',
            component: routerPage.qualityRules,
            meta: {
              sider: true,
              requiresAuth: false,
              title: '质量规则',
            },
          },
          {
            path: '/dataGovernance/dataQuality/qualityModel',
            name: 'qualityModel',
            component: routerPage.qualityModel,
            meta: {
              sider: true,
              requiresAuth: false,
              title: '质量模型',
            },
          },
          {
            path: '/dataGovernance/dataQuality/governanceRules',
            name: 'governanceRules',
            component: routerPage.governanceRules,
            meta: {
              sider: true,
              requiresAuth: false,
              title: '自动治理规则',
            },
          },
        ],
      },
      {
        path: '/dataGovernance/dataStandard',
        name: 'dataStandard',
        component: routerPage.dataStandard,
        redirect: '/dataGovernance/dataStandard/StandardManage',
        meta: {
          sider: true,
          requiresAuth: false,
          title: '数据标准',
        },
        children: [
          {
            path: '/dataGovernance/dataStandard/scoreCriteria',
            name: 'scoreCriteria',
            component: routerPage.scoreCriteria,
            meta: {
              sider: true,
              requiresAuth: false,
              title: '健康度评分标准',
            },
          },
        ],
      },
      {
        path: '/dataGovernance/dataAdminister',
        name: 'dataAdminister',
        component: routerPage.dataAdminister,
        redirect: '/dataGovernance/dataAdminister/governanceTask',
        meta: {
          sider: true,
          requiresAuth: false,
          title: '数据治理',
        },
        children: [
          {
            path: '/dataGovernance/dataAdminister/governanceTask',
            name: 'governanceTask',
            component: routerPage.governanceTask,
            meta: {
              sider: true,
              requiresAuth: false,
              title: '治理任务',
            },
          },
        ],
      },
    ],
  },

  // 系统设置
  {
    path: '/setting',
    name: 'setting',
    component: routerPage.setting,
    meta: {
      sider: false,
      requiresAuth: false,
      title: '系统设置',
    },
  },
]

const routes: RouteRecordRaw[] = [
  {
    path: '/dataRecovery',
    name: 'dataRecovery',
    component: () => import('@/pages/DataRecovery/index.vue'),
    meta: {
      requiresAuth: false,
      title: '数据恢复等待页',
    },
  },
  {
    path: '/onlyOffice',
    name: 'onlyOffice',
    component: () => import('@/pages/OnlyOffice/index.vue'),
    meta: {
      requiresAuth: false,
      title: 'onlyOffice预览',
    },
  },
  // 系统设置
  // {
  //   path: '/setting',
  //   name: 'setting',
  //   component: routerPage.setting,
  //   meta: {
  //     sider: false,
  //     requiresAuth: false,
  //     title: '系统设置',
  //   },
  // },
]

const router = createRouter({
  routes,
  history: createWebHistory(),
})

export default router
