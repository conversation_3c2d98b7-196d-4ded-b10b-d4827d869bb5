<template>
  <ElDialog
    v-model="dialogFormVisible"
    top="10vh"
    title="添加数据源"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="table-main">
      <SearchTool @handle-search="handleSearch" />
      <div class="main">
        <el-table
          v-loading="pageData.loading"
          header-cell-class-name="common-table-header"
          cell-class-name="common-table-cell"
          ref="multipleTableRef"
          :data="tableData"
          height="100%"
          style="width: 100%"
          row-key="tid"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" reserve-selection width="30" />
          <el-table-column property="adapterName" label="适配器" show-overflow-tooltip />
          <el-table-column property="datasourceName" label="数据源名称" show-overflow-tooltip />
          <el-table-column property="userName" label="用户" show-overflow-tooltip />
        </el-table>
      </div>
      <div class="footer">
        <el-pagination
          v-model:currentPage="pageData.page"
          v-model:page-size="pageData.limit"
          :page-sizes="pageData.pageSizes"
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="pageData.total"
          @size-change="pageData.handleSizeChange"
          @current-change="pageData.handleCurrentChange"
        />
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="setDialogFormVisible(false)">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </span>
    </template>
  </ElDialog>
</template>
<script setup lang="ts">
import useDialogForm from '@/hooks/useDialogForm'
import { ElDialog, ElMessage } from 'element-plus'
import { ref } from 'vue'
import SearchTool from './SearchTool.vue'
import usePageData from '@/hooks/usePageData'
import { whiteListAddApi, whiteListAddPageApi } from '@/api/gather_audit'

const { dialogFormVisible, setDialogFormVisible, handleDialogClose } = useDialogForm()

const { tableData, pageData } = usePageData(whiteListAddPageApi, false)

const handleSearch = (data: any) => {
  pageData.handleSearch({ ...(data || {}) }, 1)
}

const multipleTableRef = ref<any>()
const handleClose = () => {
  multipleTableRef.value!.clearSelection()
  handleDialogClose()
}

const handleSubmit = async () => {
  if (!selectData.value.length) return ElMessage.warning('请选择要加密的文件')
  try {
    const { message, status } = await whiteListAddApi(selectData.value)
    if ([200].includes(status)) {
      ElMessage.success(message)
      setDialogFormVisible(false)
      emit('handleRefresh')
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}

const handleOpen = async () => {
  try {
    await pageData.handleSearch(null, 1)
    setDialogFormVisible(true)
  } catch (error) {}
}

const selectData = ref<any[]>([])
const handleSelectionChange = (list: any[]) => {
  selectData.value = list
}

defineExpose({ handleOpen })

const emit = defineEmits<{
  (e: 'handleRefresh'): void
}>()
</script>
<style lang="less" scoped>
.table-main {
  height: 50vh;
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;
  .main {
    flex: 1;
    overflow: auto;
    box-sizing: border-box;
    margin: 15px 0;
  }
  .footer {
    display: flex;
    justify-content: flex-end;
  }
}
</style>
