<template>
  <li class="adapterList-item">
    <div class="row" :style="{ backgroundColor: `rgba(${color}, 1)` }"></div>
    <p
      class="title"
      :style="{ color: `rgba(${color}, 1)`, backgroundColor: `rgba(${color}, 0.1)` }"
    >
      {{ card.adapterName }}适配器
    </p>
    <p class="version">适配器版本：{{ card.adapterVersion }}</p>
    <p class="text" :title="card.adapterDesc">描述：{{ card.adapterDesc }}</p>
  </li>
</template>

<script setup lang="ts">
defineProps<{ color: string; card: any }>()
</script>

<style lang="less" scoped>
.adapterList-item {
  height: 200px;
  background-color: @withe;
  box-shadow: 0px 0px 7px 1px rgba(0, 0, 0, 0.05);
  border-radius: 4px;
  cursor: pointer;
  .row {
    height: 4px;
    border-radius: 4px 4px 0 0;
  }
  .title {
    margin: 0 24px;
    margin-top: 24px;
    display: inline-block;
    height: 36px;
    border-radius: 4px;
    padding: 0 12px;
    line-height: 36px;
    font-size: 14px;
    font-weight: 600;
  }
  .version {
    margin: 0 24px;
    margin-top: 30px;
    font-size: 14px;
    color: #333333;
  }
  .text {
    margin: 0 24px;
    margin-top: 20px;
    font-size: 14px;
    color: #333333;
    line-height: 20px;
    .setEllipsis(2);
  }
}
</style>
