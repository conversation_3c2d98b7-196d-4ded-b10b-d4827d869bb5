<template>
  <el-form ref="ruleFormRef" :model="ruleForm">
    <div class="search-content">
      <div class="search-input">
        <div class="search-item">
          <el-form-item prop="adapterCode">
            <el-select
              class="form-item"
              v-model="ruleForm.adapterCode"
              placeholder="请选择适配器"
              clearable
              @change="handleSearch"
            >
              <el-option
                v-for="p in adapterCodeOptions"
                :key="p.key"
                :label="p.name"
                :value="p.key"
              />
            </el-select>
          </el-form-item>
        </div>
        <div class="search-item">
          <el-form-item prop="datasourceName">
            <el-input
              class="form-item"
              v-model="ruleForm.datasourceName"
              placeholder="请输入数据源名称"
              @keydown.enter="handleSearch"
            >
              <template #suffix>
                <SvgIcon @click="handleSearch" style="cursor: pointer" name="search" />
              </template>
            </el-input>
          </el-form-item>
        </div>
        <div class="search-item">
          <el-form-item prop="userName">
            <el-input
              class="form-item"
              v-model="ruleForm.userName"
              placeholder="请输入用户名称"
              @keydown.enter="handleSearch"
            >
              <template #suffix>
                <SvgIcon @click="handleSearch" style="cursor: pointer" name="search" />
              </template>
            </el-input>
          </el-form-item>
        </div>
      </div>
      <div v-if="isBtn && permission.hasButton('whiteList_add')" class="search-btn">
        <el-button type="primary" @click="emit('handleCreate')">
          <template #icon>
            <SvgIcon name="add" />
          </template>
          添加数据源
        </el-button>
      </div>
    </div>
  </el-form>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'
import type { FormInstance } from 'element-plus'
import useGlobalData from '@/store/useGlobalData'
import { DicType } from '@/utils/constant'
import useUserInfo from '@/store/useUserInfo'

const permission = useUserInfo()

defineProps<{ isBtn?: boolean }>()

// 字典值
const global = useGlobalData()
const adapterCodeOptions = global.getTypeData(DicType.AdapterCode)

const ruleFormRef = ref<FormInstance>()
const ruleForm = reactive<any>({})

const handleSearch = () => {
  emit('handleSearch', ruleForm)
}

const emit = defineEmits<{
  (e: 'handleSearch', form: any): void
  (e: 'handleCreate'): void
}>()
</script>
