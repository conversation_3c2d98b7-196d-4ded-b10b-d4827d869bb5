<template>
  <transition name="fade">
    <div v-if="showViewer" class="common-preview-wrapper">
      <div class="tip-wrapper">
        <div></div>
        <p>{{ getFileNameComplete(rowData) }}</p>
        <div class="tip-right">
          <SvgIcon name="closeBold" @click="handleClose" class="close-icon" />
        </div>
      </div>
      <div v-loading="loading" element-loading-text="正在保存中..." class="map-wrapper">
        <mavonEditor.mavonEditor
          v-model="markdownText"
          :toolbars="toolbars"
          :subfield="true"
          :editable="editable"
          :toolbarsFlag="editable"
          defaultOpen="preview"
          @save="handleModifyFileContent"
        />
      </div>
    </div>
  </transition>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import mavonEditor from 'mavon-editor'
import 'mavon-editor/dist/css/index.css'
import { filePreviewApi, updateDocumentFileApi } from '@/api/data_catalog'
import { getFileNameComplete } from '@/utils/fileUtils'
import { ElMessage } from 'element-plus'

const showViewer = ref<boolean>(false)
const markdownText = ref<any>('')
const loading = ref<boolean>(false)

const handleModifyFileContent = async (value: string) => {
  try {
    loading.value = true
    const { message, status } = await updateDocumentFileApi({
      userFileId: rowData.value.tid,
      fileContent: value,
    })
    if ([200].includes(status)) {
      ElMessage.success(message)
    } else {
      ElMessage.error(message)
    }
    loading.value = false
  } catch (error) {
    loading.value = false
  }
}

const handleClose = () => {
  showViewer.value = false
}

const editable = ref<boolean>(false)
const toolbars: any = {
  bold: true, // 粗体
  italic: true, // 斜体
  header: true, // 标题
  underline: true, // 下划线
  strikethrough: true, // 中划线
  mark: true, // 标记
  superscript: true, // 上角标
  subscript: true, // 下角标
  quote: true, // 引用
  ol: true, // 有序列表
  ul: true, // 无序列表
  link: true, // 链接
  imagelink: true, // 图片链接
  code: true, // code
  table: true, // 表格
  fullscreen: true, // 全屏编辑
  readmodel: true, // 沉浸式阅读
  htmlcode: true, // 展示html源码
  help: true, // 帮助
  /* 1.3.5 */
  undo: true, // 上一步
  redo: true, // 下一步
  trash: true, // 清空
  save: true, // 保存（触发 events 中的 save 事件）
  /* 1.4.2 */
  navigation: true, // 导航目录
  /* 2.1.8 */
  alignleft: true, // 左对齐
  aligncenter: true, // 居中
  alignright: true, // 右对齐
  /* 2.2.1 */
  subfield: true, // 单双栏模式
  preview: true, // 预览
}
const rowData = ref<any>({})
const handleOpenPreview = async (row: any, edit: boolean = true) => {
  try {
    rowData.value = row
    const res: any = await filePreviewApi({ tid: row.tid })
    markdownText.value = typeof res === 'object' ? JSON.stringify(res) : res.toString()
    showViewer.value = true
    editable.value = edit
  } catch (error) {}
}

defineExpose({ handleOpenPreview })
</script>

<style scoped lang="less">
.common-preview-wrapper {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  .tip-wrapper {
    background: rgba(0, 0, 0, 0.5);
    padding: 0 48px;
    width: 100%;
    height: 48px;
    line-height: 48px;
    color: #fff;
    font-size: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-sizing: border-box;
    .tip-right {
      display: flex;
      height: 100%;
      align-items: center;
      .close-icon {
        cursor: pointer;
      }
    }
  }
  .map-wrapper {
    flex: 1;
    box-sizing: border-box;
    padding: 30px 40px;
    :global(.v-note-wrapper) {
      height: 100%;
    }
    :deep(.v-note-wrapper .v-note-panel) {
      border-radius: 4px 4px 4px 4px;
    }
  }
}
</style>
