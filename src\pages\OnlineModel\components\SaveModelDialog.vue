<template>
  <el-dialog
    width="500px"
    v-model="dialogFormVisible"
    title="模型信息"
    :close-on-click-modal="false"
    @close="handleDialogClose"
  >
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="auto">
      <el-form-item label="模型名称" prop="modelName">
        <el-input placeholder="请输入模型名称" v-model="formData.modelName"></el-input>
      </el-form-item>
      <el-form-item label="模型描述" prop="notes">
        <el-input :rows="3" type="textarea" v-model="formData.notes" placeholder="请输入模型描述" />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="setDialogFormVisible(false)">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import useDialogForm from '@/hooks/useDialogForm'
import { reactive, ref } from 'vue'

const saveType = ref<number>(0)
const formData = ref<any>({})
const rules = reactive<any>({
  modelName: { required: true, message: '请输入模型名称', trigger: 'blur' },
})

const { dialogFormVisible, setDialogFormVisible, formRef, handleDialogClose, validateForm } =
  useDialogForm()

const handleSubmit = async () => {
  try {
    await validateForm()
    emit('handleSubmit', setDialogFormVisible, formData.value, saveType.value)
  } catch (error) {}
}

const handleOpen = (isSave: number, row?: any) => {
  if (row) {
    formData.value = { ...row }
  }
  saveType.value = isSave
  setDialogFormVisible(true)
}

const emit = defineEmits<{
  handleSubmit: [callback: any, form: any, isSave: number]
}>()
defineExpose({ handleOpen })
</script>

<style lang="less" scoped></style>
