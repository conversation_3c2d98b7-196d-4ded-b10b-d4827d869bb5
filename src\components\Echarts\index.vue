<template>
  <div class="echarts" ref="myEchartsRef"></div>
</template>

<script setup lang="ts">
import { onBeforeUnmount, onMounted, ref, watch, nextTick } from 'vue'
import * as echarts from 'echarts'
import { EChartsOption } from 'echarts'

const props = defineProps<{ options: EChartsOption }>()
const myEchartsRef = ref<HTMLElement>()

let myEcharts: echarts.ECharts | null = null

const handleResize = () => {
  if (myEcharts) {
    myEcharts.resize()
  }
}

watch(
  () => props.options,
  (newOptions) => {
    if (myEcharts && newOptions) {
      myEcharts.setOption(newOptions, true)
    }
  },
  {
    deep: true,
  },
)

onMounted(async () => {
  await nextTick()
  if (myEchartsRef.value) {
    myEcharts = echarts.init(myEchartsRef.value)
    if (props.options) {
      myEcharts.setOption(props.options, true)
    }
    window.addEventListener('resize', handleResize)

    // 确保图表在容器准备好后正确渲染
    setTimeout(() => {
      if (myEcharts) {
        myEcharts.resize()
      }
    }, 100)
  }
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
  if (myEcharts) {
    myEcharts.dispose()
    myEcharts = null
  }
})
</script>

<style scoped lang="less">
.echarts {
  width: 100%;
  height: 100%;
  min-height: 300px;
  min-width: 300px;
}
</style>
