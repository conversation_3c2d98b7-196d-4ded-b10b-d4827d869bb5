<template>
  <div class="echarts" ref="myEchartsRef"></div>
</template>

<script setup lang="ts">
import { onBeforeUnmount, onMounted, ref, watch } from 'vue'
import * as echarts from 'echarts'
import { EChartsOption } from 'echarts'

const props = defineProps<{ options: EChartsOption }>()
const myEchartsRef = ref<HTMLElement>()

let myEcharts: any = null

const handleResize = () => {
  myEcharts.resize()
}

watch(
  () => props.options,
  () => {
    myEcharts.setOption(props.options)
  },
  {
    deep: true,
  },
)

onMounted(() => {
  myEcharts = echarts.init(myEchartsRef.value as HTMLElement)
  myEcharts.setOption(props.options, true)
  window.addEventListener('resize', handleResize)
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped lang="less">
.echarts {
  width: 100%;
  height: 100%;
}
</style>
