import { queryPlatform<PERSON>ode<PERSON><PERSON>, queryFiletype<PERSON>ree<PERSON>pi } from '@/api/common'
import { ElMessage } from 'element-plus'
import { defineStore } from 'pinia'
import { DicType } from '@/utils/constant'

interface State {
  dicTypeData: any
  catalogMenuList: any[]
}

const useGlobalData = defineStore({
  id: 'globalData',
  state: (): State => {
    return {
      // 全局字典值
      dicTypeData: null,
      // 数据目录菜单
      catalogMenuList: [],
    }
  },
  actions: {
    // 获取全局字典值
    async getDicTypeData() {
      try {
        const { data, status, message } = await queryPlatformCodeApi()
        if ([200].includes(status)) {
          this.dicTypeData = data
        } else {
          ElMessage.error(message)
        }
      } catch (error) {
        console.error(error)
      }
    },
    // 获取各个类型的字典值
    getTypeData(dictype: DicType) {
      return this.dicTypeData[dictype]
    },
    async getCatalogMenuList() {
      try {
        const { data, status, message } = await queryFiletypeTreeApi()
        if ([200].includes(status)) {
          this.catalogMenuList = data
        } else {
          ElMessage.error(message)
        }
      } catch (error) {
        console.error(error)
      }
    },
  },
})

export default useGlobalData
