<template>
  <div class="report-onlyoffice">
    <iframe
      allow="clipboard-read *; clipboard-write *"
      name="online-viewer"
      allowfullscreen="true"
      :src="previewUrl"
      height="100%"
      width="100%"
      frameborder="0"
    ></iframe>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { previewOfficeApi } from '@/api/data_catalog'
import { ElMessage } from 'element-plus'
import { useRoute } from 'vue-router'
import useUserInfo from '@/store/useUserInfo'

const previewUrl = ref<string>('')
const route = useRoute()
const permission = useUserInfo()

const initData = async () => {
  try {
    const { userFileId, isEdit } = route.query
    const { data, message, status } = await previewOfficeApi({ userFileId, isEdit })
    if ([200].includes(status)) {
      previewUrl.value = `${data}&token=${permission.token}`
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}

initData()
</script>

<style scoped lang="less">
.report-onlyoffice {
  height: 100vh;
  width: 100%;
}
</style>
