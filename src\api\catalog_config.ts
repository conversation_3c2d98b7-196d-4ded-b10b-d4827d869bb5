import request from '@/utils/request'

// 查询-数据目录集合（树）
export const configQueryListApi = (data: any) => request.post(`/dataCatalog/queryTreeByParam`, data)

// 新增-数据目录
export const configAddApi = (data: any) => request.post(`/dataCatalog/add`, data)

// 修改-数据目录
export const configModifyByIdApi = (data: any) => request.post(`/dataCatalog/modifyById`, data)

// 删除-数据目录
export const configRemoveByIdApi = (tid: any) => request.get(`/dataCatalog/removeById/${tid}`)
