<template>
  <div class="SourceFileDetail">
    <Breadcrumb />
    <div class="SourceFileDetail-main">
      <TableList :rowData="rowData" v-if="route.query.list" />
      <DataDetail :rowData="rowData" v-else />
    </div>
  </div>
</template>

<script setup lang="ts">
import Breadcrumb from '@/pages/DataIntegration/DataSource/components/Breadcrumb.vue'
import DataDetail from '@/pages/DataIntegration/DataSource/components/DataDetail.vue'
import TableList from './TableList.vue'
import { useRoute } from 'vue-router'
import { queryByIdApi } from '@/api/data_source'
import { ref } from 'vue'
import { ElMessage } from 'element-plus'

const route = useRoute()

// 获取详情
const rowData = ref<any>({})
const getDataDeatail = async () => {
  try {
    const { data, status, message } = await queryByIdApi(route.query.id)
    if ([200].includes(status)) {
      rowData.value = data
    } else {
      ElMessage.error(message)
    }
  } catch (error) {
    console.error(error)
  }
}
getDataDeatail()
</script>

<style scoped lang="less">
.SourceFileDetail {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  .SourceFileDetail-main {
    margin-top: 15px;
    flex: 1;
    display: flex;
    box-sizing: border-box;
    overflow: hidden;
    background-color: @withe;
  }
}
</style>
