<template>
  <el-dialog
    v-model="dialogFormVisible"
    top="10vh"
    title="元数据"
    width="800px"
    destroy-on-close
    :close-on-click-modal="false"
    @close="handleDialogClose"
  >
    <ElScrollbar max-height="60vh" wrap-style="overflow-x: hidden;">
      <div class="metadata">
        <div class="img" :style="{ 'background-image': `url(${getFileIcon(formData)})` }"></div>
        <div class="formData">
          <p class="title">基本信息</p>
          <el-form
            class="metadata-form"
            ref="formRef"
            :model="formData"
            :rules="rules"
            label-width="auto"
          >
            <el-form-item label="文件名称" prop="fileName">
              <el-input
                v-if="props.isEdit"
                v-model="formData.fileName"
                placeholder="请输入文件名称"
              />
              <span v-else class="text-fill">{{ getFileNameComplete(formData) }}</span>
            </el-form-item>
            <el-form-item label="是否公开" prop="isPublic">
              <el-radio-group v-if="props.isEdit" v-model="formData.isPublic">
                <el-radio label="是" :value="1" size="large"></el-radio>
                <el-radio label="否" :value="0" size="large"></el-radio>
              </el-radio-group>
              <span v-else>{{ formData.isPublic ? '是' : '否' }}</span>
            </el-form-item>
            <el-form-item label="数据类型" prop="fileTypePid">
              <el-cascader
                v-if="props.isEdit"
                :props="catalogProps"
                :options="catalogOptions"
                style="width: 100%"
                v-model="formData.fileTypeId"
                placeholder="请选择数据类型"
              />
              <span v-else>{{ formData.fileTypeName }}</span>
            </el-form-item>
            <ElFormItem v-if="props.isEdit" label="目录分类" prop="dataCatalogId">
              <ElTreeSelect
                v-model="formData.dataCatalogId"
                :data="dataSource"
                check-strictly
                :props="{
                  label: 'catalogAlias',
                  value: 'tid',
                  children: 'childVOList',
                }"
                value-key="tid"
                style="width: 100%"
                @change="handleChange"
              />
            </ElFormItem>
            <ElFormItem v-if="props.isEdit" label="文件路径" prop="filePath">
              <ElTreeSelect
                v-model="formData.filePath"
                :data="dirList"
                check-strictly
                :props="{
                  label: 'filePath',
                  value: 'filePath',
                  children: 'childList',
                }"
                value-key="filePath"
                style="width: 100%"
              />
            </ElFormItem>
            <el-form-item
              v-if="['21'].includes(formData.fileTypePid)"
              label="几何类型"
              prop="featureType"
            >
              <span>{{ formData?.metadataVO?.featureType }}</span>
            </el-form-item>
            <el-form-item label="数据容量" prop="customerName">
              <span>{{ calculateFileSize(Number(formData.fileSize)) }}</span>
            </el-form-item>
            <el-form-item label="存储位置" prop="customerName">
              <span>{{ formData.fileUrl }}</span>
            </el-form-item>
            <el-form-item label="创建时间" prop="produceTime">
              <ElDatePicker
                v-if="props.isEdit"
                v-model="formData.produceTime"
                type="datetime"
                value-format="YYYY-MM-DD HH:mm:ss"
                placeholder="请选择创建时间"
              ></ElDatePicker>
              <span v-else>{{ formData.produceTime || '--' }}</span>
            </el-form-item>
            <el-form-item label="上传时间" prop="createTime">
              <span>{{ formData.createTime }}</span>
            </el-form-item>
            <el-form-item label="空间位置" prop="createTime">
              <el-input v-if="props.isEdit" v-model="formData.center" placeholder="请输入" />
              <span v-else>{{ formData.center.join(',') || formData.center }}</span>
            </el-form-item>
            <el-form-item label="用户" prop="createTime">
              <span>{{ formData.userName }}</span>
            </el-form-item>
            <el-form-item label="来源方式" prop="createTime">
              <span>{{ formData.fileSource }}</span>
            </el-form-item>
            <el-form-item label="来源部门" prop="createTime">
              <span>{{ formData.orgName }}</span>
            </el-form-item>
            <el-form-item label="来源形式" prop="createTime">
              <span>非结构化数据</span>
            </el-form-item>
            <el-form-item label="数据含义" prop="notes">
              <el-input
                v-if="props.isEdit"
                :rows="3"
                type="textarea"
                v-model="formData.notes"
                placeholder="请输入描述"
              />
              <span v-else>{{ formData.notes }}</span>
            </el-form-item>
            <template v-if="gisPreview.includes(formData.fileTypeId) && formData.metadataVO">
              <el-form-item label="坐标系" prop="metadataVO">
                <span>{{ formData.metadataVO.srid }}</span>
              </el-form-item>
              <el-form-item label="x方向上分辨率" prop="metadataVO">
                <span>{{ formData.metadataVO.xresolution || '--' }}</span>
              </el-form-item>
              <el-form-item label="y方向上分辨率" prop="metadataVO">
                <span>{{ formData.metadataVO.yresolution || '--' }}</span>
              </el-form-item>
              <el-form-item label="数据范围" prop="metadataVO">
                <el-row :gutter="10">
                  <el-col :span="12">
                    <ElFormItem label="右上角X坐标">
                      <el-input
                        v-if="props.isEdit"
                        v-model="formData.metadataVO.minX"
                        placeholder="请输入"
                      />
                      <span v-else>{{ formData.metadataVO.minX }}</span>
                    </ElFormItem>
                  </el-col>
                  <el-col :span="12">
                    <ElFormItem label="左下角X坐标">
                      <el-input
                        v-if="props.isEdit"
                        v-model="formData.metadataVO.maxX"
                        placeholder="请输入"
                      />
                      <span v-else>{{ formData.metadataVO.maxX }}</span>
                    </ElFormItem>
                  </el-col>
                  <el-col :span="12">
                    <ElFormItem label="右上角Y坐标">
                      <el-input
                        v-if="props.isEdit"
                        v-model="formData.metadataVO.minY"
                        placeholder="请输入"
                      />
                      <span v-else> {{ formData.metadataVO.minY }}</span>
                    </ElFormItem>
                  </el-col>
                  <el-col :span="12">
                    <ElFormItem label="左下角Y坐标">
                      <el-input
                        v-if="props.isEdit"
                        v-model="formData.metadataVO.minY"
                        placeholder="请输入"
                      />
                      <span v-else> {{ formData.metadataVO.maxY }}</span>
                    </ElFormItem>
                  </el-col>
                </el-row>
              </el-form-item>
            </template>
          </el-form>
          <BloodRelationship v-if="formData.tid" :formData="formData" />
        </div>
      </div>
    </ElScrollbar>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="setDialogFormVisible(false)">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'
import useDialogForm from '@/hooks/useDialogForm'
import {
  userFileListDetailApi,
  updateDetailApi,
  queryBySuffixApi,
  getDirTreeApi,
} from '@/api/data_catalog'
import { ElMessage, CascaderProps, ElScrollbar, ElDatePicker, ElFormItem } from 'element-plus'
import { gisPreview } from '@/utils/fileMap'
import { calculateFileSize, getFileIcon, getFileNameComplete } from '@/utils/fileUtils'
import BloodRelationship from './BloodRelationship.vue'
import { configQueryListApi } from '@/api/catalog_config'
import * as turf from '@turf/turf'
const dataSource = ref<any[]>([])
const getTableData = async () => {
  try {
    const { data, status, message } = await configQueryListApi({ dataType: 2 })
    if (status === 200) {
      dataSource.value = data
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}

const handleChange = (val: any) => {
  formData.value.filePath = ''
  getDirList(val)
}

const dirList = ref<any[]>([])
const getDirList = async (dataCatalogId: string) => {
  try {
    const { data, message, status } = await getDirTreeApi({ dataCatalogId })
    if (status === 200) {
      dirList.value = [data]
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}

const catalogOptions = ref<any[]>([])
const catalogProps: CascaderProps = {
  expandTrigger: 'hover',
  value: 'tid',
  label: 'fileTypeName',
  children: 'childVOList',
  emitPath: false,
}

const { dialogFormVisible, handleDialogClose, formRef, setDialogFormVisible, validateForm } =
  useDialogForm()

const handleOpen = async ({ tid, suffix, dataCatalogId }: any) => {
  try {
    if (props.isEdit) {
      const res = await queryBySuffixApi({ suffix })
      if ([200].includes(res.status)) {
        catalogOptions.value = res.data
      } else {
        ElMessage.error(res.message)
      }
      getTableData()
      if (dataCatalogId) {
        getDirList(dataCatalogId)
      }
    }
    const { data, status, message } = await userFileListDetailApi({ tid })
    if ([200].includes(status)) {
      const bbox = data.metadataVO.bbox.split(',').map(Number)
      const polygon = turf.bboxPolygon(bbox)
      const center = turf.center(polygon)
      data.center = center.geometry.coordinates
      formData.value = data
      setDialogFormVisible(true)
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}

const handleSubmit = async () => {
  if (!props.isEdit) {
    setDialogFormVisible(false)
    return
  }
  try {
    await validateForm()
    const { status, message } = await updateDetailApi(formData.value)
    if ([200].includes(status)) {
      emit('handleRefresh')
      setDialogFormVisible(false)
      ElMessage.success(message)
    } else {
      ElMessage.error(message)
    }
  } catch (error) {
    console.error(error)
  }
}

const formData = ref<any>({})
const rules = reactive<any>({
  filePath: [{ required: true, message: '文件路径必填！', trigger: 'blur' }],
})

defineExpose({ handleOpen })

const props = withDefaults(defineProps<{ isEdit?: boolean }>(), { isEdit: true })

const emit = defineEmits<{
  (e: 'handleRefresh'): void
}>()
</script>

<style scoped lang="less">
.metadata {
  overflow-x: hidden;
  display: flex;
  box-sizing: border-box;
  .img {
    width: 120px;
    height: 80px;
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    box-sizing: border-box;
  }
  .formData {
    margin-left: 10px;
    flex: 1;
    box-sizing: border-box;
    overflow: hidden;
    .title {
      font-weight: 600;
      color: #333333;
      font-size: 14px;
      margin-bottom: 15px;
    }
    .metadata-form {
      padding-left: 15px;
      box-sizing: border-box;
      :deep(.el-form-item) {
        margin-bottom: 10px;
        .el-form-item__label {
          font-weight: bold;
        }
      }
    }
  }
}
</style>
