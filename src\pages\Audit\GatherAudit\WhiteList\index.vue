<template>
  <MainLayout>
    <template #header>
      <SearchTool
        isBtn
        @handle-search="handleSearch"
        @handle-create="AddWhiteDialogRef?.handleOpen"
      />
    </template>
    <el-table
      v-loading="pageData.loading"
      header-cell-class-name="common-table-header"
      cell-class-name="common-table-cell"
      ref="multipleTableRef"
      :data="tableData"
      height="100%"
      style="width: 100%"
      row-key="tid"
    >
      <el-table-column property="adapterName" label="适配器" show-overflow-tooltip />
      <el-table-column property="datasourceName" label="数据源名称" show-overflow-tooltip />
      <el-table-column property="userName" label="用户" show-overflow-tooltip />
      <el-table-column
        v-if="permission.hasButton('whiteList_del')"
        label="操作"
        width="130"
        fixed="right"
      >
        <template #default="scope">
          <el-button
            @click="handleDel(scope.row)"
            title="移除"
            class="common-icon-btn"
            type="danger"
            plain
            link
          >
            <template #icon>
              <SvgIcon name="delete" />
            </template>
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <template #footer>
      <el-pagination
        v-model:currentPage="pageData.page"
        v-model:page-size="pageData.limit"
        :page-sizes="pageData.pageSizes"
        background
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageData.total"
        @size-change="pageData.handleSizeChange"
        @current-change="pageData.handleCurrentChange"
      />
    </template>
    <AddWhiteDialog ref="AddWhiteDialogRef" @handle-refresh="pageData.handleSearch(null, 1)" />
  </MainLayout>
</template>

<script setup lang="ts">
import MainLayout from '@/components/MainLayout/index.vue'
import SearchTool from './SearchTool.vue'
import usePageData from '@/hooks/usePageData'
import { whiteListPageApi, whiteListRemoveByIdApi } from '@/api/gather_audit'
import { ElMessage, ElMessageBox } from 'element-plus'
import AddWhiteDialog from './AddWhiteDialog.vue'
import { ref } from 'vue'
import useUserInfo from '@/store/useUserInfo'

const permission = useUserInfo()

const { tableData, pageData } = usePageData(whiteListPageApi)

const AddWhiteDialogRef = ref<InstanceType<typeof AddWhiteDialog>>()

const handleSearch = (data: any) => {
  pageData.handleSearch({ ...data }, 1)
}

const handleDel = ({ tid }: any) => {
  ElMessageBox.confirm('确认要该数据移除白名单嘛？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    try {
      const { status, message } = await whiteListRemoveByIdApi({ tid })
      if ([200].includes(status)) {
        ElMessage.success(message)
        pageData.handleSearch(null, 1)
      } else {
        ElMessage.error(message)
      }
    } catch (error) {
      console.error(error)
    }
  })
}
</script>

<style lang="less" scoped></style>
