<template>
  <div class="LeftBottom">
    <Echarts :options="option" />
  </div>
</template>

<script setup lang="ts">
import { EChartsOption } from 'echarts'
import Echarts from '@/components/Echarts/index.vue'
import { computed } from 'vue'

const props = defineProps<{ dataPercent: any[] }>()

const filterData = () => {
  return props.dataPercent.map((item: any) => ({ value: Number(item.data), name: item.desc }))
}

const option = computed<EChartsOption>(() => ({
  tooltip: {
    trigger: 'item',
    show: true,
    formatter: '{b}: {d}%',
  },
  // legend: {
  //   type: 'scroll',
  //   orient: 'horizontal',
  //   bottom: '0',
  //   left: 'center',
  //   icon: 'rect'
  // },
  series: [
    {
      name: '数据占比',
      type: 'pie',
      radius: ['40%', '50%'],
      center: ['50%', '50%'],
      avoidLabelOverlap: false,
      itemStyle: {
        borderRadius: 10,
        borderColor: '#fff',
        borderWidth: 2,
      },
      data: filterData(),
    },
  ],
}))
</script>

<style scoped lang="less">
.LeftBottom {
  flex: 1;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
