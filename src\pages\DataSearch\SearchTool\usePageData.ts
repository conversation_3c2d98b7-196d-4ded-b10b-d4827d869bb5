import { ElLoading, ElMessage } from 'element-plus'
import { reactive, ref } from 'vue'
import { queryWfsApi, getFileList<PERSON>pi, getBdFileListApi } from '@/api/data_search'
import { calculateFileSize } from '@/utils/fileUtils'
import * as XLSX from 'xlsx'
import { saveAs } from 'file-saver'
import { IotGetByBoxApi } from '@/api/stream_data'

export default function usePageData() {
  type Page = {
    startIndex: number
    maxFeatures: number
    total: number
    pageSizes: Array<any>
    searchForm: any
    loading: boolean
    active: number
    searchType: number
    handleSearch: (searchType: number, active: number, form?: any, startIndex?: number) => void
    handleSizeChange(val: number): void
    handleCurrentChange(val: number): void
  }

  // 列表数据变量
  const tableData = ref<Array<any>>([])

  // 列表分页数据
  let pageData = reactive<Page>({
    startIndex: 1,
    maxFeatures: 25,
    pageSizes: [25, 50, 75, 100],
    total: 0,
    searchForm: {},
    loading: false,
    active: 1,
    searchType: 1,
    async handleSearch(searchType, active, form, startIndex) {
      pageData.searchType = searchType
      pageData.active = active
      if (form) {
        pageData.searchForm = { ...form }
      }
      if (startIndex) {
        pageData.startIndex = startIndex
      }
      await getTableData()
    },
    handleSizeChange(val) {
      pageData.maxFeatures = val
      getTableData()
    },
    handleCurrentChange(val) {
      pageData.startIndex = val
      getTableData()
    },
  })

  // 获取列表数据方法
  const getTableData = async () => {
    try {
      pageData.loading = true
      const { maxFeatures, startIndex, searchForm } = pageData
      const params = {
        maxFeatures,
        startIndex: (startIndex - 1) * maxFeatures,
        ...searchForm,
      }
      const { features, totalFeatures }: any = [0].includes(pageData.searchType)
        ? await IotGetByBoxApi(params)
        : [4].includes(pageData.active)
          ? await getFileListApi(params)
          : [5].includes(pageData.active)
            ? await getBdFileListApi(params)
            : await queryWfsApi(params)
      if (features) {
        tableData.value = features || []
        pageData.total = totalFeatures || 0
      } else {
        ElMessage.error('查询报错')
        return Promise.reject()
      }
      pageData.loading = false
    } catch (error) {
      pageData.loading = false
      console.error(error)
    }
  }

  // 导出数据
  const handleExport = async () => {
    const loading = ElLoading.service({
      lock: true,
      text: '正在导出请稍等...',
      background: 'rgba(0, 0, 0, 0.7)',
    })
    try {
      const { searchForm } = pageData
      const params = {
        maxFeatures: 10000,
        startIndex: 0,
        ...searchForm,
      }
      const { features }: any = [0].includes(pageData.searchType)
        ? await IotGetByBoxApi(params)
        : [4].includes(pageData.active)
          ? await getFileListApi(params)
          : [5].includes(pageData.active)
            ? await getBdFileListApi(params)
            : await queryWfsApi(params)
      if (features) {
        await exportToExcel(features)
      } else {
        ElMessage.error('导出报错！')
        return Promise.reject()
      }
      loading.close()
    } catch (error) {
      loading.close()
      console.error(error)
    }
  }

  // 导出表格方法
  const exportToExcel = (tableData: any[]) => {
    return new Promise((resolve) => {
      // 自定义表头
      const header: any[] = [['名称', '数据类型', '大小']]
      // 将数据转换为数组格式
      const list: any[] = tableData.map((row: any) => [
        `${
          row.properties.scene_type
            ? row.properties.file_name
            : `${row.properties.file_name}.${row.properties.suffix}`
        }`,
        row.properties.file_type_name,
        `${calculateFileSize(Number(row.properties.file_size))}`,
      ])
      // 合并表头和数据
      const worksheetData = header.concat(list)
      // 将数据转换为工作表
      const worksheet = XLSX.utils.aoa_to_sheet(worksheetData)
      const workbook = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1')

      // 生成 Excel 文件并下载
      const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' })
      const dataBlob = new Blob([excelBuffer], { type: 'application/octet-stream' })
      saveAs(dataBlob, 'table_data.xlsx')
      resolve(true)
    })
  }

  return { tableData, pageData, handleExport }
}
