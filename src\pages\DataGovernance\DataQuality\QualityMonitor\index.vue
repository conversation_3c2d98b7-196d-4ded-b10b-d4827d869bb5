<template>
  <div class="qualityMonitor">
    <ul class="totalTop">
      <li v-for="(p, i) in totalList" :key="i" class="totalist">
        <div v-if="p.name === 'line'" class="line"></div>
        <template v-else>
          <div class="img" :style="{ backgroundColor: p.bg }">
            <img :src="p.img" alt="" />
          </div>
          <div class="total">
            <p>{{ p.total }}</p>
            <p>{{ p.name }}</p>
          </div>
        </template>
      </li>
    </ul>
    <div class="centerMain">
      <div class="square lf">
        <span class="text">数据总体通过率</span>
        <Echarts v-if="passRateOptions" :options="passRateOptions" />
      </div>
      <div class="rectangle lf">
        <span class="text">文件质检趋势分析</span>
        <Echarts v-if="historyOptions" :options="historyOptions" />
      </div>
      <div class="rectangle lf">
        <span class="text">表数据质检趋势分析</span>
        <Echarts v-if="tableHistoryOptions" :options="tableHistoryOptions" />
      </div>
    </div>
    <div class="bottomMain">
      <div class="square lf">
        <span class="text">质量平均得分</span>
        <Echarts v-if="qualityScore" :options="qualityScore" />
      </div>
      <div class="rectangle lf">
        <span class="text">质量平均得分趋势</span>
        <Echarts v-if="qualitySubTrendOptions" :options="qualitySubTrendOptions" />
      </div>
      <div class="square lf">
        <span class="text">质量得分分布</span>
        <Echarts v-if="pieOptions" :options="pieOptions" />
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref } from 'vue'
import rule from '@/assets/qualityMonitorImg/rule.png'
import model from '@/assets/qualityMonitorImg/model.png'
import checked from '@/assets/qualityMonitorImg/checked.png'
import qualified from '@/assets/qualityMonitorImg/qualified.png'
import unqualified from '@/assets/qualityMonitorImg/unqualified.png'
import checkedTable from '@/assets/qualityMonitorImg/checkedTable.png'
import qualifiedTable from '@/assets/qualityMonitorImg/qualifiedTable.png'
import unqualifiedTable from '@/assets/qualityMonitorImg/unqualifiedTable.png'
import { ElMessage } from 'element-plus'
import { queryResultApi } from '@/api/data_governance'
import Echarts from '@/components/Echarts/index.vue'
import * as echarts from 'echarts'
import { EChartsOption } from 'echarts'

const totalList = ref<any[]>([])

//数据总体通过率
const passRateOptions = ref<any>()
const getPassRateOptions = (passRate: number): EChartsOption => {
  return {
    series: [
      {
        name: 'Pressure',
        type: 'gauge',
        progress: {
          show: true,
        },
        axisTick: {
          distance: 0,
          length: 5,
          lineStyle: {
            width: 1,
          },
        },
        splitLine: {
          distance: 0,
          length: 10,
          lineStyle: {
            width: 2,
          },
        },
        axisLabel: {
          fontSize: 8,
        },
        detail: {
          valueAnimation: true,
          formatter: '{value}%',
          fontSize: 18,
        },
        data: [
          {
            value: passRate,
          },
        ],
      },
    ],
  }
}

// 文件质检趋势分析
const historyOptions = ref<EChartsOption | any>()

// 表数据质检趋势分析
const tableHistoryOptions = ref<EChartsOption | any>()

const getHistoryOptions = (qaTotalList: any[], qaPassList: any[], qaFailList: any[]) => {
  return {
    animationDuration: 5000,
    title: {
      show: false,
    },
    legend: {
      show: false,
    },
    grid: {
      top: '20%',
      left: 20,
      right: 20,
      bottom: '10%',
      containLabel: true,
    },
    tooltip: {
      trigger: 'axis',
    },
    xAxis: {
      type: 'category',
      axisLine: {
        show: true,
        lineStyle: {
          color: '#CCCCCC',
        },
      },
      splitLine: {
        show: false,
      },
      axisLabel: {
        fontFamily: 'MicrosoftYaHei',
        fontSize: 12,
        color: '#333333',
      },
      axisTick: {
        show: true,
        alignWithLabel: true,
        inside: true,
      },
      boundaryGap: true,
      data: qaTotalList.map((item: any) => item.name),
    },
    yAxis: {
      type: 'value',
      min: 0,
      axisLine: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: '#CCCCCC',
          type: 'dashed',
        },
      },
      axisLabel: {
        show: true,
        margin: 20,
      },
      axisTick: {
        show: false,
      },
    },
    series: [
      {
        name: '治理文件数',
        type: 'line',
        showSymbol: false,
        showAllSymbol: true,
        symbol: 'circle',
        symbolSize: 12,
        smooth: true,
        lineStyle: {
          color: '#5592EC',
        },
        label: {
          show: false,
        },
        itemStyle: {
          color: '#5592EC',
          borderColor: '#ffffff',
          borderWidth: 3,
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(
            0,
            0,
            0,
            1,
            [
              {
                offset: 0,
                color: 'rgba(85, 146, 236, 0.8)',
              },
              {
                offset: 1,
                color: 'rgba(255, 255, 255, 0.1)',
              },
            ],
            false,
          ),
        },
        data: qaTotalList.map((item: any) => item.value),
      },
      {
        name: '合格文件数',
        type: 'line',
        showSymbol: false,
        showAllSymbol: true,
        symbol: 'circle',
        symbolSize: 12,
        smooth: true,
        lineStyle: {
          color: 'rgba(59, 217, 193, 1)',
        },
        label: {
          show: false,
        },
        itemStyle: {
          color: 'rgba(59, 217, 193, 1)',
          borderColor: '#ffffff',
          borderWidth: 3,
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(
            0,
            0,
            0,
            1,
            [
              {
                offset: 0,
                color: 'rgba(59, 217, 193, 0.8)',
              },
              {
                offset: 1,
                color: 'rgba(255, 255, 255, 0.1)',
              },
            ],
            false,
          ),
        },
        data: qaPassList.map((item: any) => item.value),
      },
      {
        name: '问题文件数',
        type: 'line',
        showSymbol: false,
        showAllSymbol: true,
        symbol: 'circle',
        symbolSize: 12,
        smooth: true,
        lineStyle: {
          color: 'rgba(252, 120, 102, 1)',
        },
        label: {
          show: false,
        },
        itemStyle: {
          color: 'rgba(252, 120, 102, 1)',
          borderColor: '#ffffff',
          borderWidth: 3,
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(
            0,
            0,
            0,
            1,
            [
              {
                offset: 0,
                color: 'rgba(252, 120, 102, 0.8)',
              },
              {
                offset: 1,
                color: 'rgba(255, 255, 255, 0.1)',
              },
            ],
            false,
          ),
        },
        data: qaFailList.map((item: any) => item.value),
      },
    ],
  }
}

// 质量平均得分
const qualityScore = ref<any>()
const getQualityScore = (avgScore: number) => {
  return {
    series: [
      {
        type: 'gauge',
        axisLine: {
          lineStyle: {
            width: 10,
            color: [
              [0.25, '#BCEBEE'],
              [0.5, '#92E0E4'],
              [0.75, '#92E0E4'],
              [1, '#26C1C9'],
            ],
          },
        },
        axisTick: {
          distance: 0,
          length: 5,
          lineStyle: {
            width: 1,
            color: '#8F99A3',
          },
        },
        splitLine: {
          distance: 0,
          length: 10,
          lineStyle: {
            width: 2,
            color: '#D0DCE5',
          },
        },
        axisLabel: {
          color: 'inherit',
          fontSize: 8,
        },
        pointer: {
          itemStyle: {
            color: 'auto',
          },
        },
        title: {
          show: false,
        },
        detail: {
          valueAnimation: true,
          formatter: '{value}',
          color: 'inherit',
          fontSize: 20,
        },
        data: [
          {
            value: avgScore,
          },
        ],
      },
    ],
  }
}

// 质量平均得分趋势
const qualitySubTrendOptions = ref<any>()
const getQualitySubTrendOptions = (avgScoreList: any[]) => {
  return {
    animationDuration: 5000,
    title: {
      show: false,
    },
    legend: {
      show: false,
    },
    grid: {
      top: '20%',
      left: 20,
      right: 20,
      bottom: '10%',
      containLabel: true,
    },
    tooltip: {
      trigger: 'axis',
    },
    xAxis: {
      type: 'category',
      axisLine: {
        show: true,
        lineStyle: {
          color: '#CCCCCC',
        },
      },
      splitLine: {
        show: false,
      },
      axisLabel: {
        fontFamily: 'MicrosoftYaHei',
        fontSize: 12,
        color: '#333333',
      },
      axisTick: {
        show: true,
        alignWithLabel: true,
        inside: true,
      },
      boundaryGap: true,
      data: avgScoreList.map((item: any) => item.name),
    },
    yAxis: {
      type: 'value',
      min: 0,
      axisLine: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: '#CCCCCC',
          type: 'dashed',
        },
      },
      axisLabel: {
        show: true,
        margin: 20,
      },
      axisTick: {
        show: false,
      },
    },
    series: [
      {
        name: '',
        type: 'line',
        showAllSymbol: true,
        symbol: 'circle',
        symbolSize: 12,
        smooth: true,
        lineStyle: {
          color: 'rgba(85, 146, 236, 1)',
        },
        label: {
          show: false,
        },
        itemStyle: {
          color: 'rgba(85, 146, 236, 1)',
          borderColor: '#ffffff',
          borderWidth: 3,
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(
            0,
            0,
            0,
            1,
            [
              {
                offset: 0,
                color: 'rgba(85, 146, 236, 0.8)',
              },
              {
                offset: 1,
                color: 'rgba(255, 255, 255, 0.5)',
              },
            ],
            false,
          ),
        },
        data: avgScoreList.map((item: any) => item.value),
      },
    ],
  }
}

// 质量得分分布
const pieOptions = ref<any>()
const getPieOptions = (
  countExcellent: number,
  countGood: number,
  countMedium: number,
  countFail: number,
) => {
  return {
    tooltip: {
      trigger: 'item',
    },
    legend: {
      bottom: '2%',
      left: 'center',
    },
    series: [
      {
        name: '质量得分分布',
        type: 'pie',
        radius: ['40%', '60%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center',
        },
        labelLine: {
          show: false,
        },
        data: [
          { value: countExcellent, name: '优秀' },
          { value: countGood, name: '良好' },
          { value: countMedium, name: '一般' },
          { value: countFail, name: '不合格' },
        ],
      },
    ],
  }
}

const getAllData = async () => {
  try {
    const { data, status, message } = await queryResultApi()
    if ([200].includes(status)) {
      const {
        countTotal,
        countPass,
        countFail,
        passRate,
        avgScore,
        qaTotalList,
        qaPassList,
        qaFailList,
        avgScoreList,
        countExcellent,
        countGood,
        countMedium,
        ruleCount,
        modelCount,
        countNotPass,
        tableCountTotal,
        tableCountPass,
        tableCountNotPass,
        tableQaFailList,
        tableQaPassList,
        tableQaTotalList,
      } = data
      totalList.value = [
        { total: ruleCount, name: '规则数', bg: 'rgba(255, 229, 243, 1)', img: rule },
        { total: modelCount, name: '模型数', bg: 'rgba(227, 252, 251, 1)', img: model },
        { name: 'line' },
        { total: countTotal, name: '质检文件数', bg: 'rgba(255, 244, 229, 1)', img: checked },
        { total: countPass, name: '合格文件数', bg: 'rgba(234, 254, 255, 1)', img: qualified },
        {
          total: countNotPass,
          name: '不合格文件数',
          bg: 'rgba(255, 229, 243, 1)',
          img: unqualified,
        },
        { name: 'line' },
        {
          total: tableCountTotal,
          name: '质检表数量',
          bg: 'rgba(255, 244, 229, 1)',
          img: checkedTable,
        },
        {
          total: tableCountPass,
          name: '合格表数量',
          bg: 'rgba(234, 254, 255, 1)',
          img: qualifiedTable,
        },
        {
          total: tableCountNotPass,
          name: '不合格表数量',
          bg: 'rgba(255, 229, 243, 1)',
          img: unqualifiedTable,
        },
      ]
      passRateOptions.value = getPassRateOptions(passRate)
      historyOptions.value = getHistoryOptions(qaTotalList, qaPassList, qaFailList)
      tableHistoryOptions.value = getHistoryOptions(
        tableQaTotalList,
        tableQaPassList,
        tableQaFailList,
      )
      qualityScore.value = getQualityScore(avgScore)
      qualitySubTrendOptions.value = getQualitySubTrendOptions(avgScoreList)
      pieOptions.value = getPieOptions(countExcellent, countGood, countMedium, countFail)
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}

getAllData()
</script>
<style lang="less" scoped>
.qualityMonitor {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  .totalTop {
    height: 7.2917vw;
    background-color: @withe;
    box-shadow: 0px 0px 7px 1px rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 1.0417vw;
    box-sizing: border-box;
    overflow: hidden;
    .totalist {
      display: flex;
      align-items: center;
      .img {
        height: 3.9583vw;
        width: 3.9583vw;
        border-radius: 4px;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: center;
        img {
          width: 2.0833vw;
        }
      }
      .total {
        margin-left: 0.7813vw;
        :nth-child(1) {
          font-size: 1.875vw;
          color: #333333;
        }
        :nth-child(2) {
          font-size: 0.8333vw;
          color: #656d92;
        }
      }
      .line {
        width: 1px;
        height: 2.6042vw;
        background: #e5e5e5;
      }
    }
  }
  .centerMain,
  .bottomMain {
    margin-top: 0.7813vw;
    flex: 1;
    overflow: hidden;
    box-sizing: border-box;
    display: flex;
    .lf + .lf {
      margin-left: 0.7813vw;
    }
    .square {
      width: 23.4375vw;
      height: 100%;
      box-sizing: border-box;
      background-color: @withe;
      box-shadow: 0px 0px 7px 1px rgba(0, 0, 0, 0.05);
      position: relative;
      overflow: hidden;
    }
    .rectangle {
      flex: 1;
      box-sizing: border-box;
      background-color: @withe;
      box-shadow: 0px 0px 7px 1px rgba(0, 0, 0, 0.05);
      position: relative;
      overflow: hidden;
    }
    .text {
      position: absolute;
      left: 1vw;
      top: 1vw;
      font-size: 1vw;
      color: #656d92;
    }
  }
}
</style>
