<template>
  <div class="dataGovernance">
    <CommonAccordionMenu />
    <div class="main">
      <RouterView />
    </div>
  </div>
</template>

<script setup lang="ts">
import CommonAccordionMenu from '@/components/CommonAccordionMenu/index.vue'
import { RouterView } from 'vue-router'
</script>

<style lang="less" scoped>
.dataGovernance {
  height: 100%;
  width: 100%;
  box-sizing: border-box;
  display: flex;
  .main {
    flex: 1;
    overflow: hidden;
    box-sizing: border-box;
    margin-left: 20px;
  }
}
</style>
