<template>
  <div class="CreateModel" ref="CreateModelRef">
    <LeftMenu @handleNodeClick="handleLeftNodeClick" :isRunning="taskModelStatus.isRunning" />
    <div class="CreateModel-main">
      <TopNav @handleclick="handlebtnClick" :isRunning="taskModelStatus.isRunning" />
      <div class="graph" ref="graphRef">
        <RelationGraph
          :on-canvas-click="graphOperate.handleCanvasClick"
          :onLineClick="graphOperate.handleLineClick"
          :onImageDownload="() => false"
          :on-node-click="graphOperate.handleNodeClick"
          :options="options"
          ref="RelationGraphRef"
        >
          <template #node="{ node }">
            <div class="node">
              <div class="nodeIcon">
                <img :src="(node as any).data.icon" alt="" />
              </div>
              <span :class="getNodeStyle(node)" @click.stop.self="graphOperate.handleReport(node)">
                {{ (node as any).text }}
              </span>
            </div>
          </template>
        </RelationGraph>
      </div>
      <ModelLog :logList="logData.logList" ref="ModelLogRef" />
    </div>
    <RightMenu
      ref="RightMenuRef"
      :selectNode="graphOperate.selectNode"
      :isRunning="taskModelStatus.isRunning"
    />
    <CreateModelDialog @handle-submit="modelData.handleImportModel" ref="CreateModelDialogRef" />
    <SaveModelDialog @handle-submit="saveRunObj.handleSaveModel" ref="SaveModelRef" />
    <CreateEditDialog
      @handle-submit="saveRunObj.handleRunModel"
      isCreateTask
      ref="CreateEditDialogRef"
    />
    <QualityReportDialog ref="QualityReportDialogRef" />
    <MapBoxPreview ref="MapBoxPreviewRef" :isImageCutShow="false" />
    <CesiumPreview ref="CesiumPreviewRef" />
    <ServerAddress ref="ServerAddressRef" />
    <ConfigDialog ref="ConfigDialogRef" @handle-refresh="setOptions" />
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, reactive, ref } from 'vue'
import RelationGraph, { RGLine, RGLink, RGNode, RGUserEvent } from 'relation-graph-vue3'
import LeftMenu from '@/pages/OnlineModel/CreateModel/components/LeftMenu.vue'
import TopNav from '@/pages/OnlineModel/CreateModel/components/TopNav.vue'
import RightMenu from '@/pages/OnlineModel/components/RightMenu.vue'
import ModelLog from '@/pages/OnlineModel/components/ModelLog.vue'
import CreateModelDialog from '@/pages/OnlineModel/components/CreateModelDialog.vue'
import CreateEditDialog from '@/pages/OnlineModel/components/CreateEditDialog.vue'
import SaveModelDialog from '@/pages/OnlineModel/components/SaveModelDialog.vue'
import ConfigDialog from '@/pages/OnlineModel/CreateModel/components/ConfigDialog.vue'
import useRelationGraph from '@/hooks/useRelationGraph.ts'
import { ElMessage, ElLoading } from 'element-plus'
import {
  queryNodeParamApi,
  modelAddApi,
  modifyByIdApi,
  queryByIdApi,
  addTaskApi,
} from '@/api/online_model'
import { useRoute, useRouter } from 'vue-router'
import screenfull from 'screenfull'
import { getFilterLineData, getFilterNodeData } from '@/utils'
import useOutputData from '@/pages/OnlineModel/hooks/useOutputData'

const router = useRouter()
const route = useRoute()

const {
  ServerAddress,
  ServerAddressRef,
  QualityReportDialog,
  QualityReportDialogRef,
  MapBoxPreview,
  MapBoxPreviewRef,
  CesiumPreview,
  CesiumPreviewRef,
  handleOutputData,
  getNodeStyle,
} = useOutputData()

const {
  setCheckedNode,
  options,
  RelationGraphRef,
  addNode,
  removeNode,
  addLine,
  setZoom,
  removeLine,
  clearChecked,
  clearAll,
  getGraphJsonData,
  getImage,
  setJsonData,
  taskModelStatus,
  logData,
  setOptions,
  // moveToCenter,
  // zoomToFit,
} = useRelationGraph()

// 获取model详情
interface ModelData {
  getModelData: (tid?: any) => Promise<void>
  modelParams: any
  filterModelParams: (params: any) => any
  handleImportModel: (callback: any, tid: string) => void
}
const modelData = reactive<ModelData>({
  modelParams: {},
  getModelData: async (tid: any = route.query.tid) => {
    try {
      const { status, data, message } = await queryByIdApi(tid)
      if ([200].includes(status)) {
        modelData.modelParams = modelData.filterModelParams(data)
        const { tid, nodes, lines } = modelData.modelParams
        await setJsonData({ rootId: tid, nodes, lines }, true)
        graphOperate.selectNode = null
        graphOperate.selectLine = null
        clearChecked()
      } else {
        ElMessage.error(message)
      }
    } catch (error) {}
  },
  filterModelParams: (params: any) => {
    const { tid, modelName, notes, modelNodeLineVOList, modelNodeVOList } = params
    const nodes = getFilterNodeData(modelNodeVOList)
    const lines = getFilterLineData(modelNodeLineVOList)
    return { tid, modelName, notes, nodes, lines }
  },
  handleImportModel: async (setDialogFormVisible, tid) => {
    try {
      await modelData.getModelData(tid)
      setDialogFormVisible(false)
    } catch (error) {}
  },
})

// 保存运行模型
const SaveModelRef = ref<any>()
const ModelLogRef = ref<any>()
interface SaveRunObj {
  taskModelId: string
  handleOpenSaveDialog: (isSave: number) => void
  getNodeParam: (nodes: any[]) => any[]
  getLinkParam: (lines: any[]) => any[]

  saveModel: (form: any, isSave: number) => Promise<void>
  handleSaveModel: (setDialogFormVisible: any, form: any, isSave: number) => Promise<void>
  handleRunModel: (form: any, setDialogFormVisible: any) => Promise<void>
}
const saveRunObj = reactive<SaveRunObj>({
  taskModelId: '',
  // isSave 0 保存  1另存为新模型
  handleOpenSaveDialog: (isSave: number) => {
    const { isModify, tid }: any = route.query
    if (['0'].includes(isModify) && !isSave) {
      ElMessage.warning('该模型只能使用不能编辑，您可另存为新模型！')
      return
    }
    const { modelName, notes } = modelData.modelParams
    SaveModelRef.value?.handleOpen(isSave, tid && !isSave ? { modelName, notes } : {})
  },
  getNodeParam: (nodes: any[]) => {
    return nodes.map((node: any) => ({
      nodePkId: node.id,
      name: node.text,
      nodeId: node.data.nodeId,
      nodeParam: node.data.nodeParam,
      // nodeShape: node.nodeShape,
      fixed: true,
      nodeX: node.x,
      nodeY: node.y,
      width: node.width,
      height: node.height,
      // color: node.color,
      // borderColor: options.value.defaultNodeBorderColor,
      // fontColor: node.fontColor
    }))
  },
  getLinkParam: (lines: any[]) => {
    return lines.map((line: any) => ({
      fromId: line.from,
      toId: line.to,
      lineName: line.text,
      // color: options.value.defaultLineColor
    }))
  },
  // 通用保存编辑
  saveModel: async (form: any, isSave: number) => {
    try {
      logData.clearData()
      const graphData = getGraphJsonData()
      const modelNodeDTOList: any[] = saveRunObj.getNodeParam(graphData.nodes)
      const modelNodeLineDTOList: any[] = saveRunObj.getLinkParam(graphData.lines)
      const thumbnail = await getImage()
      const params = { ...form, isModify: 1, thumbnail, modelNodeDTOList, modelNodeLineDTOList }
      const { status, message, data } = isSave
        ? await modelAddApi(params)
        : route.query.tid && ['1'].includes(route.query.isModify as any)
          ? await modifyByIdApi({ tid: route.query.tid, ...params })
          : await modelAddApi(params)
      if ([200].includes(status)) {
        await router.replace({
          query: { tid: [true].includes(data) ? route.query.tid : data, isModify: '1' },
        })
        await modelData.getModelData()
        return Promise.resolve()
      } else {
        return Promise.reject(message)
      }
    } catch (error) {
      return Promise.reject(error)
    }
  },
  // 保存另存为
  handleSaveModel: async (setDialogFormVisible: any, form: any, isSave: number) => {
    const loading = ElLoading.service({
      lock: true,
      text: '正在保存,请勿离开...',
      spinner: 'el-icon-loading',
      background: 'rgba(0, 0, 0, 0.7)',
    })
    try {
      await saveRunObj.saveModel(form, isSave)
      setDialogFormVisible(false)
      ElMessage.success('保存成功！')
      loading.close()
    } catch (error) {
      ElMessage.error('保存失败！')
      loading.close()
    }
  },
  // 运行模型
  handleRunModel: async (form: any, setDialogFormVisible: any) => {
    const loading = ElLoading.service({
      lock: true,
      text: '正在操作,请勿离开...',
      spinner: 'el-icon-loading',
      background: 'rgba(0, 0, 0, 0.7)',
    })
    try {
      taskModelStatus.stopTask()
      saveRunObj.taskModelId = ''
      const { tid }: any = route.query
      const { modelName, notes } = modelData.modelParams
      await saveRunObj.saveModel(tid ? { modelName, notes } : { modelName: form.taskName }, 0)
      const { data, status, message } = await addTaskApi({
        modelId: route.query.tid,
        taskDTO: { ...form },
      })
      if ([200].includes(status)) {
        if (data) {
          saveRunObj.taskModelId = data
          logData.setTaskModelId(data)
          taskModelStatus.initStatus(data)
          ModelLogRef.value?.handleOpenLog()
        }
        ElMessage.success(message || '运行成功！')
        setDialogFormVisible(false)
      } else {
        ElMessage.error(message)
      }
      loading.close()
    } catch (error) {
      ElMessage.error('保存失败！')
      loading.close()
    }
  },
})
onMounted(() => {
  if (route.query.tid && RelationGraphRef.value) {
    modelData.getModelData()
  }
  logData.getLogData()
})
// 取消定时任务轮询
onUnmounted(() => {
  taskModelStatus.stopTask()
  logData.clearLogData()
})

// 关系图相关操作
interface GraphOperate {
  selectNode: RGNode | any
  handleNodeClick: (node: RGNode, _e?: RGUserEvent) => boolean
  selectLine: any
  handleLineClick: (line: RGLine, link: RGLink, _e: RGUserEvent) => boolean
  handleCanvasClick: () => void
  handleReport: (node: any) => void
}
const graphOperate = reactive<GraphOperate>({
  // 点击节点事件
  selectNode: null,
  handleNodeClick: (node: RGNode, _e?: any) => {
    const className: string = _e.target.className
    if (className.indexOf('nodeText') !== -1) return false
    graphOperate.selectNode = node
    graphOperate.selectLine = null
    setCheckedNode(node.id)
    return true
  },
  // 点击线事件
  selectLine: null,
  handleLineClick: (_line, link, _e) => {
    console.log(link)
    graphOperate.selectLine = link
    graphOperate.selectNode = null
    return true
  },
  // 取消选中
  handleCanvasClick: () => {
    graphOperate.selectNode = null
    graphOperate.selectLine = null
    clearChecked()
  },
  // 点击报告
  handleReport: async (node: any) => {
    const nodeIsReport: boolean = await handleOutputData(node, saveRunObj.taskModelId)
    if (nodeIsReport) return
    graphOperate.selectNode = node
    graphOperate.selectLine = null
    setCheckedNode(node.id)
  },
})

// 右侧拦 菜单右侧点点击
const RightMenuRef = ref<any>()
const graphRef = ref<any>()
const handleLeftNodeClick = async (node: any, e: any) => {
  try {
    const { data, message, status } = await queryNodeParamApi(node.tid)
    if ([200].includes(status)) {
      addNode(
        {
          id: Date.now().toString(),
          text: `${node.nodeName}`,
          // nodeShape: options.value.defaultNodeShape,
          data: {
            icon: `data:image/jpeg;base64,${node.icon}`,
            nodeParam: data,
            nodeId: node.tid,
            nodeStatus: undefined,
            nodeCode: node.nodeCode,
          },
          // color: options.value.defaultNodeColor,
          // fontColor: options.value.defaultNodeFontColor,
          // borderColor: options.value.defaultNodeBorderColor,
          width: options.value.defaultNodeWidth,
          height: options.value.defaultNodeHeight,
        },
        e,
      )
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}

// 打开模型
const CreateModelDialogRef = ref<any>()
//运行
const CreateEditDialogRef = ref<any>()

// 添加连线
const handleAddRelation = (e: any) => {
  addLine(e)
}
// 删除节点
const handleRemove = () => {
  if (!graphOperate.selectNode && !graphOperate.selectLine) {
    ElMessage.warning('请选择要删除的节点或线条！')
    return
  }
  if (graphOperate.selectNode) {
    removeNode(graphOperate.selectNode)
    graphOperate.selectNode = null
  }
  if (graphOperate.selectLine) {
    removeLine(graphOperate.selectLine)
    graphOperate.selectLine = null
  }
}

// 设置全屏
const CreateModelRef = ref<any>()
const handleFullScreen = () => {
  if (screenfull.isEnabled) {
    if (screenfull.isFullscreen) {
      screenfull.exit()
    } else {
      screenfull.request(CreateModelRef.value)
    }
  }
}

// 全局配置设置
const ConfigDialogRef = ref<any>()
// 图表按钮的各种操作
const handlebtnClick = (name: any, e: any) => {
  switch (name) {
    case 'onlineModelOpen':
      CreateModelDialogRef.value?.handleOpen()
      break
    case 'onlineModelSave':
      saveRunObj.handleOpenSaveDialog(0)
      break
    case 'omlineModelSaveAs':
      saveRunObj.handleOpenSaveDialog(1)
      break
    case 'onlineModelRun':
      CreateEditDialogRef.value?.handleOpen()
      break
    case 'onlineModelDel':
      handleRemove()
      break
    case 'onlineModelConnect':
      handleAddRelation(e)
      break
    case 'onlineModelAmplify':
      setZoom(10)
      break
    case 'onlineModelReduce':
      setZoom(-10)
      break
    case 'onlineModelFullScreen':
      handleFullScreen()
      break
    case 'onlineModelClear':
      clearAll()
      break
    default:
      ConfigDialogRef.value?.handleOpen()
      break
  }
}
</script>

<style lang="less" scoped>
.CreateModel {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  overflow: hidden;
  display: flex;
  .CreateModel-main {
    flex: 1;
    box-sizing: border-box;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    .graph {
      flex: 1;
      overflow: hidden;
      box-sizing: border-box;
      .node {
        font-size: 12px;
        cursor: pointer;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        .nodeIcon {
          width: 36px;
          height: 36px;
          pointer-events: none;
          img {
            width: 100%;
            height: 100%;
          }
        }
        .nodeText {
          text-align: center;
          position: absolute;
          bottom: -30px;
          width: 80px;
          .ellipseLine();
        }
        .nodeTextActive {
          color: var(--el-color-primary);
          text-decoration: underline;
        }
      }
    }
  }
}
</style>
