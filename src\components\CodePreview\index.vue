<template>
  <transition name="fade">
    <div v-show="showViewer" class="common-preview-wrapper">
      <div class="tip-wrapper">
        <div></div>
        <p>{{ getFileNameComplete(rowData) }}</p>
        <div class="tip-right">
          <SvgIcon name="closeBold" @click="handleClose" class="close-icon" />
        </div>
      </div>
      <div class="map-wrapper">
        <div class="code-editor">
          <div class="code-editor-operate">
            <ElButton v-if="isEdit" @click="handleSave" type="primary" size="small">保 存</ElButton>
          </div>
          <div v-loading="loading" element-loading-text="正在保存中..." class="code-preview">
            <Codemirror
              v-model="code"
              readOnly
              :disabled="config.disabled"
              :extensions="config.extensions"
              :tabSize="config.tabSize"
              :indentWithTab="config.indentWithTab"
              :autofocus="config.autofocus"
              placeholder="Please enter the code."
              :style="{
                height: config.height,
                width: '100%',
              }"
            />
          </div>
          <div class="code-editor-operate-bootom"></div>
        </div>
      </div>
    </div>
  </transition>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Codemirror } from 'vue-codemirror'
import { javascript } from '@codemirror/lang-javascript'
import { filePreviewApi, updateDocumentFileApi } from '@/api/data_catalog'
import { getFileNameComplete } from '@/utils/fileUtils'
import { ElButton, ElMessage } from 'element-plus'

const code = ref<any>('')
const loading = ref<boolean>(false)

const config = ref<any>({
  disabled: false,
  indentWithTab: true,
  tabSize: 2,
  autofocus: false,
  height: '100%',
  extensions: [javascript()],
})

const showViewer = ref<boolean>(false)
const isEdit = ref<boolean>(false)

const handleSave = async () => {
  try {
    loading.value = true
    const { message, status } = await updateDocumentFileApi({
      userFileId: rowData.value.tid,
      fileContent: code.value,
    })
    if ([200].includes(status)) {
      ElMessage.success(message)
    } else {
      ElMessage.error(message)
    }
    loading.value = false
  } catch (error) {
    loading.value = false
  }
}

const handleClose = () => {
  showViewer.value = false
  code.value = ''
}

const rowData = ref<any>({})
const handleOpenPreview = async (row: any, edit: boolean = false) => {
  try {
    rowData.value = row
    const res: any = await filePreviewApi({ tid: row.tid })
    code.value = typeof res === 'object' ? JSON.stringify(res) : res.toString()
    showViewer.value = true
    isEdit.value = edit
  } catch (error) {}
}

defineExpose({ handleOpenPreview })
</script>

<style scoped lang="less">
.common-preview-wrapper {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  overflow: hidden;
  .tip-wrapper {
    background: rgba(0, 0, 0, 0.5);
    padding: 0 48px;
    width: 100%;
    height: 48px;
    line-height: 48px;
    color: #fff;
    font-size: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-sizing: border-box;
    .tip-right {
      display: flex;
      height: 100%;
      align-items: center;
      .close-icon {
        cursor: pointer;
      }
    }
  }

  .map-wrapper {
    flex: 1;
    box-sizing: border-box;
    padding: 30px 40px;
    overflow: hidden;
    .code-editor {
      height: 100%;
      width: 100%;
      display: flex;
      flex-direction: column;
      box-sizing: border-box;
      overflow: hidden;
      .code-preview {
        flex: 1;
        background-color: @withe;
        overflow: hidden;
        :deep(.cm-content) {
          box-sizing: border-box;
          flex: 1;
          overflow: hidden;
          padding-right: 15px;
          .cm-line {
            word-wrap: break-word;
            white-space: pre-wrap;
            word-break: normal;
          }
        }
      }
      .code-editor-operate {
        border-radius: 8px 8px 0 0;
        background-color: @withe;
        border-bottom: 1px solid #dcdfe6;
        padding: 8px 16px;
        text-align: right;
      }
      .code-editor-operate-bootom {
        border-radius: 0 0 8px 8px;
        border-top: 1px solid #dcdfe6;
        background-color: @withe;
        padding: 8px 16px;
      }
    }
  }
}
</style>
