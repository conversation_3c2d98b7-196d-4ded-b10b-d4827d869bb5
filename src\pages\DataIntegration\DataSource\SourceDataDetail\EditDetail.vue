<template>
  <el-dialog
    v-model="dialogFormVisible"
    top="10vh"
    title="编辑"
    width="600px"
    :close-on-click-modal="false"
    destroy-on-close
    @close="handleDialogClose"
  >
    <el-scrollbar height="50vh" style="padding-right: 10px">
      <el-form ref="formRef" :model="formData" scroll-to-error label-width="auto">
        <el-form-item
          v-for="p in props.fieldOptions"
          :label="p.fieldName"
          :key="p.fieldName"
          :prop="p.fieldName"
          :rules="p.notNull ? rules : null"
        >
          <el-input :disabled="p.primaryKey" v-model="formData[p.fieldName]" placeholder="请输入" />
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="setDialogFormVisible(false)">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import useDialogForm from '@/hooks/useDialogForm'
import { editPgDetailApi } from '@/api/data_source'
import { ref } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'

// openType 1 数据采集 2 数据目录
const props = withDefaults(defineProps<{ openType?: number; fieldOptions: any[] }>(), {
  openType: 1,
})

const rules = {
  required: true,
  message: '必填',
  trigger: 'blur',
}

const route = useRoute()

const formData = ref<any>({})
const { dialogFormVisible, handleDialogClose, formRef, setDialogFormVisible, validateForm } =
  useDialogForm()

const handleOpen = (row: any) => {
  formData.value = { ...row }
  setDialogFormVisible(true)
}

const handleSubmit = async () => {
  try {
    await validateForm()
    let params = {}
    if (props.openType === 1) {
      const { id, dbname } = route.query
      params = { dataSourceId: id, tableName: dbname, map: formData.value }
    } else {
      const { dataCatalogId, tableName } = route.query
      params = { dataCatalogId, tableName, map: formData.value }
    }
    const { status, message } = await editPgDetailApi(params)
    if ([200].includes(status)) {
      ElMessage.success(message)
      emit('handleRefresh')
      setDialogFormVisible(false)
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}

const emit = defineEmits<{
  (e: 'handleRefresh'): void
}>()

defineExpose({ handleOpen })
</script>

<style scoped></style>
