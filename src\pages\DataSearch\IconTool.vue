<template>
  <ul class="iconTool">
    <li v-for="p in iconList" :key="p.name" @click="emit('handleIcon', p.key)">
      <SvgIcon class="icon" :name="p.name" />
    </li>
  </ul>
</template>

<script setup lang="ts">
const iconList: any[] = [
  {
    name: 'clear',
    key: 'handleClear',
    title: '清除',
  },
  // {
  //   name: 'heatMap',
  //   key: 'handleHeatMap',
  //   title: '热力图'
  // },
  {
    name: 'reset',
    key: 'handleReset',
    title: '复位',
  },
  {
    name: 'add',
    key: 'handleAdd',
    title: '放大',
  },
  {
    name: 'minus',
    key: 'handleMinus',
    title: '缩小',
  },
]

const emit = defineEmits<{
  (e: 'handleIcon', name: string): void
}>()
</script>

<style scoped lang="less">
.iconTool {
  position: absolute;
  z-index: 100;
  right: 20px;
  bottom: 90px;
  li {
    box-sizing: border-box;
    width: 36px;
    height: 36px;
    background: #252d4b;
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #333951;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    .icon {
      font-size: 24px;
      color: @withe;
      &:hover {
        color: var(--el-color-primary);
      }
    }
  }
  li + li {
    margin-top: 5px;
  }
}
</style>
