import { v4 as uuidV4 } from 'uuid'
import useUserInfo from '@/store/useUserInfo'

interface Params {
  onOpen?: (data: any) => void
  onMessage?: (data: any) => void
  onClose?: (data: any) => void
  onError?: (data: any) => void
}

class NewWebSocket {
  webSocket: any // websocket实例
  heartbeat_timer: any // 用于心跳检测

  active_close: boolean | undefined // 是否主动关闭,

  uuid: string // 唯一标识符

  userId: any // 用户id

  url: string // url

  constructor() {
    this.webSocket = null
    this.heartbeat_timer = null
    this.active_close = false
    this.uuid = uuidV4()
    this.userId = null
    this.url = ''
  }

  initWebSocket(params: Params, isReconnect: boolean = false, socketUrl?: string) {
    const userInfo = useUserInfo()
    this.userId = userInfo.userInfo?.tid
    if (!this.userId) {
      return
    }

    if (!isReconnect) {
      if (socketUrl) {
        this.url = `${['http:'].includes(window.location.protocol) ? 'ws://' : 'wss://'}${
          window.location.host
        }${socketUrl}`
      } else {
        this.url = `${['http:'].includes(window.location.protocol) ? 'ws://' : 'wss://'}${
          window.location.host
        }${import.meta.env.VITE_BASE_SOCKE}/datamanage/${this.userId}/${this.uuid}`
      }
    }

    this.webSocket = new WebSocket(this.url)
    this.webSocket.onopen = (data: any) => {
      this.heartbeat()
      console.log('webSocket已连接')
      if (params.onOpen) {
        params.onOpen(data)
      }
    }
    this.webSocket.onmessage = (data: any) => {
      if (params.onMessage) {
        params.onMessage(data.data)
      }
    }
    this.webSocket.onclose = () => {
      clearInterval(this.heartbeat_timer)
      if (this.active_close) {
        if (params.onClose) {
          params.onClose('连接已手动关闭')
        }
      } else {
        if (this.webSocket.readyState !== 2) {
          this.reconnect(params)
          if (params.onClose) {
            params.onClose('连接已关闭, 正在重连')
          }
        }
      }
    }
    this.webSocket.onerror = () => {
      clearInterval(this.heartbeat_timer)
      if (params.onError) {
        console.log('连接发生错误！等待五秒后重连')
      }
      if (this.webSocket.readyState !== 3) {
        this.reconnect(params)
      }
    }
  }

  heartbeat() {
    console.log('执行心跳')
    if (this.heartbeat_timer) {
      clearInterval(this.heartbeat_timer)
    }
    this.heartbeat_timer = setInterval(() => {
      this.webSocket.send(`heartbeat:${this.userId}:${this.uuid}`)
    }, 3000)
  }

  reconnect(params: Params) {
    setTimeout(() => {
      console.log('执行重连')
      this.active_close = false
      if (this.webSocket) {
        this.webSocket.close()
        this.webSocket = null
      }
      this.uuid = uuidV4()
      this.initWebSocket(params, true, this.url)
    }, 5000)
  }

  close() {
    console.log('手动关闭，无需重连')
    if (this.heartbeat_timer) {
      clearInterval(this.heartbeat_timer)
      this.heartbeat_timer = null
    }
    this.active_close = true
    if (this.webSocket) {
      this.webSocket.close()
      this.webSocket = null
    }
  }
}

export default new NewWebSocket()
