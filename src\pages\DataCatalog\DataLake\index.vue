<template>
  <commonNav>
    <div class="dataHouse">
      <div class="searchTool">
        <SearchTool @handleSearchClick="handleSearchClick" />
      </div>
      <Breadcrumb :formData="formData" />
      <div class="mainContent">
        <TableList
          ref="TableListRef"
          @handleClick="handleClick"
          :table-data="tableList"
          :page-data="pageData"
        />
      </div>
      <div class="mainFooter">
        <el-pagination
          v-model:currentPage="pageData.page"
          v-model:page-size="pageData.limit"
          :page-sizes="pageData.pageSizes"
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="pageData.total"
          @size-change="pageData.handleSizeChange"
          @current-change="pageData.handleCurrentChange"
        />
      </div>
    </div>
  </commonNav>
  <ServerAddress ref="ServerAddressRef" />
  <FileMetadata
    ref="FileMetadataRef"
    :is-edit="fileIsEdit"
    @handle-refresh="pageData.handleSearch(null, 1)"
  />
  <ImagePreview ref="ImagePreviewRef" />
  <UploadFile
    ref="UploadFileRef"
    @getFileData="getFileData"
    @handle-refresh="pageData.handleSearch(null, 1)"
  />
  <!-- <OlMapPreview ref="OlMapPreviewRef" /> -->
  <MapBoxPreview ref="MapBoxPreviewRef" />
  <CesiumPreview ref="CesiumPreviewRef" />
  <MarkdownPreview ref="MarkdownPreviewRef" />
  <CodePreview ref="CodePreviewRef" />
  <VideoPreview ref="VideoPreviewRef" />
  <AudioPreview ref="AudioPreviewRef" />
  <CannotPreview ref="CannotPreviewRef" />
  <CreateFolder ref="CreateFolderRef" @handle-refresh="pageData.handleSearch(null, 1)" />
  <SelectFileTypeUpload
    ref="SelectFileTypeUploadRef"
    @handleSelectSpaceData="handleSelectSpaceData"
    @handleSelectUpload="handleSelectUpload"
  />
  <VersionManage ref="VersionManageRef" @handleClick="handleClick" />
  <GovernDetailDialog ref="GovernDetailDialogRef" />
  <RegisterDataDialog
    ref="RegisterDataDialogRef"
    :treeAsdProps="treeAsdProps"
    :productLabelData="productLabelData"
    :sharedScopeOptions="sharedScopeOptions"
    :asdData="asdData"
  />
  <RegisterServeDialog
    ref="RegisterServeDialogRef"
    :treeAsdProps="treeAsdProps"
    :productLabelData="productLabelData"
    :sharedScopeOptions="sharedScopeOptions"
    :asdData="asdData"
  />
  <VectorMap ref="VectorMapRef" />
  <VectorLayerGroupDialog
    ref="VectorLayerGroupDialogRef"
    @handle-refresh="pageData.handleSearch(null, 1)"
  />
  <CopyDataCatalogDialog ref="CopyDataCatalogDialogRef" @handle-refresh="handleRefresh" />
</template>
<script setup lang="ts">
import commonNav from '../components/commonNav.vue'
import ServerAddress from '@/components/ServerAddress/index.vue'
import FileMetadata from '@/components/FileMetadata/index.vue'
import Breadcrumb from './components/Breadcrumb.vue'
import CreateFolder from '@/components/CreateFolder/index.vue'
import TableList from './components/TableList.vue'
import SearchTool from './components/SearchTool.vue'
import usePageData from '@/hooks/usePageData'
import useFilePreview from '@/hooks/useFilePreview'
import useUpload from '@/hooks/useUpload'
import { computed, onMounted, onUnmounted, ref, watch } from 'vue'
import { userFileListApi, publishApi, getProcessUrlApi } from '@/api/data_catalog'
import { downloadBatchFileApi, downloadFileApi } from '@/api/common'
import { downloadFile } from '@/utils/fileUtils'
import { ElMessage, ElMessageBox } from 'element-plus'
import { removeApi } from '@/api/data_catalog'
import { previewList } from '@/utils/fileMap'
import useUserInfo from '@/store/useUserInfo'
import useSocket from '@/store/useSocket'
import VersionManage from './components/VersionManage.vue'
import GovernDetailDialog from './components/GovernDetailDialog.vue'
import RegisterDataDialog from './components/RegisterDataDialog.vue'
import RegisterServeDialog from './components/RegisterServeDialog.vue'
import useRegisterData from './hooks/useRegisterData'
import VectorMap from '@/components/VectorMap/index.vue'
import { useRoute, useRouter } from 'vue-router'
import VectorLayerGroupDialog from './components/VectorLayerGroupDialog.vue'
import CopyDataCatalogDialog from './components/CopyDataCatalogDialog.vue'
const userInfo = useUserInfo()

const router = useRouter()
const route = useRoute()

const { treeAsdProps, productLabelData, sharedScopeOptions, asdData } = useRegisterData()

// 列表实例
const TableListRef = ref<InstanceType<typeof TableList>>()

// 文件路径
const filePath = computed<any>(() => route.query?.filePath || '/')
const dataCatalogId = computed<any>(() => route.query?.dataCatalogId)
const { pageData, tableData } = usePageData(userFileListApi, false)

// 加载列表数据
onMounted(() => {
  pageData.handleSearch({ dataCatalogId: dataCatalogId.value, filePath }, 1)
})

// 监听数据加载
watch([dataCatalogId, filePath], () => {
  formData.value = {}
  TableListRef.value?.clearSort()
  TableListRef.value?.clearSelection()
  pageData.handleSearch({ dataCatalogId: dataCatalogId.value, filePath }, 1)
})

// 批量数据处理
const selectData = ref<any[]>([])
const handleSelectionChange = (value: any[]) => {
  selectData.value = value.map((item: any) => item.tid)
}

// 新建文件夹
const CreateFolderRef = ref<any>()

// 文件上传逻辑处理
const {
  UploadFileRef,
  handleUpload,
  handleSelectUpload,
  UploadFile,
  SelectFileTypeUploadRef,
  SelectFileTypeUpload,
  handleSelectFileTypeUpload,
  handleSelectSpaceData,
  getFileData,
} = useUpload()

const CopyDataCatalogDialogRef = ref<InstanceType<typeof CopyDataCatalogDialog>>()
const handleRefresh = () => {
  pageData.handleSearch(null, 1)
  selectData.value = []
  TableListRef.value?.clearSelection()
}

// 搜索组件各种点击事件处理
const formData = ref<any>({})
const handleSearchClick = (name: string, form?: any) => {
  switch (name) {
    case 'handleSearch':
      const { dateRange } = form
      const params = {
        ...form,
        dataCatalogId: dataCatalogId.value,
        filePath: form.fileName && ['/'].includes(filePath.value) ? undefined : filePath.value,
        startTime: dateRange && dateRange.length ? dateRange[0] : undefined,
        endTime: dateRange && dateRange.length ? dateRange[1] : undefined,
      }
      delete params.dateRange
      formData.value = params
      pageData.handleSearch(params, 1)
      return
    case 'handleReset':
      // 重置查询条件
      formData.value = {}
      // 清除表格排序和选择
      TableListRef.value?.clearSort()
      TableListRef.value?.clearSelection()
      // 重新加载数据
      pageData.handleSearch({ dataCatalogId: dataCatalogId.value, filePath: filePath.value }, 1)
      return
    case 'handleUpload':
      if ([1, 2].includes(form)) {
        handleUpload(form)
      } else {
        handleSelectFileTypeUpload()
      }
      return
    case 'handleCopyData':
      if (!selectData.value.length) {
        ElMessage.warning('请选择需要复制的数据！')
        return
      }
      CopyDataCatalogDialogRef.value?.handleOpen(selectData.value, 1)
      return
    case 'handleMove':
      if (!selectData.value.length) {
        ElMessage.warning('请选择需要移动的数据！')
        return
      }
      CopyDataCatalogDialogRef.value?.handleOpen(selectData.value, 0)
      return
    case 'handleDelete':
      if (!selectData.value.length) {
        ElMessage.warning('请选择需要删除的数据！')
        return
      }
      handleDel(selectData.value)
      return
    case 'handleDownload':
      if (!selectData.value.length) {
        ElMessage.warning('请选择需要下载的数据！')
        return
      }
      downloadFile(
        `${downloadBatchFileApi()}?tids=${selectData.value.join(',')}&token=${userInfo.token}`,
      )
      return
    case 'handleCreateFolder':
      CreateFolderRef.value?.handleOpen()
      return
    default:
      break
  }
}

// 各种文件的预览
const {
  CannotPreview,
  CannotPreviewRef,
  AudioPreview,
  AudioPreviewRef,
  VideoPreview,
  VideoPreviewRef,
  CodePreview,
  CodePreviewRef,
  ImagePreview,
  ImagePreviewRef,
  // OlMapPreview,
  // OlMapPreviewRef,
  MapBoxPreview,
  MapBoxPreviewRef,
  CesiumPreview,
  CesiumPreviewRef,
  MarkdownPreview,
  MarkdownPreviewRef,
  handleFilePreview,
  handleCodePreview,
  handleMarkdownPreview,
} = useFilePreview()

// 数据各种操作
const handleClick = (name: string, row: any, isEdit: boolean = true) => {
  console.log(name, row, isEdit)
  switch (name) {
    case 'handleSortChange':
      const params = {
        ...formData.value,
        ...row,
      }
      formData.value = params
      pageData.handleSearch(
        {
          ...params,
          dataCatalogId: dataCatalogId.value,
          filePath:
            formData.value.fileName && ['/'].includes(filePath.value) ? undefined : filePath.value,
        },
        1,
      )
      return
    case 'handleDetails':
      fileIsEdit.value = isEdit
      FileMetadataRef.value.handleOpen(row)
      return
    case 'handleEdit':
      fileIsEdit.value = isEdit
      handleEdit(row)
      return
    case 'handleFilePreview':
      handleFilePreview(
        row,
        previewList.includes(formData.value?.fileTypeId) ? tableData.value : [],
      )
      return
    case 'handlePublish':
      handleServePublish(row)
      return
    case 'handleShareServe':
      ServerAddressRef.value?.handleOpen(row)
      return
    case 'handleDownload':
      downloadFile(`${downloadFileApi()}?tid=${row.tid}&token=${userInfo.token}`)
      return
    case 'handleDel':
      handleDel([row.tid])
      return
    case 'handleSelectionChange':
      handleSelectionChange(row)
      return
    case 'handleVersion':
      VersionManageRef.value?.handleOpen(row)
      return
    case 'handleGovern':
      GovernDetailDialogRef.value?.handleOpen(row)
      return
    case 'handleRegisterData':
      RegisterDataDialogRef.value?.handleOpen(row)
      return
    case 'handleRegisterServe':
      RegisterServeDialogRef.value?.handleOpen(row)
      return
    case 'handlePicture':
      VectorMapRef.value?.handleOpenPreview(row)
      // handlePicture(row)
      return
    case 'handleSlicePicture':
      handleSlicePicture()
      return
    case 'handleCodeEdit':
      handleCodePreview(row, true)
      return
    case 'handleMarkdownEdit':
      handleMarkdownPreview(row, true)
      return
    case 'handleOfficeEdit':
      const { href } = router.resolve({
        name: 'onlyOffice',
        query: {
          userFileId: row.tid,
          isEdit: 1,
        },
      })
      window.open(href, '_blank')
      return
    default:
      break
  }
}

// 切片处理
const handleSlicePicture = async () => {
  try {
    const { status, message, data } = await getProcessUrlApi()
    if ([200].includes(status)) {
      window.open(data?.processWebUrl, '_blank')
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}

// 在线配图
const VectorMapRef = ref<any>()

// 注册数据
const RegisterDataDialogRef = ref<any>()

// 注册服务
const RegisterServeDialogRef = ref<any>()

// 治理详情
const GovernDetailDialogRef = ref<any>()

// 版本管理
const VersionManageRef = ref<any>()

// 服务列表
const ServerAddressRef = ref<any>(null)

// 发布服务
const VectorLayerGroupDialogRef = ref<any>()
const handleServePublish = (row: any) => {
  const { isThematic, analysisStatus } = row
  if ([1].includes(analysisStatus)) {
    if ([1].includes(isThematic)) {
      VectorLayerGroupDialogRef.value?.handleOpen(row)
    } else {
      handlePublish(row)
    }
  } else if ([0].includes(analysisStatus)) {
    ElMessage.warning('服务解析失败！')
  } else {
    ElMessage.warning('服务正在解析中,请稍等...')
  }
}
const handlePublish = async ({ tid }: any) => {
  try {
    const { message, status } = await publishApi(tid)
    if ([200].includes(status)) {
      ElMessage.success(message)
      pageData.handleSearch(null, 1)
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}

// 删除
const handleDel = (tid: any[]) => {
  ElMessageBox.confirm('确认要删除该数据嘛？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    try {
      const { status, message } = await removeApi(tid)
      if ([200].includes(status)) {
        ElMessage.success(message)
        handleRefresh()
      } else {
        ElMessage.error(message)
      }
    } catch (error) {
      console.error(error)
    }
  })
}

// 元数据编辑
const FileMetadataRef = ref<any>()
const fileIsEdit = ref<boolean>(true)
const handleEdit = (row: any) => {
  if (row.isDir) {
    CreateFolderRef.value?.handleOpen(row)
  } else {
    FileMetadataRef.value?.handleOpen(row)
  }
}

// socket时时获取图片地址服务状态
const socket = useSocket()

// 列表数据
const tableList = computed<any[]>(() => {
  if (socket.socketData && Object.keys(socket.socketData).length) {
    return tableData.value.map((item: any) => {
      if (socket.socketData.userFileId === item.tid) {
        if (socket.socketData?.serverStatus || [0].includes(socket.socketData?.serverStatus)) {
          item.serverStatus = Number(socket.socketData?.serverStatus)
        }
        if (socket.socketData?.serverStatusName) {
          item.serverStatusName = socket.socketData?.serverStatusName
        }
        if (socket.socketData?.analysisStatus || [0].includes(socket.socketData?.analysisStatus)) {
          item.analysisStatus = socket.socketData?.analysisStatus
        }
        if (socket.socketData?.analysisStatusName) {
          item.analysisStatusName = socket.socketData?.analysisStatusName
        }
        if (socket.socketData?.thumbnailUrl) {
          item.metadataVO.thumbnailUrl = socket.socketData?.thumbnailUrl
        }
        if (socket.socketData?.isThematic || [0].includes(socket.socketData?.isThematic)) {
          item.isThematic = socket.socketData?.isThematic
        }
      }
      return item
    })
  }
  return tableData.value
})

onMounted(() => {
  socket.handleCreateSocket()
})

onUnmounted(() => {
  socket.handleCloseSocket()
})

const marginTop = computed<string>(() => (dataCatalogId.value ? '15px' : '0px'))
</script>
<style lang="less" scoped>
.dataHouse {
  height: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .searchTool {
    padding: 20px;
    background-color: @withe;
  }

  .mainContent {
    margin-top: v-bind(marginTop);
    flex: 1;
    box-sizing: border-box;
    overflow: hidden;
  }

  .mainFooter {
    background-color: @withe;
    box-sizing: border-box;
    padding: 15px 20px 15px 20px;
    display: flex;
    justify-content: flex-end;
  }
}
</style>
