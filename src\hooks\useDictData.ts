import { dictDataListApi } from '@/api/common'
import { ElMessage } from 'element-plus'
import { ref } from 'vue'

const useDictData = (type: string) => {
  const dicData = ref<any[]>([])

  const getDict = async () => {
    try {
      const { data, message, status } = await dictDataListApi({ typeCode: type })
      if ([200].includes(status)) {
        dicData.value = data
      } else {
        ElMessage.error(message)
      }
    } catch (error) {}
  }

  getDict()

  return dicData
}

export default useDictData
