import request from '@/utils/request'

const baseIotURL = '/iot'

// 查询服务列表
export const IotlistApi = (params: any) => request.get(`${baseIotURL}/v1/iot/list`, params)

// 发布服务
export const IotCreateApi = (params: any) => request.post(`${baseIotURL}/v1/iot/create`, params)

// 更新服务
export const IotUpdateApi = (params: any) => request.post(`${baseIotURL}/v1/iot/update`, params)

// 启动服务
export const IotStartApi = (id: any) => request.post(`${baseIotURL}/v1/iot/start/${id}`)

// 停止服务
export const IotStopApi = (id: any) => request.get(`${baseIotURL}/v1/iot/stop/${id}`)

// 根据服务表主键Id查询任务详情
export const IotServerApi = (id: any) => request.get(`${baseIotURL}/v1/iot/server/${id}`)

// 获取指定服务的扩展字段
export const IotExtParamsApi = (id: any) => request.get(`${baseIotURL}/v1/iot/extParams/${id}`)

// 删除服务
export const IotDelIdApi = (id: any) => request.get(`${baseIotURL}/v1/iot/delId/${id}`)

// 常量字典
export const IotDictApi = () => request.get(`${baseIotURL}/v1/dict/map`)

// 回放数据
export const IotPlaybackApi = (params: any) => request.get(`${baseIotURL}/v1/data/playback`, params)

// 经纬度范围查询服务
export const IotGetByBoxApi = (params: any) => request.post(`${baseIotURL}/v1/iot/getByBox`, params)

// 根据服务表主键Id查询任务详情
export const IotServerDetailsApi = (id: any) => request.get(`${baseIotURL}/v1/iot/server/${id}`)

// 启动服务
export const IotServerStartApi = (id: any) => request.post(`${baseIotURL}/v1/iot/start/${id}`)

// 发布服务
export const IotServerStopApi = (id: any) => request.get(`${baseIotURL}/v1/iot/stop/${id}`)
