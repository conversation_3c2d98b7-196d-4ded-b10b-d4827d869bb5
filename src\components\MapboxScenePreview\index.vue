<template>
  <transition name="fade">
    <div v-if="showViewer" class="common-preview-wrapper">
      <div class="tip-wrapper">
        <div></div>
        <p>{{ rowData.name }}</p>
        <div class="tip-right">
          <el-select
            size="small"
            style="width: 100px; margin-right: 20px"
            v-model="basemapType"
            placeholder="底图"
            @change="handleChangeMap"
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
          <SvgIcon name="closeBold" @click="handleClose" class="close-icon" />
        </div>
      </div>
      <div class="map-wrapper">
        <div class="olMap" ref="mapRef">
          <MapboxTimeLine
            v-show="sceneData.rowList.length"
            @hanlde-is-paly="hanldeIsPaly"
            :palyList="sceneData.rowList"
            :currentIndex="sceneData.currentIndex"
            :isPlay="sceneData.isPlay"
          />
        </div>
      </div>
    </div>
  </transition>
</template>

<script setup lang="ts">
import useMapbox from '@/hooks/useMapbox'
import useScenePreview from '@/hooks/useScenePreview'
import { ref, nextTick } from 'vue'
import MapboxTimeLine from '@/components/MapboxTimeLine/index.vue'

const { initMap, mapRef, setPreviewLayers, handleChangeMap } = useMapbox()
const { sceneData, handleScenePreview, hanldeIsPaly, resetInterval } =
  useScenePreview(setPreviewLayers)

const showViewer = ref<boolean>(false)
const handleClose = () => {
  resetInterval()
  showViewer.value = false
}

const basemapType = ref<string>('img')
const options: any[] = [
  { value: 'img', label: '星图影像' },
  { value: 'vec', label: '星图矢量' },
]

const rowData = ref<any>({})

const handleOpenPreview = (row: any) => {
  showViewer.value = true
  rowData.value = row
  nextTick(async () => {
    initMap(() => {
      handleScenePreview(
        row.fileList.map((item: any) => ({
          fileName: item.fileName,
          sceneTimeFormat: item.sceneTimeFormat,
          tid: item.userFileId,
          gisPreviewVO: item.detail.gisPreviewVO,
          metadataVO: item.detail.metadataVO,
        })),
        row.interval,
      )
    })
  })
}

defineExpose({ handleOpenPreview })
</script>

<style lang="less" scoped>
.common-preview-wrapper {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  .tip-wrapper {
    background: rgba(0, 0, 0, 0.5);
    padding: 0 48px;
    width: 100%;
    height: 48px;
    line-height: 48px;
    color: @withe;
    font-size: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-sizing: border-box;
    .tip-right {
      display: flex;
      height: 100%;
      align-items: center;
      .close-icon {
        cursor: pointer;
      }
    }
  }
  .map-wrapper {
    flex: 1;
    box-sizing: border-box;
    padding: 30px 40px;
    .olMap {
      height: 100%;
      width: 100%;
      box-sizing: border-box;
      background-color: rgba(0, 0, 0, 0.9);
      position: relative;
      :global(.mapboxgl-ctrl-bottom-left) {
        display: none;
      }
    }
  }
}
</style>
