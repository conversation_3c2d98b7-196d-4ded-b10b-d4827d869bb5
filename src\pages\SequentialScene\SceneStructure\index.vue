<template>
  <div class="sceneStructure">
    <LeftEdit @handle-preview="handlePreview" @handle-scene-preview="handleScenePreview" />
    <div class="mapbox-view" ref="mapRef">
      <MapboxTimeLine
        v-show="sceneData.rowList.length"
        @hanlde-is-paly="hanldeIsPaly"
        :palyList="sceneData.rowList"
        :currentIndex="sceneData.currentIndex"
        :isPlay="sceneData.isPlay"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import LeftEdit from '@/pages/SequentialScene/SceneStructure/LeftEdit.vue'
import MapboxTimeLine from '@/components/MapboxTimeLine/index.vue'
import useMapbox from '@/hooks/useMapbox'
import useScenePreview from '@/hooks/useScenePreview'
import { onMounted } from 'vue'

const { mapRef, setPreviewLayers, initMap } = useMapbox()
const { sceneData, hanldeIsPaly, handlePreview, handleScenePreview } =
  useScenePreview(setPreviewLayers)

onMounted(() => {
  initMap()
})
</script>

<style lang="less" scoped>
.sceneStructure {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  background-color: @withe;
  overflow: hidden;
  box-shadow: 0px 8px 8px 8px rgba(0, 0, 0, 0.05);
  padding: 20px;
  display: flex;
  .mapbox-view {
    flex: 1;
    box-sizing: border-box;
    overflow: hidden;
    position: relative;
    :global(.mapboxgl-ctrl-bottom-left) {
      display: none;
    }
  }
}
</style>
