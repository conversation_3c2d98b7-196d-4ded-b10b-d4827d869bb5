<template>
  <ElRow :gutter="20" style="box-sizing: border-box; margin: 0">
    <ElCol :span="12">
      <ElFormItem label="任务名称" prop="taskDTO.taskName">
        <ElInput disabled v-model="formData.taskDTO.taskName" placeholder="请输入任务名称" />
      </ElFormItem>
    </ElCol>
    <ElCol :span="12"></ElCol>
    <ElCol :span="12">
      <ElFormItem label="入库方式" prop="dataType">
        <ElRadioGroup disabled v-model="formData.dataType">
          <ElRadio :value="2" label="数据入湖" />
          <ElRadio :value="1" label="数据入仓" />
        </ElRadioGroup>
      </ElFormItem>
    </ElCol>
    <!-- 数据入湖 -->
    <template v-if="[2].includes(formData.dataType)">
      <ElCol :span="12">
        <ElFormItem label="原始数据源" prop="datasourceId">
          <ElSelect disabled v-model="formData.datasourceId" placeholder="请选择原始数据源">
            <ElOption
              v-for="item in dataSourceList"
              :key="item.tid"
              :label="item.datasourceName"
              :value="item.tid"
            />
          </ElSelect>
        </ElFormItem>
      </ElCol>
      <ElCol :span="24">
        <ElFormItem class="fromTable" label="选择文件" prop="dataImportFileDTOList">
          <div style="width: 100%">
            <el-table
              header-cell-class-name="common-table-header"
              cell-class-name="common-table-cell"
              height="250px"
              :data="formData.dataImportFileDTOList"
              ref="multipleTableRef"
              row-key="fullPath"
              border
            >
              <el-table-column width="50">
                <template #default="scope">
                  <img
                    :src="scope.row.dir ? fileDir : file"
                    width="30"
                    height="30"
                    alt="img"
                    style="cursor: pointer"
                  />
                </template>
              </el-table-column>
              <el-table-column property="tableName" label="名称" show-overflow-tooltip>
                <template #default="scope">
                  <span>
                    {{ scope.row.fileName || scope.row.filePath }}
                  </span>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem label="数据目录" prop="dataCatalogId">
          <ElTreeSelect
            disabled
            v-model="formData.dataCatalogId"
            :data="dataCataLogSource"
            check-strictly
            :props="{
              label: 'catalogAlias',
              value: 'tid',
              children: 'childVOList',
              disabled: 'disabled',
            }"
            value-key="tid"
            style="width: 100%"
          />
        </ElFormItem>
      </ElCol>
    </template>
    <!-- 数据入仓 -->
    <template v-if="[1].includes(formData.dataType)">
      <ElCol :span="12"></ElCol>
      <ElCol :span="12">
        <ElFormItem label="原数据库" prop="datasourceId">
          <ElSelect disabled v-model="formData.datasourceId" placeholder="请选择原始数据源">
            <ElOption
              v-for="item in dataSourceList"
              :key="item.tid"
              :label="item.datasourceName"
              :value="item.tid"
            />
          </ElSelect>
        </ElFormItem>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem label="目标数据库" prop="dataCatalogId">
          <ElTreeSelect
            disabled
            v-model="formData.dataCatalogId"
            :data="dataCataLogSource"
            check-strictly
            :props="{
              label: 'catalogAlias',
              value: 'tid',
              children: 'childVOList',
              disabled: (_data: any, node: any) => {
                if (node) {
                  return node?.data?.bsType === 1
                }
                return false
              },
            }"
            value-key="tid"
            style="width: 100%"
          />
        </ElFormItem>
      </ElCol>
      <ElCol :span="24">
        <ElFormItem class="fromTable" label="入库方式" prop="dataImportFileDTOList">
          <ElTable
            :data="formData.dataImportFileDTOList"
            header-cell-class-name="common-table-header"
            cell-class-name="common-table-cell"
            :max-height="300"
            border
            row-key="fileName"
          >
            <el-table-column property="targetFileName" label="数据库表名" />
          </ElTable>
        </ElFormItem>
      </ElCol>
      <ElCol :span="24">
        <ElFormItem label="健康度评分标准" prop="qaScoreId">
          <ElSelect disabled v-model="formData.qaScoreId" placeholder="请选择健康度评分标准">
            <ElOption v-for="p in scoreList" :key="p.tid" :label="p.scoreName" :value="p.tid" />
          </ElSelect>
        </ElFormItem>
      </ElCol>
    </template>
    <ElCol :span="12">
      <ElFormItem label="定时任务" prop="taskDTO.timerType">
        <ElRadioGroup disabled v-model="formData.taskDTO.timerType">
          <el-radio :value="1" label="否" />
          <el-radio :value="2" label="是" />
        </ElRadioGroup>
      </ElFormItem>
    </ElCol>
    <template v-if="[2].includes(formData.taskDTO.timerType)">
      <ElCol :span="24">
        <el-form-item label="执行周期" required>
          <div class="select-model">
            <el-form-item style="width: 150px" prop="taskDTO.timerCycle">
              <el-select disabled v-model="formData.taskDTO.timerCycle" placeholder="请选择">
                <el-option label="小时" :value="5" />
                <el-option label="天" :value="4" />
                <el-option label="周" :value="3" />
                <el-option label="月" :value="2" />
              </el-select>
            </el-form-item>
            <div class="select-row">
              <el-form-item
                v-if="[3].includes(formData.taskDTO.timerCycle)"
                class="row-item"
                prop="taskDTO.timerCycleDay"
              >
                <el-select disabled v-model="formData.taskDTO.timerCycleDay" placeholder="选择时间">
                  <el-option label="星期一" :value="1" />
                  <el-option label="星期二" :value="2" />
                  <el-option label="星期三" :value="3" />
                  <el-option label="星期四" :value="4" />
                  <el-option label="星期五" :value="5" />
                  <el-option label="星期六" :value="6" />
                  <el-option label="星期日" :value="7" />
                </el-select>
              </el-form-item>
              <el-form-item
                v-if="[2].includes(formData.taskDTO.timerCycle)"
                class="row-item"
                prop="taskDTO.timerCycleDay"
              >
                <el-select disabled v-model="formData.taskDTO.timerCycleDay" placeholder="选择时间">
                  <el-option v-for="p in 30" :key="p" :label="p" :value="p" />
                </el-select>
              </el-form-item>
              <el-form-item class="row-item" prop="taskDTO.timerCycleHour">
                <el-time-select
                  disabled
                  v-show="[5].includes(formData.taskDTO.timerCycle)"
                  v-model="formData.taskDTO.timerCycleHour"
                  start="00:00"
                  step="00:01"
                  end="0:59"
                  placeholder="选择时间"
                />
                <el-time-select
                  disabled
                  v-show="![5].includes(formData.taskDTO.timerCycle)"
                  v-model="formData.taskDTO.timerCycleHour"
                  start="00:00"
                  step="00:01"
                  end="23:59"
                  placeholder="选择时间"
                />
              </el-form-item>
            </div>
          </div>
        </el-form-item>
      </ElCol>
      <ElCol :span="12">
        <ElFormItem label="立即执行" prop="taskDTO.isRun">
          <ElRadioGroup disabled v-model="formData.taskDTO.isRun">
            <ElRadio :value="1" label="是" />
            <ElRadio :value="2" label="否" />
          </ElRadioGroup>
        </ElFormItem>
      </ElCol>
    </template>
    <ElCol :span="24">
      <el-form-item label="描述信息" prop="note">
        <el-input
          disabled
          :rows="6"
          v-model="formData.note"
          placeholder="请输入描述信息"
          type="textarea"
        />
      </el-form-item>
    </ElCol>
    <slot></slot>
  </ElRow>
</template>
<script lang="ts" setup>
import fileDir from '@/assets/fileIconImg/file_dir.png'
import file from '@/assets/commonImg/file.png'
import { queryListByParamApi } from '@/api/data_import'
import { ElInput, ElFormItem, ElCol, ElRow, ElRadioGroup, ElRadio, ElMessage } from 'element-plus'
import { ref } from 'vue'
import { configQueryListApi } from '@/api/catalog_config'
import { qaScoreListApi } from '@/api/data_governance'

const props = defineProps<{ formData: any }>()

const dataSourceList = ref<any[]>([])
const getDataSourceList = async (dataType: number) => {
  try {
    const { data, message, status } = await queryListByParamApi({ dataType })
    if (status === 200) {
      dataSourceList.value = data
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}
getDataSourceList(props.formData.dataType)

const dataCataLogSource = ref<any[]>([])
const getDataCataLogSource = async (dataType: number) => {
  try {
    const { data, message, status } = await configQueryListApi({ dataType })
    if (status === 200) {
      dataCataLogSource.value = data
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}
getDataCataLogSource(props.formData.dataType)

const scoreList = ref<any[]>([])
const getScoreList = async (form: any = {}) => {
  try {
    const { data, status, message } = await qaScoreListApi(form)
    if ([200].includes(status)) {
      scoreList.value = data
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}
getScoreList()
</script>
<style scoped lang="less">
.select-model {
  width: 100%;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  .select-row {
    margin-left: 15px;
    flex: 1;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    .row-item {
      flex: 1;
      box-sizing: border-box;
    }
    .row-item + .row-item {
      margin-left: 15px;
    }
  }
}
</style>
