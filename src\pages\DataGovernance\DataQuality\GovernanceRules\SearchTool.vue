<template>
  <el-form ref="ruleFormRef" :model="ruleForm">
    <div class="search-content">
      <div class="search-input">
        <div class="search-item">
          <el-form-item prop="autoRuleName">
            <el-input
              class="form-item"
              v-model="ruleForm.autoRuleName"
              placeholder="请输入规则名称"
              @keydown.enter="handleSearch"
            >
              <template #suffix>
                <SvgIcon @click="handleSearch" style="cursor: pointer" name="search" />
              </template>
            </el-input>
          </el-form-item>
        </div>
        <div class="search-item">
          <el-form-item prop="fileTypeId">
            <el-cascader
              style="width: 100%"
              v-model="ruleForm.fileTypeId"
              :options="globalData.catalogMenuList"
              :props="props"
              clearable
              @change="handleSearch"
            />
          </el-form-item>
        </div>
      </div>
    </div>
  </el-form>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import type { FormInstance } from 'element-plus'
import useGlobalData from '@/store/useGlobalData'

const globalData = useGlobalData()
const props = {
  value: 'tid',
  label: 'fileTypeName',
  children: 'childVOList',
  emitPath: false,
}

const ruleFormRef = ref<FormInstance>()
const ruleForm = ref<any>({})

const handleSearch = () => {
  emit('handleSearch', ruleForm.value)
}

const emit = defineEmits<{
  (e: 'handleSearch', form: any): void
}>()
</script>
