<template>
  <div class="dataRecovery">
    <img :src="recovery" alt="recovery" />
    <p>数据恢复中，请稍后...</p>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, ref } from 'vue'
import recovery from './recovery.png'
import { recoveryFinishApi } from '@/api/data_security'
import { useRouter } from 'vue-router'

const router = useRouter()
const timer = ref<any>(null)

const initData = async () => {
  try {
    const { data, status } = await recoveryFinishApi()
    if ([200].includes(status) && data) {
      router.replace({ name: 'home' })
    }
  } catch (error) {}
}

onMounted(() => {
  initData()
  timer.value = setInterval(() => {
    initData()
  }, 3000)
})

onUnmounted(() => {
  if (timer.value) {
    clearInterval(timer.value)
    timer.value = null
  }
})
</script>

<style lang="less" scoped>
.dataRecovery {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  p {
    margin-top: 60px;
    font-size: 30px;
    font-weight: 400;
    color: #333333;
  }
}
</style>
