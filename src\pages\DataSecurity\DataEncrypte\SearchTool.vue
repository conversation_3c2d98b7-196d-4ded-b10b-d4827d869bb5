<template>
  <el-form ref="ruleFormRef" :model="ruleForm">
    <div class="search-content">
      <div class="search-input">
        <div class="search-item">
          <el-form-item prop="fileName">
            <el-input
              class="form-item"
              v-model="ruleForm.fileName"
              placeholder="请输入文件名称"
              @keydown.enter="handleSearch"
            >
              <template #suffix>
                <SvgIcon @click="handleSearch" style="cursor: pointer" name="search" />
              </template>
            </el-input>
          </el-form-item>
        </div>
        <div class="search-item">
          <el-form-item prop="fileTypeId">
            <el-cascader
              :props="catalogProps"
              :options="catalogOptions"
              style="width: 100%"
              v-model="ruleForm.fileTypeId"
              placeholder="请选择数据类型"
              :show-all-levels="false"
              @change="handleSearch"
            />
          </el-form-item>
        </div>
      </div>
      <div
        v-if="
          permission.hasButton(['dataEncrypte_del', 'dataEncrypte_add', 'dataEncrypte_edit']) &&
          isBtn
        "
        class="search-btn"
      >
        <el-button
          v-if="permission.hasButton('dataEncrypte_add')"
          type="primary"
          @click="emit('handleCreate')"
        >
          <template #icon>
            <SvgIcon name="add" />
          </template>
          新增数据加密
        </el-button>
        <el-button
          v-if="permission.hasButton('dataEncrypte_edit')"
          type="primary"
          @click="emit('handleUpdate')"
        >
          <template #icon>
            <SvgIcon name="edit" />
          </template>
          修改密码
        </el-button>
        <el-button
          v-if="permission.hasButton('dataEncrypte_del')"
          type="danger"
          @click="emit('handleDel')"
        >
          <template #icon>
            <SvgIcon name="Unlock" />
          </template>
          批量脱敏
        </el-button>
      </div>
    </div>
  </el-form>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'
import type { CascaderProps, FormInstance } from 'element-plus'
import useGlobalData from '@/store/useGlobalData'
import useUserInfo from '@/store/useUserInfo'

defineProps<{ isBtn?: boolean }>()

const permission = useUserInfo()

// 数据类型
const globalData = useGlobalData()
const catalogOptions = globalData.catalogMenuList
const catalogProps: CascaderProps = {
  value: 'tid',
  label: 'fileTypeName',
  children: 'childVOList',
  emitPath: false,
}

const ruleFormRef = ref<FormInstance>()
const ruleForm = reactive<any>({})

const handleSearch = () => {
  emit('handleSearch', ruleForm)
}

const emit = defineEmits<{
  (e: 'handleSearch', form: any): void
  (e: 'handleCreate'): void
  (e: 'handleUpdate'): void
  (e: 'handleDel'): void
}>()
</script>
