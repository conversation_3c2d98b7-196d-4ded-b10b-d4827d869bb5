<template>
  <el-dialog
    v-model="dialogFormVisible"
    top="10vh"
    title="新建备份任务"
    width="500px"
    :close-on-click-modal="false"
    @close="handleDialogClose"
  >
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="100px">
      <el-form-item label="任务名称" prop="taskName">
        <el-input v-if="isEditShow" v-model="formData.taskName" placeholder="请输入任务名称" />
        <span v-else>{{ formData.taskName }}</span>
      </el-form-item>
      <el-form-item label="数据源" prop="datasourceName">
        <div v-if="isEditShow" class="data-source">
          <el-select
            style="flex: 1"
            value-key="datasourceId"
            v-model="formData.datasourceName"
            placeholder="请选择数据源"
            @change="handleChange"
          >
            <el-option
              v-for="p in dataSourceOptions"
              :key="p.tid"
              :label="p.datasourceName"
              :value="p"
            />
          </el-select>
          <el-button
            @click="handleCreate"
            style="margin-left: 10px; font-size: 12px"
            type="primary"
            plain
            link
          >
            新建数据源
          </el-button>
        </div>
        <span v-else>{{ formData.datasourceName }}</span>
      </el-form-item>
      <el-form-item label="服务器地址" prop="hosts">
        <el-input
          v-if="isEditShow"
          disabled
          v-model="formData.hosts"
          placeholder="请输入访问秘钥"
        />
        <span v-else>{{ formData.hosts }}</span>
      </el-form-item>
      <el-form-item label="端口" prop="ports">
        <el-input
          v-if="isEditShow"
          disabled
          v-model="formData.ports"
          placeholder="请输入安全秘钥"
        />
        <span v-else>{{ formData.ports }}</span>
      </el-form-item>
      <el-form-item label="用户名" prop="usernames">
        <el-input
          v-if="isEditShow"
          disabled
          v-model="formData.usernames"
          placeholder="请输入桶名"
        />
        <span v-else>{{ formData.usernames }}</span>
      </el-form-item>
      <el-form-item label="密码" prop="passwords">
        <el-input
          v-if="isEditShow"
          disabled
          v-model="formData.passwords"
          placeholder="请输入桶名"
        />
        <span v-else>{{ formData.passwords }}</span>
      </el-form-item>
      <el-form-item v-if="isEditShow" label="是否立即执行">
        <el-switch v-model="formData.startNow" />
      </el-form-item>
      <el-form-item label="描述" prop="notes">
        <el-input
          v-if="isEditShow"
          :rows="4"
          type="textarea"
          v-model="formData.notes"
          placeholder="请输入桶名"
        />
        <span v-else>{{ formData.notes }}</span>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="setDialogFormVisible(false)">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import useDialogForm from '@/hooks/useDialogForm'
import { listDatasourceApi, addOneApi } from '@/api/data_security'
import { ElMessage } from 'element-plus'

const { dialogFormVisible, setDialogFormVisible, handleDialogClose, formRef, validateForm } =
  useDialogForm()

// 新建数据源
const router = useRouter()
const handleCreate = () => {
  router.push({ name: 'dataSourceAddEditDetail', query: { id: undefined, isEdit: 'true' } })
}

// 数据源
const dataSourceOptions = ref<any[]>([])
const getOptions = async () => {
  try {
    const { data, status, message } = await listDatasourceApi()
    if ([200].includes(status)) {
      dataSourceOptions.value = data
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}
const handleChange = (p: any) => {
  const { datasourceId, hosts, passwords, ports, usernames } = p
  formData.value.datasourceId = datasourceId
  formData.value.hosts = hosts
  formData.value.passwords = passwords
  formData.value.ports = ports
  formData.value.usernames = usernames
}

const formData = ref<any>({})
const isEditShow = ref<boolean>(false)
const handleOpen = (row: any, isEdit: boolean) => {
  if (isEdit) {
    getOptions()
  }
  if (row) {
    formData.value = { ...row }
  } else {
    formData.value = {}
  }
  isEditShow.value = isEdit
  setDialogFormVisible(true)
}

const handleSubmit = async () => {
  if (!isEditShow.value) {
    setDialogFormVisible(false)
    return
  }
  try {
    await validateForm()
    const { status, message } = await addOneApi(formData.value)
    if ([200].includes(status)) {
      ElMessage.success(message)
      emit('handleRefresh')
      setDialogFormVisible(false)
    } else {
      ElMessage.error(message)
    }
  } catch (error) {
    console.error(error)
  }
}

const rules = reactive<any>({
  taskName: [{ required: true, message: '任务名称必填', trigger: 'blur' }],
  datasourceName: [{ required: true, message: '数据源必填', trigger: 'blur' }],
})

defineExpose({ handleOpen })
const emit = defineEmits<{
  (e: 'handleRefresh'): void
}>()
</script>

<style scoped lang="less">
.data-source {
  display: flex;
  align-items: center;
  width: 100%;
}
</style>
