<template>
  <ElDialog
    title="注册数据"
    top="10vh"
    width="600px"
    :close-on-click-modal="false"
    v-model="dialogFormVisible"
    @close="handleDialogClose"
  >
    <ElScrollbar height="60vh">
      <ElForm
        ref="formRef"
        scroll-to-error
        :disabled="isRegister"
        :model="formData"
        :rules="formRule"
        label-width="80px"
      >
        <ElRow class="row" :gutter="20">
          <ElCol class="title" :span="24">基本信息</ElCol>
          <ElCol :span="24">
            <ElFormItem label="文件名" prop="fileName">
              <span>{{ formData.fileName }}</span>
            </ElFormItem>
          </ElCol>
          <ElCol :span="24">
            <ElFormItem label="资源名称" prop="name">
              <el-input v-model="formData.name" placeholder="请输入资源名称" />
            </ElFormItem>
          </ElCol>
          <ElCol :span="24">
            <ElFormItem label="缩略图" prop="pictureUrl">
              <ImageUpload v-model="formData.pictureUrl" />
            </ElFormItem>
          </ElCol>
          <ElCol :span="24">
            <ElFormItem label="数据类型" prop="fileTypeId">
              <el-input readonly v-model="formData.fileTypeName" placeholder="请输入数据类型">
              </el-input>
            </ElFormItem>
          </ElCol>
          <template v-if="formData.srid">
            <ElCol :span="24">
              <ElFormItem label="坐标系" prop="srid">
                <el-input readonly v-model="formData.srid" placeholder="请输入坐标系"> </el-input>
              </ElFormItem>
            </ElCol>
            <ElCol :span="24">
              <ElFormItem label="数据范围" prop="maxX">
                <el-input
                  class="dataItem"
                  readonly
                  v-model="formData.maxX"
                  placeholder="请输入最大经度"
                >
                  <template #append>
                    <span>最大经度</span>
                  </template>
                </el-input>
              </ElFormItem>
            </ElCol>
            <ElCol :span="24">
              <ElFormItem prop="maxY">
                <el-input
                  class="dataItem"
                  readonly
                  v-model="formData.maxY"
                  placeholder="请输入最大纬度"
                >
                  <template #append>
                    <span>最大纬度</span>
                  </template>
                </el-input>
              </ElFormItem>
            </ElCol>
            <ElCol :span="24">
              <ElFormItem prop="minX">
                <el-input
                  class="dataItem"
                  readonly
                  v-model="formData.minX"
                  placeholder="请输入最小经度"
                >
                  <template #append>
                    <span>最小经度</span>
                  </template>
                </el-input>
              </ElFormItem>
            </ElCol>
            <ElCol :span="24">
              <ElFormItem prop="minY">
                <el-input
                  class="dataItem"
                  readonly
                  v-model="formData.minY"
                  placeholder="请输入最小纬度"
                >
                  <template #append>
                    <span>最小纬度</span>
                  </template>
                </el-input>
              </ElFormItem>
            </ElCol>
          </template>
          <ElCol v-if="['22'].includes(formData.fileTypeId)" :span="24">
            <ElFormItem label="矢量分类" prop="source">
              <el-radio-group v-model="formData.source">
                <el-radio
                  v-for="p in sourceList"
                  :key="p.code"
                  :label="p.value"
                  :value="p.code"
                ></el-radio>
              </el-radio-group>
            </ElFormItem>
          </ElCol>
          <ElCol :span="24">
            <ElFormItem label="数据分类" prop="productTypeId">
              <el-tree-select
                v-model="formData.productTypeId"
                :data="productTypeData"
                :render-after-expand="false"
                style="width: 100%"
                :props="treeProps"
                value-key="id"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="24">
            <ElFormItem label="行政区划" prop="admincode">
              <el-tree-select
                v-model="formData.admincode"
                check-strictly
                :data="asdData"
                style="width: 100%"
                :props="treeAsdProps"
                value-key="value"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="24">
            <ElFormItem label="数据标签" prop="productLabelIdList">
              <el-select
                v-model="formData.productLabelIdList"
                multiple
                collapse-tags
                collapse-tags-tooltip
                placeholder="请选择数据标签"
              >
                <el-option
                  v-for="item in productLabelData"
                  :key="item.id"
                  :value="item.id"
                  :label="item.name"
                />
              </el-select>
            </ElFormItem>
          </ElCol>
          <ElCol :span="24">
            <ElFormItem label="描述信息" prop="remark">
              <el-input
                v-model="formData.remark"
                :rows="3"
                type="textarea"
                placeholder="请填写描述信息"
              />
            </ElFormItem>
          </ElCol>
          <ElCol class="title" :span="24">
            发布信息
            <span>发布信息将会在时空数据门户资源详情中显示，您可以自行选择填写。</span>
          </ElCol>
          <ElCol :span="24">
            <ElFormItem label="发布人" prop="pubUserName">
              <el-input v-model="formData.pubUserName" placeholder="请输入发布人" />
            </ElFormItem>
          </ElCol>
          <ElCol :span="24">
            <ElFormItem label="发布单位" prop="pubUnitName">
              <el-input v-model="formData.pubUnitName" placeholder="请输入发布单位" />
            </ElFormItem>
          </ElCol>
          <ElCol :span="24">
            <ElFormItem label="联系电话" prop="pubUserPhone">
              <el-input v-model="formData.pubUserPhone" placeholder="请输入联系电话" />
            </ElFormItem>
          </ElCol>
          <ElCol :span="24">
            <ElFormItem label="公开范围" prop="sharedScope">
              <div style="display: flex; align-items: center; width: 100%">
                <el-radio-group v-model="formData.sharedScope">
                  <el-radio-button
                    v-for="p in sharedScopeOptions"
                    :key="p.code"
                    :value="p.code"
                    :label="p.value"
                  >
                  </el-radio-button>
                </el-radio-group>
              </div>
            </ElFormItem>
          </ElCol>
        </ElRow>
      </ElForm>
    </ElScrollbar>
    <template #footer>
      <el-text v-if="isRegister" type="danger">该数据已注册！</el-text>
      <ElButton @click="setDialogFormVisible(false)">取消</ElButton>
      <ElButton v-if="!isRegister" :loading="loading" type="primary" @click="handleSubmit"
        >确定</ElButton
      >
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
import { ElForm, ElFormItem, ElDialog, ElScrollbar, ElRow, ElCol, ElMessage } from 'element-plus'
import ImageUpload from '@/components/ImageUpload/index.vue'
import useDialogForm from '@/hooks/useDialogForm'
import { ref } from 'vue'
import {
  getExcellentDetailApi,
  getProductDetailByUserFileIdApi,
  productRegisterApi,
  productTypeListApi,
} from '@/api/data_catalog'
import useDictType from '@/hooks/useDictType'

defineProps<{
  treeAsdProps: any
  productLabelData: any[]
  sharedScopeOptions: any[]
  asdData: any[]
}>()

const sourceList = useDictType('vector_data_type')

// 数据分类
const treeProps = {
  children: 'childList',
  value: 'id',
  label: 'name',
}
const productTypeData = ref<any>([])
const getProductTypeData = async () => {
  try {
    const { data, message, status } = await productTypeListApi({ productType: 1 })
    if ([200].includes(status)) {
      productTypeData.value = data
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}
getProductTypeData()

const { dialogFormVisible, handleDialogClose, setDialogFormVisible, formRef, validateForm } =
  useDialogForm()

const formData = ref<any>({})
const formRule = ref<any>({
  name: [{ required: true, message: '应用数据名称', trigger: 'blur' }],
  userFileId: [{ required: true, message: '请选择文件', trigger: 'change' }],
  fileTypeId: [{ required: true, message: '请选择文件数据类型', trigger: 'change' }],
  pictureUrl: [{ required: true, message: '请上传图片', trigger: 'blur' }],
  productTypeId: [{ required: true, message: '请选择数据分类', trigger: 'blur' }],
  pubUserName: [{ required: true, message: '请输入发布人', trigger: 'blur' }],
  pubUnitName: [{ required: true, message: '请输入发布单位', trigger: 'blur' }],
  pubUserPhone: [{ required: true, message: '请输入联系电话', trigger: 'blur' }],
  sharedScope: [{ required: true, message: '请选择公开范围', trigger: 'blur' }],
})
const isRegister = ref<boolean>(false)

const handleOpen = async (row: any) => {
  try {
    const { data, message, status } = await getProductDetailByUserFileIdApi({ userFileId: row.tid })
    if ([200].includes(status)) {
      const productData = data.find((item: any) => item.productType === 1)
      if (productData) {
        formData.value = productData
        isRegister.value = true
      } else {
        await getFileData(row.tid)
        isRegister.value = false
      }
      setDialogFormVisible(true)
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}

const getFileData = async (userFileId: string) => {
  try {
    const { data, message, status } = await getExcellentDetailApi({ userFileId })
    if ([200].includes(status)) {
      const {
        fileTypeId,
        fileTypeName,
        userFileId,
        pictureUrl,
        fileName,
        suffix,
        srid,
        maxX,
        maxY,
        minX,
        minY,
        fileSize,
      } = data
      formData.value.fileTypeId = fileTypeId
      formData.value.fileTypeName = fileTypeName
      formData.value.userFileId = userFileId
      formData.value.pictureUrl = pictureUrl
      formData.value.fileName = `${fileName}.${suffix}`
      formData.value.fileSize = fileSize
      formData.value.srid = srid
      formData.value.maxX = maxX
      formData.value.maxY = maxY
      formData.value.minX = minX
      formData.value.minY = minY
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}

const loading = ref<boolean>(false)
const handleSubmit = async () => {
  try {
    loading.value = true
    await validateForm()
    const { status, message } = await productRegisterApi({ ...formData.value, productType: 1 })
    if ([200].includes(status)) {
      ElMessage.success('注册成功')
      setDialogFormVisible(false)
    } else {
      ElMessage.error(message)
    }
    loading.value = false
  } catch (error) {
    loading.value = false
  }
}

defineExpose({ handleOpen })
</script>

<style scoped lang="less">
.row {
  width: 100%;
  box-sizing: border-box;
  .title {
    font-weight: bold;
    font-size: 14px;
    color: #333333;
    margin-bottom: 20px;
    > span {
      font-size: 12px;
      color: #999999;
      margin-left: 15px;
      font-weight: 400;
    }
  }
}
</style>
