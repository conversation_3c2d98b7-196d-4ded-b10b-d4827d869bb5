import { configQueryListApi, configRemoveByIdApi } from '@/api/catalog_config'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ref } from 'vue'

const usePageData = (dataType: number) => {
  const treeProps = { children: 'childVOList' }
  const tableData = ref<any[]>([])
  const loading = ref<boolean>(false)
  const getTableData = async () => {
    try {
      const { data, status, message } = await configQueryListApi({ dataType })
      if (status === 200) {
        tableData.value = data
      } else {
        ElMessage.error(message)
      }
    } catch (error) {}
  }
  getTableData()

  const handleDel = (tid: string) => {
    ElMessageBox.confirm('若存在子目录,子目录将会被同步删除，是否确定删除？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }).then(async () => {
      try {
        const { status, message } = await configRemoveByIdApi(tid)
        if (status === 200) {
          ElMessage.success(message)
          getTableData()
        } else {
          ElMessage.error(message)
        }
      } catch (error) {
        console.error(error)
      }
    })
  }

  return { treeProps, tableData, loading, getTableData, handleDel }
}

export default usePageData
