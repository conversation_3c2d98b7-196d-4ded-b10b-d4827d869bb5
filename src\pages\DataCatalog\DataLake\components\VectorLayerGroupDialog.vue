<template>
  <ElDialog
    v-model="dialogFormVisible"
    title="服务发布"
    :close-on-click-modal="false"
    @close="handleDialogClose"
    width="800px"
  >
    <ElCheckbox v-model="layerData.isNoSlice" label="是否切片" />
    <ElTable
      header-cell-class-name="common-table-header"
      cell-class-name="common-table-cell"
      ref="multipleTableRef"
      :data="layerData.tableList"
      height="300px"
      row-key="tid"
    >
      <ElTableColumn prop="dataType" align="center" width="30">
        <template #default="scope">
          <div
            class="img-icon"
            :class="`${['0'].includes(scope.row.parseState) ? 'type' : 'serviceType'}_${
              scope.row.dataType
            }`"
          ></div>
        </template>
      </ElTableColumn>
      <ElTableColumn prop="layerName" label="图层" align="center">
        <template #default="scope">
          <div class="ellipsis">
            <el-tooltip
              v-if="['0'].includes(scope.row.parseState)"
              effect="dark"
              content="该矢量图层服务发布失败，请重新发布或检查数据文件并重新上传发布"
              placement="top"
            >
              <ElIcon class="el-icon-warning">
                <SvgIcon name="WarningFilled" />
              </ElIcon>
            </el-tooltip>
            <span :title="scope.row.layerName">{{ scope.row.layerName }}</span>
          </div>
        </template>
      </ElTableColumn>
      <ElTableColumn prop="styleName" label="样式" align="center">
        <template #default="scope">
          <el-select
            :disabled="['0'].includes(scope.row.parseState)"
            size="small"
            v-model="scope.row.styleName"
            placeholder="请选择"
          >
            <el-option
              v-for="p in scope.row.styleNames || []"
              :key="p.id"
              :label="p.fileName"
              :value="p.fileName"
            >
            </el-option>
          </el-select>
        </template>
      </ElTableColumn>
      <ElTableColumn prop="type" label="显示级别" align="center">
        <template #default="scope">
          <div class="display-level">
            <el-input
              :disabled="['3', '30', '300'].includes(scope.row.parseState)"
              min="0"
              max="9"
              size="small"
              type="number"
              v-model="scope.row.minLevel"
            ></el-input>
            <span>至</span>
            <el-input
              :disabled="['3', '30', '300'].includes(scope.row.parseState)"
              min="0"
              max="9"
              size="small"
              type="number"
              v-model="scope.row.maxlevel"
            ></el-input>
          </div>
        </template>
      </ElTableColumn>
      <ElTableColumn label="操作" width="120" align="center">
        <template #default="scope">
          <el-button
            :disabled="[0].includes(scope.$index) || ['0'].includes(scope.row.parseState)"
            @click="handleTop(scope.row, scope.$index)"
            class="common-icon-btn"
            type="primary"
            plain
            link
          >
            <template #icon>
              <SvgIcon name="layerTop" />
            </template>
          </el-button>
          <el-button
            :disabled="
              [layerData.tableList.length - 1].includes(scope.$index) ||
              ['0'].includes(scope.row.parseState)
            "
            @click="handleBottom(scope.row, scope.$index)"
            class="common-icon-btn"
            type="primary"
            plain
            link
          >
            <template #icon>
              <SvgIcon name="layerBottom" />
            </template>
          </el-button>
          <el-button
            v-if="['0'].includes(scope.row.parseState)"
            class="common-icon-btn"
            type="primary"
            plain
            link
            title="重新发布"
          >
            <template #icon>
              <SvgIcon name="refresh" />
            </template>
          </el-button>
          <el-button
            @click="handleDel(scope.$index)"
            class="common-icon-btn"
            type="danger"
            plain
            link
          >
            <template #icon>
              <SvgIcon name="delete" />
            </template>
          </el-button>
        </template>
      </ElTableColumn>
    </ElTable>
    <div class="btn">
      <ElButton @click="handlePreview" type="primary" plain>预览</ElButton>
    </div>
    <template #footer>
      <ElButton @click="setDialogFormVisible(false)">取消</ElButton>
      <ElButton @click="handlePublish" type="primary">确定</ElButton>
    </template>
    <GroupPreview ref="groupPreviewRef" />
  </ElDialog>
</template>

<script setup lang="ts">
import GroupPreview from './GroupPreview.vue'
import { getChildServerApi, getEServerUrlPreApi, groupLayerPublishApi } from '@/api/data_catalog'
import useDialogForm from '@/hooks/useDialogForm'
import {
  ElCheckbox,
  ElDialog,
  ElIcon,
  ElTable,
  ElTableColumn,
  ElButton,
  ElMessage,
} from 'element-plus'
import { reactive, ref } from 'vue'

const { dialogFormVisible, handleDialogClose, setDialogFormVisible } = useDialogForm()

const layerData = reactive<any>({
  isNoSlice: false,
  tableList: [],
  row: {},
})

const groupPreviewRef = ref<any>()
const handlePreview = async () => {
  try {
    const { status, message, data } = await getEServerUrlPreApi(getParams())
    if (status === 200) {
      console.log(data)
      const { bbox, srid } = layerData.row?.metadataVO
      groupPreviewRef.value?.handleOpen({
        SERVICE: 'WMS',
        REQUEST: 'GetMap',
        FORMAT: 'image/png',
        TRANSPARENT: true,
        LAYERS: `${data}-group`,
        style: '',
        SRS: `EPSG:3857`,
        STYLES: '',
        VERSION: '1.1.1',
        srid,
        bbox: JSON.parse(`[${bbox}]`),
      })
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}

const getParams = () => {
  const { fileId, tid } = layerData.row
  return {
    openTiled: Number(layerData.isNoSlice),
    layerGroups: layerData.tableList.map((item: any) => {
      return item
    }),
    userFileId: tid,
    fileId,
  }
}

const handlePublish = async () => {
  try {
    const { status, message } = await groupLayerPublishApi(getParams())
    if (status === 200) {
      ElMessage.success(message)
      emit('handleRefresh')
      setDialogFormVisible(false)
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}

const handleOpen = async (row: any) => {
  try {
    const { data, status, message } = await getChildServerApi({ userFileId: row.tid })
    if ([200].includes(status)) {
      layerData.tableList = data
      layerData.row = row
      setDialogFormVisible(true)
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}

const handleTop = (row: any, index: number) => {
  layerData.tableList.splice(index, 1)
  layerData.tableList.splice(index - 1, 0, row)
}

const handleBottom = (row: any, index: number) => {
  layerData.tableList.splice(index, 1)
  layerData.tableList.splice(index + 1, 0, row)
}

const handleDel = (index: number) => {
  layerData.tableList.splice(index, 1)
}

const emit = defineEmits<{
  (e: 'handleRefresh'): void
}>()
defineExpose({ handleOpen })
</script>

<style lang="less" scoped>
.ellipsis {
  .ellipseLine();
}
.img-icon {
  width: 12px;
  height: 12px;
  // margin-right: 5px;
}
.serviceType_2,
.serviceType_5,
.serviceType_1,
.serviceType_0 {
  background: url('../../../../assets/commonImg/serviceType1.png') no-repeat center;
}
.serviceType_3 {
  background: url('../../../../assets/commonImg/serviceType2.png') no-repeat center;
}
.serviceType_4 {
  background: url('../../../../assets/commonImg/serviceType3.png') no-repeat center;
}
.type_2,
.type_5,
.type_1,
.type_0 {
  background: url('../../../../assets/commonImg/type1.png') no-repeat center;
}
.type_3 {
  background: url('../../../../assets/commonImg/type2.png') no-repeat center;
}
.type_4 {
  background: url('../../../../assets/commonImg/type3.png') no-repeat center;
}
.el-icon-warning {
  font-size: 16px;
  color: '#E6A23C';
}
.display-level {
  display: flex;
  align-items: center;
  :deep(.el-input__inner) {
    padding-right: 0px;
  }
  span {
    padding: 0px 5px;
  }
}
.btn {
  text-align: right;
  margin-top: 20px;
}
</style>
