<template>
  <div class="database">
    <div class="searchTool">
      <el-input
        v-model="formData.tableName"
        placeholder="请输入查询内容"
        style="width: 300px"
        @keydown.enter="pageData.handleSearch(formData, 1)"
      >
        <template #suffix>
          <SvgIcon
            @click="pageData.handleSearch(formData, 1)"
            style="cursor: pointer"
            name="search"
          />
        </template>
      </el-input>
    </div>
    <div class="mainContent">
      <el-table
        v-loading="pageData.loading"
        header-cell-class-name="common-table-header"
        cell-class-name="common-table-cell"
        :data="tableList"
        height="100%"
        style="width: 100%"
        row-key="tid"
      >
        <el-table-column property="tableName" label="表名" show-overflow-tooltip>
          <template #default="scope">
            <ElText @click="handleClick(scope.row)" type="primary" style="cursor: pointer">{{
              scope.row.tableName
            }}</ElText>
          </template>
        </el-table-column>
        <el-table-column label="空间数据" show-overflow-tooltip>
          <template #default="scope">
            <span>{{ scope.row.metadataTableVO.isSpace ? '是' : '否' }}</span>
          </template>
        </el-table-column>
        <el-table-column property="serverStatusName" label="发布状态" show-overflow-tooltip />
        <el-table-column property="scoreLevel" label="健康度" show-overflow-tooltip>
          <template #default="scope">
            <span
              @click="handleReport(scope.row)"
              :style="{ color: healthColor(scope.row.scoreLevel), cursor: 'pointer' }"
              >{{ scope.row.scoreLevel || '--' }}</span
            >
          </template>
        </el-table-column>
        <el-table-column property="qaStatusName" label="质检状态" show-overflow-tooltip />
        <el-table-column property="tableName" label="数据量(条)" show-overflow-tooltip>
          <template #default="scope">
            <span>{{ scope.row.metadataTableVO.dataVolume }}</span>
          </template>
        </el-table-column>
        <el-table-column property="createTime" label="创建时间" show-overflow-tooltip />
        <el-table-column
          v-if="
            permission.hasButton([
              'dataHouse_administer',
              'dataHouse_publish',
              'dataHouse_address',
              'dataHouse_check',
              'dataHouse_details',
              'dataHouse_del',
            ])
          "
          label="操作"
          width="200"
          fixed="right"
        >
          <template #default="scope">
            <el-button
              v-if="permission.hasButton('dataHouse_administer')"
              @click="handleAdminister"
              title="治理"
              class="common-icon-btn"
              type="primary"
              text
              link
            >
              <template #icon>
                <SvgIcon name="AdministerIcon" />
              </template>
            </el-button>
            <el-button
              v-if="
                ![1, 2].includes(scope.row.serverStatus) &&
                permission.hasButton('dataHouse_publish')
              "
              class="common-icon-btn"
              type="primary"
              plain
              link
              title="发布服务"
              @click="handlePublish(scope.row.tid)"
            >
              <template #icon>
                <SvgIcon name="position" />
              </template>
            </el-button>
            <el-button
              v-if="
                [1].includes(scope.row.serverStatus) && permission.hasButton('dataHouse_address')
              "
              class="common-icon-btn"
              type="primary"
              plain
              link
              title="服务地址"
              @click="ServerAddressRef.handleOpen(scope.row)"
            >
              <template #icon>
                <SvgIcon name="address" />
              </template>
            </el-button>
            <el-button
              v-if="permission.hasButton('dataHouse_check')"
              @click="QualityRuleTableRef.handleCatalogOpen(scope.row)"
              title="质检"
              class="common-icon-btn"
              type="primary"
              text
              link
            >
              <template #icon>
                <SvgIcon name="CheckIcon" />
              </template>
            </el-button>
            <el-button
              v-if="
                [1].includes(scope.row.serverStatus) &&
                ['优秀'].includes(scope.row.scoreLevel) &&
                permission.hasButton('dataHouse_registerServe')
              "
              @click="RegisterDataDialogRef.handleOpen(scope.row)"
              title="注册服务"
              class="common-icon-btn"
              type="primary"
              text
              link
            >
              <template #icon>
                <SvgIcon name="registerServe" />
              </template>
            </el-button>
            <el-button
              v-if="permission.hasButton('dataHouse_details')"
              @click="DetailsRef.handleOpen(scope.row)"
              title="详情"
              class="common-icon-btn"
              type="primary"
              text
              link
            >
              <template #icon>
                <SvgIcon name="details" />
              </template>
            </el-button>
            <el-button
              v-if="permission.hasButton('dataHouse_del')"
              @click="handleDel(scope.row.tid)"
              title="删除"
              class="common-icon-btn"
              type="danger"
              plain
              link
            >
              <template #icon>
                <SvgIcon name="delete" />
              </template>
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="mainFooter">
      <el-pagination
        v-model:currentPage="pageData.page"
        v-model:page-size="pageData.limit"
        :page-sizes="pageData.pageSizes"
        background
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageData.total"
        @size-change="pageData.handleSizeChange"
        @current-change="pageData.handleCurrentChange"
      />
    </div>
    <CheckReport ref="CheckReportRef" :dataType="1" />
    <Details ref="DetailsRef" />
    <QualityRuleTable
      ref="QualityRuleTableRef"
      @handle-refresh="pageData.handleSearch(null, 1)"
      :open-type="2"
    />
    <ServerAddress ref="ServerAddressRef" />
    <RegisterServeDialog
      ref="RegisterDataDialogRef"
      :treeAsdProps="treeAsdProps"
      :productLabelData="productLabelData"
      :sharedScopeOptions="sharedScopeOptions"
      :asdData="asdData"
    />
  </div>
</template>
<script setup lang="ts">
import usePageData from '@/hooks/usePageData'
import { ElInput, ElMessage, ElMessageBox, ElText } from 'element-plus'
import { computed, onMounted, onUnmounted, ref, watch } from 'vue'
import CheckReport from '@/components/CheckReport/index.vue'
import { userTableParamApi, userTablePublishApi, userTableRemoveByIdApi } from '@/api/data_house'
import { useRoute, useRouter } from 'vue-router'
import { healthColor } from '@/utils'
import Details from './Details.vue'
import QualityRuleTable from '@/pages/DataIntegration/DataGather/AddGather/QualityRuleTable.vue'
import useUserInfo from '@/store/useUserInfo'
import useSocket from '@/store/useSocket'
import ServerAddress from '@/components/ServerAddress/index.vue'
import RegisterServeDialog from './RegisterServeDialog.vue'
import useRegisterData from '../../DataLake/hooks/useRegisterData'

const permission = useUserInfo()

const RegisterDataDialogRef = ref<any>()
const { treeAsdProps, productLabelData, sharedScopeOptions, asdData } = useRegisterData()

const route = useRoute()
const router = useRouter()
const { tableData, pageData } = usePageData(userTableParamApi, false)

const dataCatalogId = computed(() => route.query.dataCatalogId as string)
watch(dataCatalogId, (val: string) => {
  if (val) {
    pageData.handleSearch({ dataCatalogId: val }, 1)
  }
})

const formData = ref<any>({ dataCatalogId: dataCatalogId.value })
pageData.handleSearch(formData.value, 1)

// socket时时获取图片地址服务状态
const socket = useSocket()
// 列表数据
const tableList = computed<any[]>(() => {
  if (socket.socketData && Object.keys(socket.socketData).length) {
    return tableData.value.map((item: any) => {
      if (socket.socketData.userTableId === item.tid) {
        if (socket.socketData?.serverStatus || [0].includes(socket.socketData?.serverStatus)) {
          item.serverStatus = Number(socket.socketData?.serverStatus)
        }
        if (socket.socketData?.serverStatusName) {
          item.serverStatusName = socket.socketData?.serverStatusName
        }
      }
      return item
    })
  }
  return tableData.value
})

onMounted(() => {
  socket.handleCreateSocket()
})

onUnmounted(() => {
  socket.handleCloseSocket()
})

const handleClick = (row: any) => {
  router.push({
    query: { dataCatalogId: route.query.dataCatalogId, tableName: row.tableName },
  })
}

const handleAdminister = () => {
  emit('handeTo', 'Sql')
}

const ServerAddressRef = ref<any>()

const handlePublish = async (tid: string) => {
  try {
    const { message, status } = await userTablePublishApi(tid)
    if ([200].includes(status)) {
      ElMessage.success(message)
      pageData.handleSearch(null, 1)
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}

const QualityRuleTableRef = ref<any>()

const CheckReportRef = ref<any>()
const handleReport = (row: any) => {
  if (!row.scoreLevel) return
  CheckReportRef.value?.handleOpen(row)
}

const DetailsRef = ref<any>()

const handleDel = (tid: string) => {
  ElMessageBox.confirm('当前数据表已有采集数据，将会同步删除，确定删除此数据表吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    try {
      const { status, message } = await userTableRemoveByIdApi(tid)
      if ([200].includes(status)) {
        ElMessage.success(message)
        pageData.handleSearch(null, 1)
      } else {
        ElMessage.error(message)
      }
    } catch (error) {
      console.error(error)
    }
  })
}

const emit = defineEmits<{
  handeTo: [path: string]
}>()
</script>
<style lang="less" scoped>
.database {
  height: 100%;
  box-sizing: border-box;
  box-shadow: 0px 0px 7px 1px rgba(0, 0, 0, 0.05);
  background-color: @withe;
  padding: 1.0417vw;
  display: flex;
  flex-direction: column;
  .searchTool {
    margin-bottom: 0.7813vw;
  }
  .mainContent {
    flex: 1;
    overflow: hidden;
    box-sizing: border-box;
  }
  .mainFooter {
    box-sizing: border-box;
    margin-top: 0.7813vw;
    display: flex;
    justify-content: flex-end;
  }
}
</style>
