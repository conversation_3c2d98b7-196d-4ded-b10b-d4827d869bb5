import QualityReportDialog from '@/pages/OnlineModel/components/QualityReportDialog.vue'
import ServerAddress from '@/components/ServerAddress/index.vue'
import { ref } from 'vue'
import { useMapboxPreview, useCesiumPreview } from '@/hooks/useFilePreview'
import { nodeServerListApi } from '@/api/online_model'
import { ElMessage } from 'element-plus'
import { gisPreview, cesiumPreview } from '@/utils/fileMap'
export default function useOutputData() {
  // 质检报告
  const QualityReportDialogRef = ref<any>()

  // 二维地图渲染
  const { MapBoxPreviewRef, MapBoxPreview, handleMapboxPreview } = useMapboxPreview()
  const { CesiumPreviewRef, CesiumPreview, handleCesiumPreview } = useCesiumPreview()

  // 服务地址展示
  const ServerAddressRef = ref<any>()

  // 输出算子逻辑处理
  const handleOutputData = async (node: any, tid: string): Promise<boolean> => {
    // 质检报告
    if ([1].includes(node.data.nodeStatus) && ['QA_REPORT'].includes(node.data.nodeCode)) {
      QualityReportDialogRef.value?.handleOpen({ taskModelId: tid, qaNodePkId: node.id })
      return true
    }
    // 二维地图渲染OUT_VIEW_2D&服务地址展示OUT_PUBLISH
    if (
      [1].includes(node.data.nodeStatus) &&
      ['OUT_VIEW_2D', 'OUT_PUBLISH'].includes(node.data.nodeCode)
    ) {
      try {
        const { data, status, message } = await nodeServerListApi({
          nodeCode: node.data.nodeCode,
          pkId: node.id,
          taskModelId: tid,
        })
        if ([200].includes(status)) {
          if (['OUT_VIEW_2D'].includes(node.data.nodeCode)) {
            if (gisPreview.includes(data.fileTypeId)) {
              handleMapboxPreview(data, true)
            } else if (cesiumPreview.includes(data.fileTypeId)) {
              handleCesiumPreview(data)
            }
          } else {
            ServerAddressRef.value?.handleOpen(data)
          }
        } else {
          ElMessage.error(message)
        }
        return true
      } catch (error) {
        return false
      }
    }
    return false
  }

  // 判断是否是输出算子
  const isOutputNode = (node: any) => {
    if (
      [1].includes(node.data.nodeStatus) &&
      ['QA_REPORT', 'OUT_VIEW_2D', 'OUT_PUBLISH'].includes(node.data.nodeCode)
    ) {
      return true
    }
    return false
  }

  // 输出算子的样式
  const getNodeStyle = (node: any) => {
    if (isOutputNode(node)) {
      return ['nodeText', 'nodeTextActive']
    }
    return ['nodeText']
  }
  return {
    ServerAddress,
    ServerAddressRef,
    QualityReportDialog,
    QualityReportDialogRef,
    MapBoxPreviewRef,
    MapBoxPreview,
    CesiumPreviewRef,
    CesiumPreview,
    handleOutputData,
    getNodeStyle,
    isOutputNode,
  }
}
