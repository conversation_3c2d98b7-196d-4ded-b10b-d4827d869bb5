<template>
  <ElDialog
    v-model="dialogFormVisible"
    width="1000px"
    align-center
    :close-on-click-modal="false"
    title="选择文件"
    @close="handleDialogClose"
  >
    <div class="select-file">
      <div class="select-file-content">
        <div class="content">
          <div class="top">选择：</div>
          <div class="main">
            <el-breadcrumb separator="/" class="breadcrumb">
              <el-breadcrumb-item
                v-for="p in breadcrumbList"
                :key="p.id"
                @click="handleBreadcrumbClick(p)"
                ><span :style="{ cursor: p.isClick ? 'pointer' : '' }">{{
                  p.text
                }}</span></el-breadcrumb-item
              >
            </el-breadcrumb>
            <ElScrollbar class="list" v-loading="page.loading">
              <ul
                class="list-main"
                v-infinite-scroll="handleScroll"
                :infinite-scroll-disabled="page.end"
                :infinite-scroll-immediate="false"
              >
                <li @click="handleSelectFile(p)" class="item" v-for="p in tableData" :key="p">
                  <SvgIcon class="icon" :name="p.ptid ? 'fileDir' : 'details'" />
                  <span>{{ p.ptid ? p.fileTypeName : p.fileName }}</span>
                </li>
              </ul>
            </ElScrollbar>
          </div>
        </div>
        <div class="content">
          <div class="top">已选：</div>
          <div class="main">
            <div class="right-tip">
              <div>
                <span>已选数据</span>
                <span>{{ selectFileList.length }}</span>
              </div>
              <p>最多可选30个文件</p>
            </div>
            <ElScrollbar class="list">
              <ul class="list-right">
                <li class="item" v-for="p in selectFileList" :key="p.tid">
                  <span>{{ p.fileName }}</span>
                  <SvgIcon @click="handleRemove(p)" class="icon" name="closeBold" />
                </li>
              </ul>
            </ElScrollbar>
          </div>
        </div>
      </div>
      <div class="select-file-footer">注意：需要数据已发布服务，才可作为时序场景素材数据</div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="setDialogFormVisible(false)">取消</el-button>
        <el-button type="primary" @click="handleSave">确定</el-button>
      </span>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
import { ElDialog, ElMessage, ElScrollbar } from 'element-plus'
import useDialogForm from '@/hooks/useDialogForm'
import { computed, reactive, ref } from 'vue'
import { userFileListApi } from '@/api/data_catalog'

const { dialogFormVisible, setDialogFormVisible, handleDialogClose } = useDialogForm()

const handleSave = () => {
  if (!selectFileList.value.length) {
    ElMessage.warning('请选择数据')
    return
  }
  emit('getSelectData', selectFileList.value)
  setDialogFormVisible(false)
}

const fileTypeId = ref<string>('')
const fileList = ref<any[]>([])
const dirList = ref<any[]>([])
const tableData = computed(() => (fileTypeId.value ? fileList.value : dirList.value))

const page = reactive<any>({
  limit: 25,
  page: 1,
  loading: false,
  end: false,
})

const getFileList = async () => {
  try {
    page.loading = true
    const params = {
      page: page.page,
      limit: page.limit,
      fileTypeId: fileTypeId.value,
    }
    const { data, status } = await userFileListApi(params)
    if ([200].includes(status)) {
      if (data.length) {
        fileList.value = fileList.value.concat(data)
        page.page++
        if (data.length < page.limit) {
          page.end = true
        }
      } else {
        page.end = true
      }
    }
    page.loading = false
  } catch (error) {
    page.loading = false
  }
}

// 下来刷新
const handleScroll = () => {
  if (page.end) {
    return
  }
  getFileList()
}

// 选择数据
const selectFileList = ref<any[]>([])
const handleSelectFile = (p: any) => {
  if (p.ptid) {
    breadcrumbList.value.push({ text: p.fileTypeName, id: p.tid })
    fileTypeId.value = p.tid
    fileList.value = []
    page.page = 1
    page.end = false
    selectFileList.value = []
    getFileList()
    return
  }
  if (![1].includes(p.serverStatus)) {
    ElMessage.warning('该数据未发布服务!')
    return
  }
  if (selectFileList.value.some((item: any) => item.tid === p.tid)) {
    ElMessage.warning('已选择该数据!')
    return
  }
  if (selectFileList.value.length > 30) {
    ElMessage.warning('最多可选30个文件!')
    return
  }
  selectFileList.value.push(p)
}

// 移除数据
const handleRemove = (p: any) => {
  selectFileList.value = selectFileList.value.filter((item: any) => item.tid !== p.tid)
}

// 面包屑
const breadcrumbList = ref<any[]>([])
const handleBreadcrumbClick = (p: any) => {
  if (p.isClick) {
    breadcrumbList.value = [{ text: '全部', id: '0' }, p]
    fileTypeId.value = ''
    fileList.value = []
  }
}

const handleOpen = (menu: any) => {
  dirList.value = menu?.childVOList || []
  breadcrumbList.value = [
    {
      text: '全部',
      id: '0',
    },
    {
      text: menu?.fileTypeName,
      menuList: menu?.childVOList || [],
      id: menu?.ptid,
      isClick: true,
    },
  ]
  fileTypeId.value = ''
  fileList.value = []
  selectFileList.value = []
  setDialogFormVisible(true)
}

const emit = defineEmits<{
  (e: 'getSelectData', list: any[]): void
}>()
defineExpose({ handleOpen })
</script>

<style lang="less" scoped>
.select-file {
  height: 65vh;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  .select-file-content {
    flex: 1;
    overflow: auto;
    box-sizing: border-box;
    display: flex;
    .content {
      flex: 1;
      overflow: auto;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      .top {
        font-size: 14px;
        font-weight: 400;
        color: #333333;
        margin-bottom: 10px;
      }
      .main {
        flex: 1;
        overflow: hidden;
        background: #f3f6f8;
        border-radius: 4px 4px 4px 4px;
        border: 1px solid #ebebec;
        display: flex;
        flex-direction: column;
        .breadcrumb {
          margin: 20px;
        }
        .list {
          flex: 1;
          box-sizing: border-box;
          padding: 0 20px;
          .list-main {
            padding-bottom: 20px;
            .item {
              display: flex;
              align-items: center;
              cursor: pointer;
              .icon {
                font-size: 36px;
              }
              span {
                flex: 1;
                font-size: 14px;
                font-weight: 400;
                color: #333333;
                margin-left: 10px;
                .ellipseLine();
              }
            }
            .item + .item {
              margin-top: 10px;
            }
          }
          .list-right {
            padding-bottom: 20px;
            .item {
              display: flex;
              align-items: center;
              font-size: 14px;
              font-weight: 400;
              color: #333333;
              span {
                flex: 1;
                margin-right: 10px;
                .ellipseLine();
              }
            }
            .item + .item {
              margin-top: 20px;
            }
          }
        }
        .right-tip {
          display: flex;
          justify-content: space-between;
          margin: 20px;
          font-size: 14px;
          font-weight: 400;
          color: #333333;
          > div {
            :nth-child(2) {
              color: var(--el-color-primary);
            }
            span + span {
              margin-left: 20px;
            }
          }
        }
      }
    }
    .content + .content {
      margin-left: 20px;
    }
  }
  .select-file-footer {
    font-size: 12px;
    font-weight: 400;
    color: #999999;
    margin-top: 20px;
  }
}
</style>
