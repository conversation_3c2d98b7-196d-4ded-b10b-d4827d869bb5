import { center, getBaseMap, getTransformExtentData, mapboxToken, markMap } from '@/utils/mapConfig'
import mapboxgl, { Map, Layer, Sources } from 'mapbox-gl'
import MapboxDraw from '@mapbox/mapbox-gl-draw'
import SimpleSelectMode from '@/pages/DataSearch/utils/simpleSelectMode'
import DirectMode from '@/pages/DataSearch/utils/directMode'
import DrawRectangle from '@/pages/DataSearch/utils/drawRectangle'
import StaticMode from '@/pages/DataSearch/utils/staticMode'
import CircleMode from '@/pages/DataSearch/utils/circleMode'
import DrawStyle from '@/pages/DataSearch/utils/drawStyle'
import { onUnmounted, reactive, ref, shallowRef } from 'vue'
import useUserInfo from '@/store/useUserInfo'
import { MapTools } from '@/types/mapbox'

export default function useMapbox(showType: any = 'globe') {
  const permssion = useUserInfo()

  mapboxgl.accessToken = mapboxToken

  const mapRef = ref<HTMLDivElement | any>()

  // 地图实例
  const map = shallowRef<Map | any>()

  // 制作绘制点线面实例
  const mapDraw = ref<MapboxDraw>()

  // 切换地图
  const styleBaseMap = (type: string = 'img') => {
    // 创建一个自定义的 TMS 图层源，并设置 bbox
    const sources: Sources['raster'] = {
      type: 'raster',
      tiles: [getBaseMap(type)],
      maxzoom: 18,
      tileSize: 256,
    }
    map.value?.addSource('base-source', sources)
    // 创建一个图层来显示 TMS 图层源
    const layer: Layer | any = {
      id: 'base-layer',
      type: 'raster',
      source: 'base-source',
    }
    map.value?.addLayer(layer, 'cia-layer')
  }
  const handleChangeMap: any = (type: string) => {
    map.value?.removeLayer('base-layer')
    map.value?.removeSource('base-source')
    if (type === 'vec') {
      map.value?.setLayoutProperty('cia-layer', 'visibility', 'none')
    } else {
      map.value?.setLayoutProperty('cia-layer', 'visibility', 'visible')
    }
    styleBaseMap(type)
  }

  // 设置预览图层
  /**
   * 设置预览图层
   * @param row 预览数据对象，包含bbox(边界框)、srid(空间参考)和url(瓦片地址)
   */
  const setPreviewLayers = (row: any) => {
    // 清除已有的预览图层
    clearPreviewLayers()

    // 解构预览数据
    const { bbox, srid, url } = row

    // 添加TMS瓦片数据源
    map.value?.addSource('mapTiff', {
      scheme: 'tms', // TMS瓦片方案
      type: 'raster', // 栅格类型
      tiles: [`${import.meta.env.VITE_BASE_SERVER}/${url}`], // 瓦片URL模板
      tileSize: 256, // 瓦片大小
    })

    // 添加栅格图层
    map.value?.addLayer({
      id: 'mapTiff',
      type: 'raster',
      source: 'mapTiff',
      paint: {
        'raster-opacity': 1, // 设置图层不透明度
      },
    })

    // 将边界框坐标从源SRID转换为EPSG:4326(WGS84)
    const bbox4326: any = getTransformExtentData(`EPSG:${srid}`, bbox, 'EPSG:4326')

    // 调整地图视野范围至边界框
    map.value?.fitBounds(bbox4326, {})
  }

  // 删除预览图层
  const clearPreviewLayers = () => {
    map.value?.getLayer('mapTiff') && map.value?.removeLayer('mapTiff')
    map.value?.getSource('mapTiff') && map.value?.removeSource('mapTiff')
  }

  // 常用操作
  const mapOperate = {
    // 地球初始化复位或是定位
    resetMap: (coordinate: any = center, zoom: number = 3) => {
      map.value?.flyTo({
        center: coordinate,
        zoom: zoom,
        speed: 2,
        curve: 1,
        easing(t: any) {
          return t
        },
      })
    },
    // 放大地图
    zoomInMap: () => {
      map.value?.zoomIn({ duration: 1000 })
    },
    // 缩小地图
    zoomOutMap: () => {
      map.value?.zoomOut({ duration: 1000 })
    },
    // 设置地图模式
    setMapMode: (mode: string) => {
      if (['2D'].includes(mode)) {
        map.value?.setProjection({ name: 'mercator' })
      } else {
        map.value?.setProjection({ name: 'globe' })
      }
    },
  }

  // 初始化地图方法
  const initMap = (callback?: any, option?: any) => {
    let obj = {}
    if (option?.sprite && option?.glyphs) {
      obj = {
        sprite: option?.sprite,
        glyphs: option?.glyphs,
      }
    }
    map.value = new mapboxgl.Map({
      container: mapRef.value,
      style: {
        version: 8,
        sources: {
          'img-cia': {
            type: 'raster',
            tiles: [markMap],
            maxzoom: 18,
            tileSize: 256,
          },
        },
        layers: [
          {
            id: 'cia-layer',
            source: 'img-cia',
            type: 'raster',
          },
        ],
        fog: {
          color: 'rgb(186, 210, 235)',
          'high-color': 'rgb(36, 92, 223)',
          'horizon-blend': 0.02,
        },
        glyphs: 'mapbox://fonts/mapbox/{fontstack}/{range}.pbf',
        ...obj,
      },
      center: [116.39880632, 38.90487972],
      zoom: 4,
      minZoom: 1,
      maxZoom: 18,
      //  mercator 二维地图展示  globe 三维地图展示
      projection: { name: showType },
      preserveDrawingBuffer: option?.preserveDrawingBuffer || false,
      transformRequest: (url: string, resourceType: any): any => {
        if (resourceType === 'Tile' && url.includes('mapbox')) {
          return {
            url,
            headers: { 'X-Mapbox-Perform-Projection': 'false', Token: permssion.token },
          }
        }
        return { url, headers: { Token: permssion.token } }
      },
    })
    map.value.on('load', () => {
      // 设置地图
      styleBaseMap()
      // 渲染地图的回调函数
      callback && callback()
    })
  }

  // 工具栏
  const mapTools = reactive<MapTools>({
    type: '',
    // 绘制工具
    drawTools: {
      drawingMode: 'static',
      // 单个清楚
      singleClear(id) {
        const all = mapDraw.value?.getAll()
        all?.features.forEach((item: any) => {
          if (item.id !== id) {
            mapDraw.value?.delete(item.id)
          }
        })
      },
      // 绘制多边形
      addPolygon() {
        mapTools.drawTools.drawingMode = 'draw_polygon'
        mapDraw.value?.changeMode('draw_polygon')
      },
      // 绘制圆形
      addCircle() {
        mapTools.drawTools.drawingMode = 'draw_circle'
        mapDraw.value?.changeMode('draw_circle')
      },
      // 绘制矩形
      addRectangle() {
        mapTools.drawTools.drawingMode = 'draw_rectangle'
        mapDraw.value?.changeMode('draw_rectangle', { featureId: '', from: [] })
      },
      // 绘制线
      addLine() {
        mapTools.drawTools.drawingMode = 'draw_line_string'
        mapDraw.value?.changeMode('draw_line_string')
      },
      // 绘制点
      addPoint() {
        mapTools.drawTools.drawingMode = 'draw_point'
        mapDraw.value?.changeMode('draw_point')
      },
      // 清楚绘图事件
      clearDraw() {
        mapTools.drawTools.drawingMode = 'static'
        mapDraw.value?.changeMode('static')
        mapDraw.value?.deleteAll()
      },
    },
    // 操作
    handleToolsEvents(type: MapTools['type']) {
      mapTools.type = type
      console.log(type)
      switch (type) {
        case 'mapLocation':
          mapTools.drawTools.addPoint()
          break
        case 'mapLine':
          mapTools.drawTools.addLine()
          break
        case 'mapPolygon':
          mapTools.drawTools.addPolygon()
          break
        case 'mapRectangle':
          mapTools.drawTools.addRectangle()
          break
        case 'mapCircle':
          mapTools.drawTools.addCircle()
          break
        case 'mapRange':
          mapTools.drawTools.addLine()
          break
        case 'mapArea':
          mapTools.drawTools.addRectangle()
          break
        default:
          break
      }
    },
    // 删除
    handleClearTools() {
      mapTools.type = ''
      mapTools.drawTools.clearDraw()
    },
  })

  // 初始化绘制工具
  const initMapDraw = (callback?: any) => {
    mapDraw.value = new MapboxDraw({
      displayControlsDefault: false,
      userProperties: true,
      modes: {
        ...MapboxDraw.modes,
        draw_circle: CircleMode,
        direct_select: DirectMode,
        simple_select: SimpleSelectMode,
        draw_rectangle: DrawRectangle,
        static: StaticMode,
      },
      styles: DrawStyle,
    })
    map.value?.addControl(mapDraw.value, 'top-left')
    map.value?.on('draw.create', (e: any) => {
      // 只保留一个绘图样式
      const all: any[] | any = mapDraw.value?.getAll().features
      const currentId = all[all.length - 1].id
      mapTools.drawTools.singleClear(currentId)
      callback && callback(e)
    })
    map.value?.on('draw.update', (e: any) => {
      console.log('draw.update', e)
    })
    map.value?.on('draw.delete', (e: any) => {
      console.log('draw.delete', e)
    })
    map.value?.on('draw.modechange', (e: any) => {
      if (e.mode == 'simple_select') {
        mapDraw.value?.changeMode(mapTools.drawTools.drawingMode)
      }
    })
  }

  onUnmounted(() => {
    if (map.value) {
      map.value.remove()
    }
  })

  return {
    mapRef,
    map,
    handleChangeMap,
    mapOperate,
    setPreviewLayers,
    initMap,
    mapTools,
    initMapDraw,
    clearPreviewLayers,
  }
}
