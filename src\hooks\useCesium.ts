import { mapOperate, MapTools } from '@/types/CesiumType'
import { center, getBaseMap, getTransformExtentData, markMap } from '@/utils/mapConfig'
import * as Cesium from 'cesium'
import { reactive, shallowRef } from 'vue'

const token: string =
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************.m5wCiSjxEvv9XmHU_biir0hggbsmg1sR5_FWiyM-fZw'

export default function useCesium() {
  const cesiumViewer = shallowRef<Cesium.Viewer>()

  // 地图图层
  const baseImageryProvider = (img: string = 'img') =>
    new Cesium.UrlTemplateImageryProvider({
      url: getBaseMap(img),
      maximumLevel: ['img'].includes(img) ? 18 : 16,
      tileWidth: 256,
      tileHeight: 256,
    })
  const markImageryProvider = new Cesium.UrlTemplateImageryProvider({
    url: markMap,
    maximumLevel: 18,
    tileWidth: 256,
    tileHeight: 256,
  })
  const handleChangeBaseMap = (img?: string) => {
    if (cesiumViewer.value?.imageryLayers.length) {
      const baseLayers = cesiumViewer.value?.imageryLayers.get(0)
      const markLayers = cesiumViewer.value?.imageryLayers.get(1)
      cesiumViewer.value?.imageryLayers.remove(baseLayers)
      cesiumViewer.value?.imageryLayers.remove(markLayers)
    }
    cesiumViewer.value?.imageryLayers.addImageryProvider(baseImageryProvider(img), 0)
    cesiumViewer.value?.imageryLayers.addImageryProvider(markImageryProvider, 1)
  }

  // 初始化地图
  const initCesium = (container: string | HTMLElement, callback?: Function) => {
    Cesium.Ion.defaultAccessToken = token
    cesiumViewer.value = new Cesium.Viewer(container, {
      geocoder: false,
      homeButton: false,
      infoBox: false,
      sceneModePicker: false,
      selectionIndicator: false,
      timeline: false,
      navigationHelpButton: false,
      baseLayerPicker: false,
      animation: false,
      fullscreenButton: false,
      shouldAnimate: false,
      contextOptions: {
        webgl: {
          alpha: true,
          depth: true,
        },
      },
      imageryProviderViewModels: [],
      terrainProvider: new Cesium.EllipsoidTerrainProvider(), // 使用椭球体地形
    })
    cesiumViewer.value.scene.skyBox.show = true //去掉天空盒子
    cesiumViewer.value.scene.backgroundColor = new Cesium.Color(0, 0, 0, 0) //设置场景背景色透明，便于显示自定的背景
    cesiumViewer.value.scene.globe.baseColor = new Cesium.Color(0, 0, 0, 0) //修改地邱球体背景透明
    cesiumViewer.value.scene.imageryLayers.removeAll() //去除其他图层
    callback && callback()
  }

  // 放大缩小恢复中心点
  const mapOperate = reactive<mapOperate>({
    zoomIn: () => {
      const info: any = mapOperate.getCurrentViewInfo()
      mapOperate.zoomToCenter([info.centerLon, info.centerLat], info.height / 1.8)
    },
    zoomOut: () => {
      const info: any = mapOperate.getCurrentViewInfo()
      mapOperate.zoomToCenter([info.centerLon, info.centerLat], info.height * 1.8)
    },
    zoomToCenter: (coordinate = center, height: number = 10000000) => {
      const position = Cesium.Cartesian3.fromDegrees(...coordinate, height)
      cesiumViewer.value?.camera.flyTo({
        destination: position,
        duration: 1.5,
      })
    },
    // 获取当前位置的高度，和中心点坐标
    getCurrentViewInfo: () => {
      const cartographic: any = cesiumViewer.value?.scene.globe.ellipsoid.cartesianToCartographic(
        cesiumViewer.value.camera.position,
      )
      return {
        height: cartographic.height,
        centerLon: parseFloat(Cesium.Math.toDegrees(cartographic.longitude).toFixed(8)),
        centerLat: parseFloat(Cesium.Math.toDegrees(cartographic.latitude).toFixed(8)),
      }
    },
  })

  // 图层预览
  let previewLayers: any = {
    tmsLayer: null,
    tilesLayer: null,
  }
  // 设置3dtiles预览
  const set3DPreviewLayers = async (url: string) => {
    try {
      clearCesiumPreviewLayers()
      const tileset = await Cesium.Cesium3DTileset.fromUrl(
        `${import.meta.env.VITE_BASE_SERVER}/${url}`,
      )
      previewLayers.tilesLayer = cesiumViewer.value?.scene.primitives.add(tileset)
      cesiumViewer.value?.zoomTo(previewLayers.tilesLayer)
    } catch (error) {
      console.error(error)
    }
  }
  // 设置星图服务预览
  const getReverseY = (y: any, level: any, tilingScheme: any) => {
    const yTiles = tilingScheme.getNumberOfYTilesAtLevel(level)
    return yTiles - y - 1
  }
  const setTmsPreviewLayers = async ({ url, bbox, srid }: any) => {
    const newUrl = url.replace('{-y}', '{reverseY}')
    const bbox4326: any = getTransformExtentData(`EPSG:${srid}`, bbox, 'EPSG:4326')
    clearCesiumPreviewLayers()
    const webMercatorTilingScheme = new Cesium.WebMercatorTilingScheme()
    const imageryProvider = new Cesium.UrlTemplateImageryProvider({
      url: `${import.meta.env.VITE_BASE_SERVER}/${newUrl}`,
      tilingScheme: webMercatorTilingScheme,
      tileWidth: 256,
      tileHeight: 256,
      rectangle: Cesium.Rectangle.fromDegrees(...bbox4326),
      customTags: {
        reverseY: function (_imageryProvider: any, _x: any, y: any, level: any) {
          return getReverseY(y, level, webMercatorTilingScheme)
        },
      },
    })
    previewLayers.tmsLayer = cesiumViewer.value?.imageryLayers.addImageryProvider(imageryProvider)
    cesiumViewer.value?.zoomTo(previewLayers.tmsLayer)
  }

  // 清除预览图层
  const clearCesiumPreviewLayers = () => {
    if (previewLayers.tmsLayer) {
      cesiumViewer.value?.imageryLayers.remove(previewLayers.tmsLayer)
      previewLayers.tmsLayer = null
    }
    if (previewLayers.tilesLayer) {
      console.log(previewLayers, 'sssssss')
      cesiumViewer.value?.scene.primitives.remove(previewLayers.tilesLayer)
      previewLayers.tilesLayer = null
    }
  }

  // 绘制多边形矩形圆
  const mapTools = reactive<MapTools | any>({
    // 图形统一颜色
    geoMaterial: Cesium.Color.fromCssColorString('#5994F1').withAlpha(0.3),
    outlineColor: Cesium.Color.fromCssColorString('#5994F1'),
    // 事件对象
    drawHandler: null,
    // 实体对象
    entity: null,
    // 初始化点线面事件
    registerEvents: (type: string) => {
      let shapeEntity: any = null
      let pointEntity: any = null
      let activeShapePoints: any[] = []
      mapTools.clearEntity()
      const viewer = cesiumViewer.value as Cesium.Viewer
      mapTools.drawHandler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas)
      mapTools.drawHandler.setInputAction((event: any) => {
        const earthPosition = cesiumViewer.value?.camera.pickEllipsoid(
          event.position,
          cesiumViewer.value.scene.globe.ellipsoid,
        )
        if (Cesium.defined(earthPosition)) {
          if (!activeShapePoints.length) {
            pointEntity = mapTools.addPoint({ position: earthPosition })
            activeShapePoints.push(earthPosition)
            if (['Polygon'].includes(type)) {
              shapeEntity = mapTools.addPolygon(activeShapePoints)
            } else if (['Rectangle'].includes(type)) {
              shapeEntity = mapTools.addRectangle(activeShapePoints)
            } else {
              shapeEntity = mapTools.addCircle(activeShapePoints)
            }
          }
          activeShapePoints.push(earthPosition)
        }
      }, Cesium.ScreenSpaceEventType.LEFT_CLICK)
      mapTools.drawHandler.setInputAction((event: any) => {
        ;(cesiumViewer.value as any).container.style.cursor = 'crosshair'
        if (Cesium.defined(pointEntity)) {
          const newPosition = cesiumViewer.value?.camera.pickEllipsoid(
            event.endPosition,
            cesiumViewer.value.scene.globe.ellipsoid,
          )
          if (Cesium.defined(newPosition)) {
            pointEntity.position = newPosition
            activeShapePoints.pop()
            activeShapePoints.push(newPosition)
          }
        }
      }, Cesium.ScreenSpaceEventType.MOUSE_MOVE)
      mapTools.drawHandler.setInputAction((_event: any) => {
        if (shapeEntity) {
          cesiumViewer.value?.entities.remove(shapeEntity)
          shapeEntity = null
        }
        if (pointEntity) {
          cesiumViewer.value?.entities.remove(pointEntity)
          pointEntity = null
        }
        activeShapePoints.pop()
        if (activeShapePoints.length) {
          if (['Polygon'].includes(type)) {
            mapTools.entity = mapTools.addPolygon(activeShapePoints)
          } else if (['Rectangle'].includes(type)) {
            mapTools.entity = mapTools.addRectangle(activeShapePoints)
          } else {
            mapTools.entity = mapTools.addCircle(activeShapePoints)
          }
        }
        activeShapePoints = []
        mapTools.removeAllEvent()
      }, Cesium.ScreenSpaceEventType.RIGHT_CLICK)
    },

    // 画点
    addPoint: (pointInfo: any) => {
      return cesiumViewer.value?.entities.add({
        id: `Point-${Date.now()}`,
        name: `Point-${Date.now()}`,
        position: pointInfo.position,
        show: true,
        point: {
          show: true,
          pixelSize: 6,
          heightReference: Cesium.HeightReference.NONE,
          color: mapTools.outlineColor,
          outlineColor: mapTools.outlineColor,
          scaleByDistance: new Cesium.NearFarScalar(0, 1, 5e10, 1),
          translucencyByDistance: new Cesium.NearFarScalar(0, 1, 5e10, 1),
          distanceDisplayCondition: new Cesium.DistanceDisplayCondition(0, 4.8e10),
        },
      })
    },

    // 多边形
    addPolygon: (positionData: any) => {
      return cesiumViewer.value?.entities.add({
        name: `Polygon-${Date.now()}`,
        id: `Polygon-${Date.now()}`,
        polygon: {
          hierarchy: new Cesium.CallbackProperty(() => {
            return new Cesium.PolygonHierarchy(positionData)
          }, false),
          height: 0,
          heightReference: 0,
          material: mapTools.geoMaterial,
          outline: true,
          outlineWidth: 7,
          outlineColor: mapTools.outlineColor,
        },
      })
    },

    // 矩形
    addRectangle: (positionData: any) => {
      return cesiumViewer.value?.entities.add({
        name: `Rectangle-${Date.now()}`,
        id: `Rectangle-${Date.now()}`,
        rectangle: {
          coordinates: new Cesium.CallbackProperty(() => {
            return Cesium.Rectangle.fromCartesianArray(positionData)
          }, false),
          material: mapTools.geoMaterial,
          height: 0,
          outline: true,
          outlineColor: mapTools.outlineColor,
          outlineWidth: 7,
        },
      })
    },

    // 画圆
    addCircle: (positionData: any[]) => {
      return cesiumViewer.value?.entities.add({
        name: `Circle-${Date.now()}`,
        id: `Circle-${Date.now()}`,
        position: positionData[0],
        ellipse: {
          extrudedHeight: 0,
          semiMinorAxis: new Cesium.CallbackProperty(() => {
            const startPoint = mapTools.cartesian3ToCartographic(positionData[0])
            const endPoint = mapTools.cartesian3ToCartographic(
              positionData[positionData.length - 1],
            )
            return mapTools.getDistance(startPoint, endPoint)
          }, false),
          semiMajorAxis: new Cesium.CallbackProperty(() => {
            const startPoint = mapTools.cartesian3ToCartographic(positionData[0])
            const endPoint = mapTools.cartesian3ToCartographic(
              positionData[positionData.length - 1],
            )
            return mapTools.getDistance(startPoint, endPoint)
          }, false),
          height: 0,
          material: mapTools.geoMaterial,
          outline: true,
          outlineColor: mapTools.outlineColor,
          outlineWidth: 7,
        },
      })
    },

    // 移除事件
    removeAllEvent: () => {
      ;(cesiumViewer.value as any).container.style.cursor = 'default'
      if (!mapTools.drawHandler) return
      mapTools.drawHandler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK)
      mapTools.drawHandler.removeInputAction(Cesium.ScreenSpaceEventType.MOUSE_MOVE)
      mapTools.drawHandler.removeInputAction(Cesium.ScreenSpaceEventType.RIGHT_CLICK)
      mapTools.drawHandler.destroy()
      mapTools.drawHandler = null
    },

    // 清除实体
    clearEntity: () => {
      if (mapTools.entity) {
        cesiumViewer.value?.entities.removeAll()
        mapTools.entity = null
      }
    },
    // 笛卡尔坐标系转地理坐标坐标
    cartesian3ToCartographic: (cartesian3: any) => {
      const ellipsoid: any = cesiumViewer.value?.scene.globe.ellipsoid
      // 笛卡尔坐标系转CartoGraphic
      return ellipsoid.cartesianToCartographic(cartesian3) // 笛卡尔坐标系转为地理坐标系
    },
    // 计算距离
    // Cartographic 格式 使用地理坐标系计算距离
    getDistance: (startCartographic: any, endCartographic: any) => {
      const geodesic = new Cesium.EllipsoidGeodesic()
      geodesic.setEndPoints(startCartographic, endCartographic)
      const distance = geodesic.surfaceDistance
      return distance
    },

    // 获取矩形四角坐标点
    getPositionPoints: (entity: any, type: string) => {
      if (!entity) return
      if (['0'].includes(type)) {
        const rectangleGraphics = entity.rectangle
        const rectangleCoordinates = rectangleGraphics.coordinates.getValue(
          cesiumViewer.value?.clock.currentTime,
        )
        return [
          [
            Cesium.Math.toDegrees(rectangleCoordinates.west),
            Cesium.Math.toDegrees(rectangleCoordinates.south),
          ],
          [
            Cesium.Math.toDegrees(rectangleCoordinates.east),
            Cesium.Math.toDegrees(rectangleCoordinates.north),
          ],
          [
            Cesium.Math.toDegrees(rectangleCoordinates.west),
            Cesium.Math.toDegrees(rectangleCoordinates.north),
          ],
          [
            Cesium.Math.toDegrees(rectangleCoordinates.east),
            Cesium.Math.toDegrees(rectangleCoordinates.south),
          ],
          [
            Cesium.Math.toDegrees(rectangleCoordinates.west),
            Cesium.Math.toDegrees(rectangleCoordinates.south),
          ],
        ]
      }
      if (['1'].includes(type)) {
        const ellipseGraphics = entity.ellipse
        const center: any = entity.position.getValue(cesiumViewer.value?.clock.currentTime)
        const cartographic = mapTools.cartesian3ToCartographic(center)
        const semiMajorAxis = ellipseGraphics.semiMajorAxis.getValue(
          cesiumViewer.value?.clock.currentTime,
        )
        return {
          ponit: [
            Cesium.Math.toDegrees(cartographic.longitude),
            Cesium.Math.toDegrees(cartographic.latitude),
          ],
          distance: semiMajorAxis,
        }
      }
      const polygonGraphics = entity.polygon
      const polygonCoordinates = polygonGraphics.hierarchy.getValue(
        cesiumViewer.value?.clock.currentTime,
      )
      const positions: any[] = polygonCoordinates.positions
      positions.push(positions[0])
      return positions.map((position: any) => {
        const cartographic = Cesium.Cartographic.fromCartesian(position)
        return [
          Cesium.Math.toDegrees(cartographic.longitude),
          Cesium.Math.toDegrees(cartographic.latitude),
        ]
      })
    },
  })

  // 销毁地图
  const destroyCesium = () => {
    cesiumViewer.value?.destroy()
    cesiumViewer.value = undefined
  }

  return {
    cesiumViewer,
    initCesium,
    set3DPreviewLayers,
    destroyCesium,
    handleChangeBaseMap,
    mapOperate,
    setTmsPreviewLayers,
    clearCesiumPreviewLayers,
    mapTools,
  }
}
