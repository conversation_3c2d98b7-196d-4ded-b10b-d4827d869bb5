<template>
  <ElScrollbar style="width: 375px; margin-right: 40px">
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="auto">
      <div class="left-edit">
        <div class="header-btn">
          <ElButton @click="handleSave" type="primary"> 保存 </ElButton>
          <ElButton @click="handleResetFields" type="primary" plain> 清空数据 </ElButton>
        </div>
        <div class="formData">
          <el-row :gutter="10">
            <el-col :span="24">
              <el-form-item label="场景名称" prop="name">
                <el-input v-model="formData.name" placeholder="请输入场景名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="时序类型" prop="fileTypeId">
                <el-select v-model="formData.fileTypeId" placeholder="请选择时序类型">
                  <el-option
                    v-for="p in fileTypeOptions"
                    :key="p.tid"
                    :label="p.fileTypeName"
                    :value="p.tid"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="播放时间" prop="interval">
                <div style="display: flex; align-items: center">
                  <el-input v-model="formData.interval" placeholder="请输入" />
                  <span style="margin-left: 5px">秒</span>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="时间分类" prop="dateFormat">
                <el-select
                  v-model="formData.dateFormat"
                  @change="handleFormat"
                  placeholder="请选择时序分类"
                >
                  <el-option v-for="(p, i) in dateFormatOptions" :key="i" :label="p" :value="p" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="描述" prop="notes">
                <el-input v-model="formData.notes" type="textarea" :rows="4" />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="选择文件" prop="fileList">
                <div class="select-view">
                  <ElButton @click="handleSelectFile" type="primary"> 选择 </ElButton>
                  <ElButton @click="handleScenePreview" type="primary" plain> 场景预览 </ElButton>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <div class="table">
          <el-table
            header-cell-class-name="common-table-header"
            cell-class-name="common-table-cell"
            ref="multipleTableRef"
            :data="formData.fileList"
            min-height="100%"
            max-height="300px"
          >
            <el-table-column property="dataSourceName" label="时间">
              <template #default="{ row, $index }">
                <el-form-item
                  class="table-form"
                  :prop="`fileList.${$index}.sceneTimeFormat`"
                  :rules="rules.sceneTimeFormat"
                >
                  <el-date-picker
                    v-model="row.sceneTimeFormat"
                    :type="pickerType"
                    :formot="
                      (formData.dateFormat && formData.dateFormat.toUpperCase()) || 'YYYY-MM-DD HH'
                    "
                    :value-format="
                      (formData.dateFormat && formData.dateFormat.toUpperCase()) || 'YYYY-MM-DD HH'
                    "
                    placeholder="请选择时间"
                  />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column property="fileName" label="文件" show-overflow-tooltip />
            <el-table-column label="操作" width="120">
              <template #default="scope">
                <el-button
                  @click="emit('handlePreview', scope.row)"
                  class="common-icon-btn"
                  type="primary"
                  plain
                  link
                >
                  <template #icon>
                    <SvgIcon name="view" />
                  </template>
                </el-button>
                <el-button
                  @click="handleDetails(scope.row)"
                  class="common-icon-btn"
                  type="primary"
                  plain
                  link
                >
                  <template #icon>
                    <SvgIcon name="details" />
                  </template>
                </el-button>
                <el-button
                  @click="handleDel(scope.row)"
                  class="common-icon-btn"
                  type="danger"
                  plain
                  link
                >
                  <template #icon>
                    <SvgIcon name="delete" />
                  </template>
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-form>
    <SelectFileDialog ref="SelectFileDialogRef" @get-select-data="getSelectData" />
    <FileMetadata ref="FileMetadataRef" :is-edit="false" />
  </ElScrollbar>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { ElButton, ElMessage, ElScrollbar } from 'element-plus'
import SelectFileDialog from './SelectFileDialog.vue'
import FileMetadata from '@/components/FileMetadata/index.vue'
import {
  queryFileTypeApi,
  queryDateFormatApi,
  addSceneInfoApi,
  querySceneDetailApi,
} from '@/api/sequential_scene'
import useGlobalData from '@/store/useGlobalData'
import { useRouter, useRoute } from 'vue-router'

const router = useRouter()
const route = useRoute()

// 获取场景详情
const getDataDetail = async () => {
  try {
    const { data, status, message } = await querySceneDetailApi({ tid: route.query.tid })
    if ([200].includes(status)) {
      formData.value = data
      formData.value.fileList = formData.value.fileList.map((item: any) => ({
        fileName: item.fileName,
        sceneTimeFormat: item.sceneTimeFormat,
        tid: item.userFileId,
        gisPreviewVO: item.detail.gisPreviewVO,
        metadataVO: item.detail.metadataVO,
      }))
    } else {
      ElMessage.error(message || '获取场景信息失败!')
    }
  } catch (error) {}
}
if (route.query.tid) {
  getDataDetail()
}

// 字典值
const globalData = useGlobalData()
const fileTypeOptions = ref<any[]>([])
const dateFormatOptions = ref<any[]>([])
const getFileType = async () => {
  try {
    const { data, status } = await queryFileTypeApi()
    if ([200].includes(status)) {
      fileTypeOptions.value = data
    }
    const res = await queryDateFormatApi()
    if ([200].includes(res.status)) {
      dateFormatOptions.value = res.data
    }
  } catch (error) {}
}
getFileType()

// 时间选择器类型
const pickerType = computed(() => {
  switch (formData.value?.dateFormat) {
    case 'yyyy':
      return 'year'
    case 'yyyy-MM':
      return 'month'
    case 'yyyy-MM-dd':
      return 'date'
    default:
      return 'datetime'
  }
})
const handleFormat = () => {
  if (formData.value.fileList && formData.value.fileList.length) {
    formData.value.fileList = formData.value.fileList.map((item: any) => ({
      ...item,
      sceneTimeFormat: '',
    }))
  }
}

// 选择文件
const SelectFileDialogRef = ref<any>()
const handleSelectFile = () => {
  if (!formData.value?.fileTypeId) {
    ElMessage.warning('请先选择时序类型!')
    return
  }
  const catalogMenu: any = globalData.catalogMenuList.find((item: any) =>
    [item.tid].includes(formData.value?.fileTypeId),
  )
  SelectFileDialogRef.value?.handleOpen(catalogMenu)
}
const getSelectData = (data: any[]) => {
  formData.value.fileList = data
}

// 表单数据
const formRef = ref<any>()
const formData = ref<any>({
  fileList: [],
})
const validatePass = (_rule: any, value: any, callback: any) => {
  const reg = /^[1-9]\d*$/
  if (!reg.test(value)) {
    callback(new Error('必须是正整数！'))
  } else {
    callback()
  }
}
const rules = ref<any>({
  name: { required: true, message: '请输入场景名称', trigger: 'blur' },
  fileTypeId: { required: true, message: '请选择时序类型', trigger: 'blur' },
  interval: [
    { required: true, message: '请输入时间间隔', trigger: 'blur' },
    { validator: validatePass, trigger: 'blur' },
  ],
  dateFormat: { required: true, message: '请选择时间分类', trigger: 'blur' },
  fileList: { required: true, message: '请选择文件', trigger: 'blur' },
  sceneTimeFormat: { required: true, message: '请输入时间', trigger: 'blur' },
})

// 时序场景预览
const handleScenePreview = () => {
  if (formData.value.fileList.length <= 1) return ElMessage.warning('请选择至少2个文件')
  if (!formData.value?.interval) return ElMessage.warning('请填写时间间隔！')
  emit('handleScenePreview', formData.value?.fileList, formData.value?.interval)
}

// 保存
const handleSave = async () => {
  if (!formRef.value) return
  await formRef.value.validate(async (valid: boolean) => {
    if (!valid) {
      ElMessage.warning('请填写相应表单信息！')
      return
    }
    try {
      if (formData.value.fileList.length <= 1) return ElMessage.warning('请选择至少2个文件！')
      const fileList: any[] = formData.value.fileList.map((item: any) => ({
        userFileId: item.userFileId || item.tid,
        sceneTimeFormat: item.sceneTimeFormat,
      }))
      const { status, message } = await addSceneInfoApi({ ...formData.value, fileList })
      if ([200].includes(status)) {
        ElMessage.success('保存成功')
        formRef.value?.resetFields()
        router.push({ name: 'sceneList' })
      } else {
        ElMessage.error(message || '保存场景信息失败！')
      }
    } catch (error) {}
  })
}

// 清空数据
const handleResetFields = () => {
  formRef.value?.resetFields()
  formData.value.fileList = []
}

// 元数据详情
const FileMetadataRef = ref<any>()
const handleDetails = (row: any) => {
  FileMetadataRef.value.handleOpen(row)
}

// 删除
const handleDel = (row: any) => {
  formData.value.fileList = formData.value.fileList.filter((item: any) => item.tid !== row.tid)
}

const emit = defineEmits<{
  handlePreview: [row: any]
  handleScenePreview: [rowlist: any[], interval: any]
}>()
</script>

<style lang="less" scoped>
.left-edit {
  width: 375px;
  box-sizing: border-box;
  height: 100%;
  overflow-x: hidden;
  .formData {
    margin-top: 20px;
    box-sizing: border-box;
    width: 100%;
    .select-view {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
    }
  }
  .table {
    flex: 1;
    box-sizing: border-box;
    overflow-x: hidden;
    .table-form {
      margin-bottom: 0;
    }
  }
}
</style>
