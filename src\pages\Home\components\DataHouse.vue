<template>
  <div class="data-house">
    <Echarts v-if="options" :options="options" />
  </div>
</template>
<script lang="ts" setup>
import { EChartsOption } from 'echarts'
import Echarts from '@/components/Echarts/index.vue'
import { ref } from 'vue'

const props = defineProps<{ catalogData: any; type: number }>()

const options = ref<EChartsOption>()

const getOptions = () => {
  let data: any[] = []
  props.type === 1
    ? (data = props.catalogData.catalogWarehouseCountList)
    : (data = props.catalogData.catalogWarehouseSizeList)
  options.value = {
    tooltip: {
      trigger: 'item',
      show: true,
      formatter: '{b}: {d}%',
    },
    legend: {
      orient: 'vertical',
      icon: 'rect',
      right: 0,
      top: 'center',
    },
    series: [
      {
        name: '数据占比',
        type: 'pie',
        radius: ['40%', '50%'],
        center: ['40%', '50%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2,
        },
        data: data.map((item: any) => ({ value: item.size, name: item.catalogAlias })),
      },
    ],
  }
}

getOptions()
</script>
<style scoped lang="less">
.data-house {
  flex: 1;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
