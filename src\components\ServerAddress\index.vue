<template>
  <el-dialog
    v-model="dialogFormVisible"
    top="10vh"
    title="服务地址"
    width="900px"
    :close-on-click-modal="false"
    @close="handleDialogClose"
  >
    <el-table
      header-cell-class-name="common-table-header"
      cell-class-name="common-table-cell"
      :data="tableData"
      row-key="tid"
    >
      <el-table-column property="protocol" label="服务协议" width="150" />
      <el-table-column property="gridSets" label="坐标参照系" width="150">
        <template #default="scope">
          <el-select
            size="small"
            v-model="scope.row.gridsets"
            placeholder="请选择"
            @change="
              (val: string) => {
                handleSelectGridSet(val, scope.row)
              }
            "
          >
            <el-option
              v-for="(item, i) in scope.row.gridSetOptions"
              :key="i"
              :label="item"
              :value="item"
            >
            </el-option>
          </el-select>
        </template>
      </el-table-column>
      <el-table-column property="createTime" label="支持格式" width="150">
        <template #default="scope">
          <el-select
            size="small"
            v-model="scope.row.formats"
            placeholder="请选择"
            @change="
              (val: string) => {
                handleSelect(val, scope.row)
              }
            "
          >
            <el-option
              v-for="(item, i) in scope.row.formatOptions"
              :key="i"
              :label="item"
              :value="item"
            >
            </el-option>
          </el-select>
        </template>
      </el-table-column>
      <el-table-column property="createTime" label="地址">
        <template #default="scope">
          <el-input
            size="small"
            v-model="scope.row.serverUrl"
            placeholder="地址"
            readonly
          ></el-input>
        </template>
      </el-table-column>
      <el-table-column prop="date" label="操作" width="100">
        <template #default="scope">
          <el-button
            @click="hanldeCopyLink(scope.row.serverUrl)"
            class="common-icon-btn"
            type="primary"
            plain
            link
            title="复制"
          >
            <template #icon>
              <SvgIcon name="copyDocument" />
            </template>
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </el-dialog>
</template>

<script setup lang="ts">
import useDialogForm from '@/hooks/useDialogForm'
import { serverListApi } from '@/api/data_catalog'
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import clipboardCopy from 'clipboard-copy'
import { getQueryString } from '@/utils'

const FORMAT: string[] = [
  'png',
  'pdf',
  'jpeg',
  'tiff',
  'geojson',
  'json',
  'mapbox-vector',
  'mvt',
  'gml',
  'webp',
]
const WMTSFORMAT: any = {
  png: 'image/png',
  jpeg: 'image/jpeg',
  webp: 'image/webp',
  geojson: 'application/json;type=geojson',
  'mapbox-vector': 'application/x-protobuf;type=mapbox-vector',
  mvt: 'application/x-protobuf;type=mapbox-vector',
}

const { dialogFormVisible, handleDialogClose, setDialogFormVisible } = useDialogForm()

const tableData = ref<any[]>([])

const handleOpen = async ({ tid, serverStatus }: any) => {
  console.log(serverStatus, 'serverStatus')
  try {
    const { data, status, message } = await serverListApi(tid)
    if ([200].includes(status)) {
      tableData.value = (data || []).map((item: any) => {
        const { formats, gridsets, serverSource, serverUrl, protocol } = item
        const formatOptions = (formats && formats.split(';')) || []
        const gridSetOptions = (gridsets && gridsets.split(';')) || []
        let format = '--'
        let gridSet = '--'
        const url = decodeURIComponent(serverUrl)
        if (['E-SERVER'].includes(serverSource) && ![0].includes(serverStatus)) {
          if (['WMTS'].includes(protocol)) {
            const { params } = getQueryString(url)
            format =
              Object.keys(WMTSFORMAT).find(
                (item: any) => WMTSFORMAT[item] === params.get('Format'),
              ) || '--'
          } else {
            format = getFiletr(url, FORMAT) || '--'
          }
          gridSet = getFiletr(url, gridSetOptions) || '--'
        } else {
          format = formats
          gridSet = gridSet
        }
        return {
          ...item,
          serverUrl: url,
          formatOptions,
          gridSetOptions,
          formats: format || '--',
          gridsets: gridSet || '--',
        }
      })
      setDialogFormVisible(true)
    } else {
      ElMessage.error(message)
    }
  } catch (error) {
    console.error(error)
  }
}

const getFiletr = (url: string, replaceArr: any[]) => {
  let data = replaceArr.filter((item) => url.indexOf(item) !== -1)
  return data[0]
}
const handleSelectGridSet = (val: string, row: any) => {
  let str = getFiletr(row.serverUrl, row.gridSetOptions)
  row.serverUrl = row.serverUrl.replaceAll(str, val)
}

const handleSelect = (val: string, row: any) => {
  if (['WMTS'].includes(row.protocol)) {
    const { url, params } = getQueryString(row.serverUrl)
    params.set('Format', WMTSFORMAT[val])
    row.serverUrl = decodeURIComponent(url.toString())
    return
  }
  let str = getFiletr(row.serverUrl, FORMAT)
  switch (val) {
    case 'png':
      row.serverUrl = row.serverUrl.replaceAll(str, 'png')
      break
    case 'jpeg':
      row.serverUrl = row.serverUrl.replaceAll(str, 'jpeg')
      break
    case 'tiff':
      row.serverUrl = row.serverUrl.replaceAll(str, 'tiff')
      break
    case 'geojson':
      row.serverUrl = ['WFS'].includes(row.type)
        ? row.serverUrl.replaceAll(str, 'json')
        : row.serverUrl.replaceAll(str, 'geojson')
      break
    case 'gml':
      row.serverUrl = row.serverUrl.replaceAll(str, 'gml')
      break
    case 'webp':
      row.serverUrl = row.serverUrl.replaceAll(str, 'webp')
      break
    case 'pdf':
      row.serverUrl = row.serverUrl.replaceAll(str, 'pdf')
      break
    default:
      break
  }
}

const hanldeCopyLink = async (url: string) => {
  try {
    await clipboardCopy(url)
    ElMessage.success('复制成功！')
  } catch (error) {
    ElMessage.error('复制失败！')
  }
}

defineExpose({ handleOpen })
</script>

<style scoped></style>
