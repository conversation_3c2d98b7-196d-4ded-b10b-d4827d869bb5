<template>
  <div class="ModelLog" :class="{ expanded: isShowLog }">
    <div class="log-up" @click="handleShowLog">
      <SvgIcon class="icon" :name="isShowLog ? 'onlineModelDown' : 'onlineModelUp'" />
    </div>
    <transition name="sidebar-slide">
      <div class="main" v-show="isShowLog" ref="MessageRef">
        <div class="content-main">
          <div class="log-item" v-for="(log, index) in props.logList" :key="index">
            <span>{{ log.messageDesc }}</span>
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, nextTick } from 'vue'

const props = withDefaults(defineProps<{ logList?: any[] }>(), {
  logList: () => [] as any[],
})

const MessageRef = ref<any>()
const scrollBottom = () => {
  if (MessageRef.value) {
    MessageRef.value.scrollTop = MessageRef.value?.scrollHeight
  }
}

watch(
  () => props.logList,
  () => {
    nextTick(() => {
      scrollBottom()
    })
  },
  {
    deep: true,
  },
)

const isShowLog = ref<boolean>(false)
const handleShowLog = () => {
  isShowLog.value = !isShowLog.value
}
const handleOpenLog = () => {
  isShowLog.value = true
}

defineExpose({ handleOpenLog })
</script>

<style lang="less" scoped>
.ModelLog {
  height: 0;
  box-sizing: border-box;
  margin: 0 10px;
  position: relative;
  z-index: 100;
  background-color: @withe;
  transition: all 0.3s ease; /* 添加过渡效果 */
  .log-up {
    width: 100px;
    height: 25px;
    background-color: @withe;
    border-radius: 16px 16px 0px 0px;
    position: absolute;
    top: -25px;
    left: 50%;
    transform: translateX(-50%);
    cursor: pointer;
    text-align: center;
    line-height: 25px;
    .icon {
      color: rgba(196, 198, 204, 1);
    }
  }
  &.expanded {
    height: 20vh;
    box-shadow: 0 8px 8px 8px rgba(0, 0, 0, 0.05);
  }
  .sidebar-slide-enter-active,
  .sidebar-slide-leave-active {
    transition: all 0.3s ease; /* 添加过渡效果 */
  }
  .main {
    height: 100%;
    overflow-y: hidden;
    padding: 15px 0;
    box-sizing: border-box;
    .content-main {
      padding: 0 15px;
      overflow-y: auto;
      height: 100%;
      box-sizing: border-box;
      .no-scrollbar(0px);
    }
    .log-item {
      display: flex;
      span {
        font-size: 14px;
        font-weight: 400;
        color: #333333;
        line-height: 24px;
      }
      :last-child {
        margin-left: 10px;
        flex: 1;
        overflow: hidden;
      }
    }
  }
}
</style>
