<template>
  <div class="header">
    <div class="left">
      <img :src="userInfo.systemInfo?.pictureUrl || logo" alt="logo" />
      <p>{{ userInfo.systemInfo?.name }}</p>
    </div>
    <div class="center">
      <RouterLink
        v-for="p in menulist"
        :class="['nav-item']"
        :key="p.path"
        :to="{ name: p.path }"
        >{{ p.name }}</RouterLink
      >
    </div>
    <div class="right">
      <ElDropdown trigger="click">
        <div class="user-center">
          <img :src="logo" alt="logo" />
          <span>{{ userInfo.userInfo?.name || 'admin' }}</span>
        </div>
        <template #dropdown>
          <!-- <el-dropdown-item @click="handleSet">系统设置</el-dropdown-item> -->
          <el-dropdown-item @click="userInfo.handleLogout">退出登录</el-dropdown-item>
        </template>
      </ElDropdown>
    </div>
  </div>
</template>

<script setup lang="ts">
import { RouterLink } from 'vue-router'
import logo from '@/assets/commonImg/logo.png'
import useUserInfo from '@/store/useUserInfo'
import { ElDropdown } from 'element-plus'
import { getMenuList } from '@/utils'

// 零时代码

const userInfo = useUserInfo()

// 菜单目录
const menulist = getMenuList(userInfo.menuList)

// const router = useRouter()

// const handleSet = () => {
//   router.push({ name: 'setting' })
// }
</script>

<style lang="less" scoped>
.header {
  height: 60px;
  background-image: url('../assets/commonImg/headerBg.webp');
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  padding: 0 20px 0 20px;
  .left {
    display: flex;
    align-items: center;
    > img {
      width: 40px;
      height: 40px;
    }
    > p {
      margin-left: 20px;
      font-size: 22px;
      font-family: Source Han Sans CN;
      font-weight: 500;
      color: @withe;
    }
  }
  .center {
    margin-left: 4.1667vw;
    flex: 1;
    height: 100%;
    box-sizing: border-box;
    overflow: hidden;
    display: flex;
    align-items: center;
    .nav-item {
      font-size: 16px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #bdced9;
      line-height: 60px;
      cursor: pointer;
      margin: 0 15px;
    }
    :deep(.router-link-active) {
      color: var(--el-color-primary);
      position: relative;
      &::after {
        position: absolute;
        width: 60px;
        left: 50%;
        transform: translateX(-50%);
        content: '';
        bottom: 0;
        border-bottom: 2px solid var(--el-color-primary);
      }
    }
  }
  .right {
    box-sizing: border-box;
    .user-center {
      display: flex;
      align-items: center;
      cursor: pointer;
      > img {
        width: 32px;
        height: 32px;
        border-radius: 50%;
      }
      > span {
        font-size: 14px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: @withe;
        margin-left: 0.7813vw;
      }
    }
  }
}
</style>
