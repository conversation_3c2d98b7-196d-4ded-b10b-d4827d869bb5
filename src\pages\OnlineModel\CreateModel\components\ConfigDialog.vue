<template>
  <ElDialog
    title="样式设置"
    v-model="dialogFormVisible"
    width="70%"
    top="5vh"
    :close-on-click-modal="false"
    @close="handleDialogClose"
    @opened="handleOpenSetJsonData"
  >
    <ElScrollbar height="65vh">
      <div class="setStyle">
        <div class="formData">
          <ElForm ref="formRef" :model="formData" label-width="100px" label-position="left">
            <p class="titleFlex">
              <span class="title">背景设置</span>
              <ElButton @click="getDefaultNodeConfig" type="primary" text link>恢复默认</ElButton>
            </p>
            <ElFormItem label="默认背景颜色" prop="backgroundColor">
              <ElInput v-model="formData.backgroundColor" readonly>
                <template #append>
                  <ElColorPicker
                    @change="handleChange"
                    v-model="formData.backgroundColor"
                    color-format="hex"
                  />
                </template>
              </ElInput>
            </ElFormItem>
            <p class="title">节点样式</p>
            <ElFormItem label="节点形状" prop="defaultNodeShape">
              <ElRadioGroup @change="handleChange" v-model="formData.defaultNodeShape">
                <ElRadioButton :value="0" label="圆形" />
                <ElRadioButton :value="1" label="方形" />
              </ElRadioGroup>
            </ElFormItem>
            <ElFormItem label="节点颜色" prop="defaultNodeColor">
              <ElInput v-model="formData.defaultNodeColor" readonly>
                <template #append>
                  <ElColorPicker
                    @change="handleChange"
                    v-model="formData.defaultNodeColor"
                    color-format="hex"
                  />
                </template>
              </ElInput>
            </ElFormItem>
            <ElFormItem label="节点文字颜色" prop="defaultNodeFontColor">
              <ElInput v-model="formData.defaultNodeFontColor" readonly>
                <template #append>
                  <ElColorPicker
                    @change="handleChange"
                    v-model="formData.defaultNodeFontColor"
                    color-format="hex"
                  />
                </template>
              </ElInput>
            </ElFormItem>
            <ElFormItem label="节点边框颜色" prop="defaultNodeBorderColor">
              <ElInput v-model="formData.defaultNodeBorderColor" readonly>
                <template #append>
                  <ElColorPicker
                    @change="handleChange"
                    v-model="formData.defaultNodeBorderColor"
                    color-format="hex"
                  />
                </template>
              </ElInput>
            </ElFormItem>
            <p class="title">连线样式</p>
            <ElFormItem label="默认连线颜色" prop="defaultLineColor">
              <ElInput v-model="formData.defaultLineColor" readonly>
                <template #append>
                  <ElColorPicker
                    @change="handleChange"
                    v-model="formData.defaultLineColor"
                    color-format="hex"
                  />
                </template>
              </ElInput>
            </ElFormItem>
            <ElFormItem label="线条样式" prop="defaultLineShape">
              <ElSelect
                @change="handleChange"
                v-model="formData.defaultLineShape"
                style="width: 100%"
              >
                <ElOption label="直线" :value="1" />
                <ElOption label="简洁" :value="2" />
                <ElOption label="鱼尾" :value="5" />
                <ElOption label="折线" :value="4" />
              </ElSelect>
            </ElFormItem>
            <ElFormItem label="线条粗细" prop="defaultLineWidth">
              <ElRadioGroup @change="handleChange" v-model="formData.defaultLineWidth">
                <ElRadioButton :value="1" label="1" />
                <ElRadioButton :value="2" label="2" />
                <ElRadioButton :value="3" label="3" />
                <ElRadioButton :value="5" label="5" />
                <ElRadioButton :value="8" label="8" />
              </ElRadioGroup>
            </ElFormItem>
          </ElForm>
        </div>
        <div class="graph">
          <RelationGraph :options="options" ref="RelationGraphRef" />
        </div>
      </div>
    </ElScrollbar>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="setDialogFormVisible(false)">取消</el-button>
        <el-button type="primary" @click="handleSubimit">确定</el-button>
      </span>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
import {
  ElColorPicker,
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElRadioGroup,
  ElRadioButton,
  ElScrollbar,
  ElSelect,
  ElMessage,
  ElButton,
} from 'element-plus'
import RelationGraph from 'relation-graph-vue3'
import useDialogForm from '@/hooks/useDialogForm'
import useRelationGraph from '@/hooks/useRelationGraph'
import { computed } from 'vue'
import { nodeConfigModifyByIdApi, queryDefaultNodeConfigApi } from '@/api/online_model'

const { dialogFormVisible, formRef, handleDialogClose, setDialogFormVisible } = useDialogForm()
const { RelationGraphRef, options, setOptions, getOptions, setJsonData } = useRelationGraph()

const formData = computed<any>({
  get() {
    return options.value
  },
  set() {},
})
const handleChange = () => {
  setOptions(formData.value)
}

const graphJsonData: any = {
  rootId: '',
  nodes: [
    {
      id: 'a',
      text: 'A',
      fixed: true,
      x: 341.00003051758,
      y: 131.090270996094,
    },
    {
      id: 'b',
      text: 'B',
      fixed: true,
      x: 392.333343505859,
      y: 240.513885498047,
    },
    {
      id: 'c',
      text: 'C',
      fixed: true,
      x: 294.333343505859,
      y: 219.513885498047,
    },
    {
      id: 'e',
      text: 'E',
      fixed: true,
      x: 355.333343505859,
      y: 16.513885498047,
    },
  ],
  lines: [
    {
      from: 'a',
      to: 'b',
      text: '',
    },
    {
      from: 'a',
      to: 'c',
      text: '',
    },
    {
      from: 'a',
      to: 'e',
      text: '',
    },
    {
      from: 'b',
      to: 'e',
      text: '',
    },
  ],
}

const getDefaultNodeConfig = async () => {
  try {
    const { status, data, message } = await queryDefaultNodeConfigApi()
    if ([200].includes(status)) {
      setOptions(data)
    } else {
      ElMessage.error(message || '获取默认节点配置失败')
    }
  } catch (error) {
    console.log(error)
  }
}

const handleSubimit = async () => {
  try {
    const { status, message } = await nodeConfigModifyByIdApi(options.value)
    if ([200].includes(status)) {
      ElMessage.success(message)
      emit('handleRefresh', options.value)
      setDialogFormVisible(false)
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}

const handleOpenSetJsonData = async () => {
  await getOptions()
  await setJsonData(graphJsonData, true)
}

const handleOpen = async () => {
  setDialogFormVisible(true)
}

const emit = defineEmits<{
  (e: 'handleRefresh', options: any): void
}>()
defineExpose({ handleOpen })
</script>

<style scoped lang="less">
.setStyle {
  display: flex;
  overflow-y: auto;
  min-height: 65vh;
  .formData {
    width: 30%;
    min-width: 300px;
    box-sizing: border-box;
    .titleFlex {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 30px;
      .title {
        margin: 0;
      }
    }
    .title {
      font-weight: 600;
      color: #333333;
      margin: 30px 0;
    }
    :deep(.el-input-group__append) {
      padding: 0;
    }
  }
  .graph {
    margin-left: 20px;
    flex: 1;
    box-sizing: border-box;
    overflow: hidden;
  }
}
</style>
