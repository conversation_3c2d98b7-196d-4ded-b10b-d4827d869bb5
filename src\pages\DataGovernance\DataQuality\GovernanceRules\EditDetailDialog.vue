<template>
  <ElDialog
    v-model="dialogFormVisible"
    title="详情"
    top="10vh"
    width="550px"
    :close-on-click-modal="false"
    @close="handleDialogClose"
  >
    <ElForm ref="formRef" :model="formData" label-width="100px">
      <ElFormItem label="规则名称" prop="autoRuleName">
        <ElInput readonly v-model="formData.autoRuleName" placeholder="请输入规则名称" />
      </ElFormItem>
      <ElFormItem label="适用数据" prop="fileTypeName">
        <ElInput readonly v-model="formData.fileTypeName" placeholder="请输入适用数据" />
      </ElFormItem>
      <ElFormItem label="治理规则" prop="autoRuleMethod">
        <ElInput
          readonly
          :rows="3"
          type="textarea"
          v-model="formData.autoRuleMethod"
          placeholder="请输入治理规则"
        />
      </ElFormItem>
      <ElFormItem label="规则说明" prop="autoRuleContent">
        <ElInput
          readonly
          :rows="3"
          type="textarea"
          v-model="formData.autoRuleContent"
          placeholder="请输入规则说明"
        />
      </ElFormItem>
    </ElForm>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="setDialogFormVisible(false)">取消</el-button>
        <el-button type="primary" @click="setDialogFormVisible(false)">确定</el-button>
      </span>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
import { ElDialog, ElForm, ElFormItem, ElInput } from 'element-plus'
import useDialogForm from '@/hooks/useDialogForm'
import { ref } from 'vue'

const { dialogFormVisible, formRef, setDialogFormVisible, handleDialogClose } = useDialogForm()

const formData = ref<any>({})

const handleOpen = (row: any) => {
  formData.value = row
  setDialogFormVisible(true)
}

defineExpose({ handleOpen })
</script>

<style scoped></style>
