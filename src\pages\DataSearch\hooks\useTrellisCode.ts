import { getBdJson<PERSON><PERSON>, getGrid<PERSON><PERSON> } from '@/api/data_search'
import * as Cesium from 'cesium'
import { reactive } from 'vue'

interface CodeData {
  selectedSquare: any[]
  isSelectCode: boolean
  isGeoSot: boolean
}
export default function useTrellisCode(cesiumViewer: any) {
  const codeData = reactive<CodeData>({
    selectedSquare: [],
    isSelectCode: false,
    isGeoSot: true,
  })

  let geoJsonData: any = null

  // 设置是否可选择网格码
  const setIsSelectCode = (isSelectCode: boolean) => {
    codeData.isSelectCode = isSelectCode
  }

  // 设置是否可选择网格码
  const setTrellisCode = async () => {
    try {
      clearSelectedSquare()
      const viewer: Cesium.Viewer = cesiumViewer.value
      const bbox = getCurrentBoundingBox()
      const { data } = codeData.isGeoSot ? await getGridApi(bbox) : await getBdJsonApi(bbox)
      console.log(Date.now(), 'sss')
      const dataSource = await Cesium.GeoJsonDataSource.load(data, {
        stroke: Cesium.Color.WHITE,
        fill: Cesium.Color.WHITE.withAlpha(0.01),
        strokeWidth: 3.0,
      })
      clearTrelisCode(false)
      geoJsonData = dataSource
      viewer.dataSources.add(dataSource)
      console.log(Date.now(), 'qqq')
    } catch (error) {}
  }

  const clearTrelisCode = (isRemoveEvent: boolean = true) => {
    const viewer: Cesium.Viewer = cesiumViewer.value
    if (geoJsonData) {
      viewer.dataSources.removeAll()
      geoJsonData = null
    }
    if (isRemoveEvent) {
      viewer.camera.moveEnd.removeEventListener(setTrellisCode)
    }
  }

  const clearSelectedSquare = () => {
    if (!geoJsonData) return
    geoJsonData.entities.values.forEach((entity: any) => {
      if (
        codeData.selectedSquare
          .map((item: any) => item.properties.code)
          .includes(entity.properties.code._value)
      ) {
        entity.polygon.material = Cesium.Color.WHITE.withAlpha(0.01)
      }
    })
    codeData.selectedSquare = []
  }

  // 获取bbox
  const getCurrentBoundingBox = () => {
    const viewer: any = cesiumViewer.value
    // 获取左下角和右上角的经纬度
    var southwest = Cesium.Rectangle.southwest(viewer.camera.computeViewRectangle())
    var northeast = Cesium.Rectangle.northeast(viewer.camera.computeViewRectangle())
    return [
      Cesium.Math.toDegrees(southwest.longitude),
      Cesium.Math.toDegrees(southwest.latitude),
      Cesium.Math.toDegrees(northeast.longitude),
      Cesium.Math.toDegrees(northeast.latitude),
    ]
  }

  const initTrellisCode = (isGeoSot: boolean) => {
    const viewer: Cesium.Viewer = cesiumViewer.value
    if (!viewer) return
    codeData.isGeoSot = isGeoSot
    setTrellisCode()
    cesiumViewer.value.camera.moveEnd.addEventListener(setTrellisCode)
    // 鼠标移动事件
    viewer.screenSpaceEventHandler.setInputAction(function (movement: any) {
      const pickedObject = viewer.scene.pick(movement.endPosition)
      if (Cesium.defined(pickedObject) && pickedObject.id) {
        const pickedEntity = pickedObject.id

        // 如果点击的是 GeoJSON 数据源中的实体，可以改变鼠标样式
        if (geoJsonData.entities.contains(pickedEntity)) {
          ;(viewer.container as any).style.cursor = 'pointer'
        }
      } else {
        // 恢复默认鼠标样式
        ;(viewer.container as any).style.cursor = 'default'
      }
    }, Cesium.ScreenSpaceEventType.MOUSE_MOVE)
    // 添加点击事件处理程序
    viewer.screenSpaceEventHandler.setInputAction(function (click: any) {
      if (!codeData.isSelectCode) return
      const pickedObject = viewer.scene.pick(click.position)
      if (Cesium.defined(pickedObject) && pickedObject.id) {
        const pickedEntity = pickedObject.id
        // 确保点击的是 GeoJSON 数据源中的实体
        if (geoJsonData.entities.contains(pickedEntity)) {
          // 获取实体的 properties 属性
          const properties = pickedEntity.properties
          if (
            codeData.selectedSquare
              .map((item: any) => item.properties.code)
              .includes(properties.code._value)
          ) {
            codeData.selectedSquare = codeData.selectedSquare.filter(
              (item: any) => item.properties.code !== properties.code._value,
            )
            pickedEntity.polygon.material = Cesium.Color.WHITE.withAlpha(0.01)
          } else {
            codeData.selectedSquare.push({
              properties: {
                code: properties.code._value,
              },
            })
            pickedEntity.polygon.material = Cesium.Color.ORANGE.withAlpha(0.5)
          }
          console.log(codeData.selectedSquare, 'selectedSquare')
        }
      }
    }, Cesium.ScreenSpaceEventType.LEFT_CLICK)
  }

  return { codeData, initTrellisCode, setIsSelectCode, clearTrelisCode, clearSelectedSquare }
}
