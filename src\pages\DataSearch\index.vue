<template>
  <div class="dataSearch">
    <div class="mapbox" id="CesiumRef">
      <!-- 时序场景时间线 -->
      <MapboxTimeLine
        v-show="sceneData.rowList.length"
        @hanlde-is-paly="hanldeIsPaly"
        :palyList="sceneData.rowList"
        :currentIndex="sceneData.currentIndex"
        :isPlay="sceneData.isPlay"
      />
      <!-- 位置服务预览 -->
      <div v-if="rowData" class="formData">
        <el-radio-group @change="handleChangeType" v-model="formData.type">
          <el-radio :value="0">实时</el-radio>
          <el-radio :value="1">回放</el-radio>
        </el-radio-group>
        <template v-if="formData.type">
          <el-date-picker
            @change="handlechangeTime"
            style="margin-left: 20px"
            v-model="formData.dateRange"
            value-format="YYYY-MM-DD HH:mm:ss"
            :shortcuts
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
          />
          <el-select style="width: 120px; margin-left: 20px" v-model="formData.speed">
            <el-option v-for="(p, i) in speedList" :key="i" :label="p.label" :value="p.value" />
          </el-select>
          <el-button v-if="playData.timer" @click="handleStop" type="primary"> 暂停 </el-button>
          <el-button v-else @click="handlePaly" type="primary"> 播放 </el-button>
        </template>
      </div>
      <div v-if="formData.dateRange && formData.dateRange.length" class="map-siler">
        <ElSlider
          :format-tooltip="(value: number) => dayjs(value).format('YYYY-MM-DD HH:mm:ss')"
          :min="playData.start"
          :max="playData.end"
          v-model="playData.targetTime"
        />
      </div>
    </div>
    <!-- 搜索框 -->
    <SearchTool
      @handle-reset-map="handleResetMap"
      @handle-tool="handleTool"
      @handle-preview="handleLayersPreview"
      :coordinateObj="mapTools"
      :trellisCode="trellisCode"
    />
    <!-- 右下角工具栏 -->
    <IconTool @handle-icon="handleIcon" />

    <!-- 切换地图影像、矢量底图 -->
    <div class="change-baseMap-box">
      <div
        v-for="p in baseMapList"
        :key="p.type"
        :class="{ active: [p.type].includes(baseMapType) }"
        @click="handleChangeMap(p.type)"
      >
        <img :src="p.url" />
        <span>{{ p.name }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import SearchTool from './SearchTool/index.vue'
import IconTool from './IconTool.vue'
import yx from '@/pages/DataSearch/images/yx.png'
import sl from '@/pages/DataSearch/images/sl.png'
import { ref, onMounted, onUnmounted, h } from 'vue'
import MapboxTimeLine from '@/components/MapboxTimeLine/index.vue'
import useCesium from '@/hooks/useCesium'
import useTrellisCode from './hooks/useTrellisCode'
import { dayjs, ElMessage } from 'element-plus'
import { userFileListDetailApi } from '@/api/data_catalog'
import { useRouter } from 'vue-router'
import useScenePreview from '@/hooks/useScenePreview'
import { querySceneDetailApi } from '@/api/sequential_scene'
import { cesiumPreview } from '@/utils/fileMap'
import usePositionMap from '@/hooks/usePositionMap'
import * as Cesium from 'cesium'
import { IotPlaybackApi } from '@/api/stream_data'

const shortcuts = [
  {
    text: '最近一周',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setDate(start.getDate() - 7)
      return [start, end]
    },
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setMonth(start.getMonth() - 1)
      return [start, end]
    },
  },
  {
    text: '最近三个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setMonth(start.getMonth() - 3)
      return [start, end]
    },
  },
]

const speedList = [
  { label: '1倍速', value: 1 },
  { label: '2倍速', value: 2 },
  { label: '4倍速', value: 4 },
  { label: '8倍速', value: 8 },
  { label: '50倍速', value: 50 },
  { label: '100倍速', value: 100 },
  { label: '1000倍速', value: 1000 },
]

const router = useRouter()

const {
  initCesium,
  handleChangeBaseMap,
  mapOperate,
  cesiumViewer,
  destroyCesium,
  setTmsPreviewLayers,
  clearCesiumPreviewLayers,
  mapTools,
  set3DPreviewLayers,
} = useCesium()
const trellisCode = useTrellisCode(cesiumViewer)
onMounted(() => {
  initCesium('CesiumRef', () => {
    handleChangeBaseMap()
    mapOperate.zoomToCenter()
  })
})

onUnmounted(() => {
  destroyCesium()
})

const handleResetMap = () => {
  mapTools.removeAllEvent()
  mapTools.clearEntity()
  trellisCode.clearSelectedSquare()
  clearCesiumPreviewLayers()
  clearData()
  rowData.value = null
  handleClearReal()
  handleClearTimer()
}

const handleTool = (type: string) => {
  mapTools.registerEvents(
    ['0'].includes(type) ? 'Rectangle' : ['1'].includes(type) ? 'Circle' : 'Polygon',
  )
}

const handleLayersPreview = (row: any) => {
  clearData()
  handleClearReal()
  handleClearTimer()
  if (['iot'].includes(row.queryType)) {
    rowData.value = row
    handleRealPreview(row)
    return
  }
  if (row.scene_type) {
    timeSequenceLayerPreview(row.user_file_id)
    return
  }
  ordinaryLayerPreview(row.user_file_id)
}

// 位置服务预览
const formData = ref<any>({ type: 0, speed: 1, dateRange: [] })
const rowData = ref<any>()
const handleChangeType = () => {
  if (formData.value.type) {
    handleClearReal()
  } else {
    handleClearTimer()
    handleRealPreview(rowData.value)
  }
}
// 实时数据
const { initSocket, closeSocket } = usePositionMap()
const marker = ref<Cesium.Entity | any>()
const handleRealPreview = async (row: any) => {
  initSocket(row.file_name, ({ lon, lat, json }: any) => {
    const keys = Object.values(json).join(',')
    if (marker.value) {
      marker.value.position = Cesium.Cartesian3.fromDegrees(Number(lon), Number(lat))
      marker.value.label.text = keys
    } else {
      marker.value = cesiumViewer.value?.entities.add({
        position: Cesium.Cartesian3.fromDegrees(Number(lon), Number(lat)),
        billboard: {
          image: '/address.svg',
          width: 50,
          height: 50,
        },
        label: {
          text: keys,
          font: '24px Helvetica', // 字体
          fillColor: Cesium.Color.fromCssColorString('#262E3B'), // 字体颜色
          outlineColor: Cesium.Color.fromCssColorString('#262E3B'), // 轮廓颜色
          outlineWidth: 2, // 轮廓宽度
          verticalOrigin: Cesium.VerticalOrigin.BOTTOM, // 垂直对齐
          horizontalOrigin: Cesium.HorizontalOrigin.CENTER, // 水平对齐
          pixelOffset: new Cesium.Cartesian2(0, -30),
        },
      })
    }
    mapOperate.zoomToCenter([Number(lon), Number(lat)])
  })
}

const handleClearReal = () => {
  closeSocket()
  if (marker.value) {
    cesiumViewer.value?.entities.remove(marker.value)
    marker.value = null
  }
}

// 回放数据
const playData = ref<any>({
  start: 0,
  end: 0,
  targetTime: 0,
  timer: null,
  marker: [],
})
const handlechangeTime = (dateRange: any) => {
  handleStop()
  playData.value.marker.forEach((item: any) => {
    cesiumViewer.value?.entities.remove(item)
  })
  playData.value = {
    start: 0,
    end: 0,
    targetTime: 0,
    timer: null,
    marker: [],
  }
  playData.value.start = dateRange && dateRange.length ? dayjs(dateRange[0]).valueOf() : 0
  playData.value.end = dateRange && dateRange.length ? dayjs(dateRange[1]).valueOf() : 0
  playData.value.targetTime = dateRange && dateRange.length ? dayjs(dateRange[0]).valueOf() : 0
}
const handleStop = () => {
  if (playData.value.timer) {
    clearInterval(playData.value.timer)
    playData.value.timer = null
  }
}
const handleClearTimer = () => {
  formData.value.dateRange = []
  formData.value.speed = 1
  handleStop()
  playData.value.marker.forEach((item: any) => {
    cesiumViewer.value?.entities.remove(item)
  })
  playData.value = {
    start: 0,
    end: 0,
    targetTime: 0,
    timer: null,
    marker: [],
  }
}

onUnmounted(() => {
  handleClearTimer()
})

const getPlayData = async () => {
  try {
    const params = {
      tid: rowData.value.user_file_id,
      duration: formData.value.speed * 1500,
      targetTime: playData.value.targetTime,
    }
    const { data, status } = await IotPlaybackApi(params)
    if ([200].includes(status)) {
      const { targets, centerPoint } = data
      if (targets && targets.length) {
        playData.value.marker.forEach((item: any) => {
          cesiumViewer.value?.entities.remove(item)
        })
        playData.value.marker = []
        playData.value.marker = targets.map(({ extend, lon, lat }: any) => {
          const keys = Object.values(extend).join(',')
          return cesiumViewer.value?.entities.add({
            position: Cesium.Cartesian3.fromDegrees(Number(lon), Number(lat)),
            billboard: {
              image: '/address.svg',
              width: 50,
              height: 50,
            },
            label: {
              text: keys,
              font: '24px Helvetica', // 字体
              fillColor: Cesium.Color.fromCssColorString('#262E3B'), // 字体颜色
              outlineColor: Cesium.Color.fromCssColorString('#262E3B'), // 轮廓颜色
              outlineWidth: 2, // 轮廓宽度
              verticalOrigin: Cesium.VerticalOrigin.BOTTOM, // 垂直对齐
              horizontalOrigin: Cesium.HorizontalOrigin.CENTER, // 水平对齐
              pixelOffset: new Cesium.Cartesian2(0, -30),
            },
          })
        })
        mapOperate.zoomToCenter(centerPoint.split(',').map((item: any) => Number(item)))
      }
    }
  } catch (error) {}
}

const handlePaly = () => {
  if (playData.value.targetTime >= playData.value.end) {
    playData.value.targetTime = playData.value.start
  }
  playData.value.timer = setInterval(() => {
    playData.value.targetTime += formData.value.speed * 1500
    if (playData.value.targetTime > playData.value.end) {
      playData.value.targetTime = playData.value.end
      getPlayData()
      handleStop()
    }
    getPlayData()
  }, 1500)
}

// 时序图层预览
const timeSequenceLayerPreview = async (user_file_id: any) => {
  try {
    const { data, status, message } = await querySceneDetailApi({ tid: user_file_id })
    if ([200].includes(status)) {
      handleScenePreview(
        data.fileList.map((item: any) => ({
          fileName: item.fileName,
          sceneTimeFormat: item.sceneTimeFormat,
          tid: item.userFileId,
          gisPreviewVO: item.detail.gisPreviewVO,
          metadataVO: item.detail.metadataVO,
        })),
        data.interval,
      )
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}

const ordinaryLayerPreview = async (user_file_id: string) => {
  try {
    const { data, status, message } = await userFileListDetailApi({ tid: user_file_id })
    if ([200].includes(status) && data) {
      if ([0].includes(data.serverStatus)) {
        ElMessage.warning('服务发布失败，暂时无法预览!')
        return
      }
      if ([2].includes(data.serverStatus)) {
        ElMessage.warning('服务发布中，请稍后预览!')
        return
      }
      if ([3].includes(data.serverStatus)) {
        ElMessage({
          message: h('p', [
            h('span', null, '服务暂未发布，暂时无法预览! '),
            h(
              'i',
              {
                style: 'color: #598bfd;cursor: pointer;',
                onClick: () => {
                  router.push({ name: 'dataCatalog' })
                },
              },
              '发布服务',
            ),
          ]),
          type: 'warning',
        })
        return
      }
      if (cesiumPreview.includes(data.fileTypeId)) {
        set3DPreviewLayers(data.gisPreviewVO.url)
      } else {
        handlePreview(data)
      }
    } else {
      ElMessage.error(message || '预览失败！')
    }
  } catch (error) {}
}

const { sceneData, handleScenePreview, hanldeIsPaly, clearData, handlePreview } = useScenePreview(
  setTmsPreviewLayers,
  false,
)

//切换地图影像、矢量底图
const baseMapList = [
  {
    name: '星图影像',
    type: 'img',
    url: yx,
  },
  {
    name: '星图矢量',
    type: 'vec',
    url: sl,
  },
]
const baseMapType = ref<string>('img')
const handleChangeMap = (type: string) => {
  baseMapType.value = type
  handleChangeBaseMap(type)
}

//下方按钮
const handleIcon = (val: string) => {
  switch (val) {
    case 'handleAdd':
      mapOperate.zoomIn()
      break
    case 'handleReset':
      mapOperate.zoomToCenter()
      break
    case 'handleMinus':
      mapOperate.zoomOut()
      break
    default:
      handleResetMap()
      break
  }
}
</script>

<style scoped lang="less">
.dataSearch {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  position: relative;
  .mapbox {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    position: relative;
    :global(.mapboxgl-ctrl-bottom-left) {
      display: none;
    }
    .formData {
      position: absolute;
      height: 40px;
      right: 20px;
      top: 30px;
      background-color: @withe;
      display: flex;
      align-items: center;
      z-index: 10000;
      padding: 0 15px;
    }
    .map-siler {
      position: absolute;
      bottom: 30px;
      left: 15%;
      right: 15%;
      z-index: 10000;
    }
  }

  .change-baseMap-box {
    user-select: none;
    position: absolute;
    z-index: 100;
    bottom: 0;
    right: 20px;
    background-color: #293a5e;
    white-space: nowrap;
    width: 85px;
    transition: width 0.5s;
    overflow: hidden;
    padding: 4px;
    &:hover {
      width: 180px;
    }
    div {
      display: inline-block;
      position: relative;
      height: 60px;
      width: 86px;
      cursor: pointer;
      img {
        width: 100%;
        height: 100%;
      }
      span {
        position: absolute;
        right: 0px;
        bottom: 0px;
        color: @withe;
        background: #5994f1;
        font-size: 12px;
        padding: 3px 5px;
      }
    }
    div + div {
      margin-left: 5px;
    }
    div.active {
      border: 1px solid #5994f1;
    }
  }
}
</style>
