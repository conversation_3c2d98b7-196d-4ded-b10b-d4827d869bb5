import { defineStore } from 'pinia'
import { getSsoToken<PERSON><PERSON>, getSsoUser<PERSON><PERSON>, logout<PERSON>pi } from '@/api/login'
import { ElMessage } from 'element-plus'
import { arrayToTree, getUrlParam } from '@/utils'
import routerPage from '@/router/routerPage'
import { getLogo<PERSON>pi } from '@/api/common'

interface State {
  token: any
  appCode: string
  userInfo: any
  menuList: any[]
  buttons: any[]
  systemInfo: any
  primaryColor: string
}

// 生成路由列表
const keepAliveList = ['dataSource']
const fliterRouteList = (list: any[]): any[] => {
  const routesList: any[] = []
  list.forEach((item) => {
    const { name, path, meta, children, component } = item
    const routerObj: any = routerPage
    const route: any = {
      name,
      path,
      redirect: children && children.length ? `${children[0].path}` : undefined,
      component: routerObj[component],
      meta: { ...meta, keepAlive: keepAliveList.includes(name) },
    }
    if (children && children.length > 0) {
      route.children = fliterRouteList(children)
    }
    routesList.push(route)
  })
  return routesList
}

const getNewRgbaColor = (rgbaColor: string, newOpacity: number) => {
  return rgbaColor.replace(/rgba\((\d+), (\d+), (\d+), (\d?\.?\d+)\)/, (_match, r, g, b, _a) => {
    // 返回新的 rgba 字符串，修改透明度为 newOpacity
    return `rgba(${r}, ${g}, ${b}, ${newOpacity})`
  })
}

const useUserInfo = defineStore({
  id: 'userInfo',
  state: (): State => {
    return {
      token: null,
      appCode: 'SSO_CENTRE_DATA',
      userInfo: null,
      menuList: [],
      buttons: [],
      systemInfo: null,
      primaryColor: 'rgba(90, 139, 254, 1)',
    }
  },
  actions: {
    // 设置用户信息
    setUserInfo(userInfo: State['userInfo']) {
      this.userInfo = userInfo
    },
    // 设置Token
    setToken(token: State['token']) {
      this.token = token
    },
    // 设置菜单
    setMenuList(list: State['menuList']) {
      this.menuList = list
    },
    // 设置按钮权限
    setButtons(buttons: State['buttons']) {
      this.buttons = buttons
    },
    // 判断是否有按钮权限
    hasButton(name: string | string[]): boolean {
      if (!this.buttons.length) return true
      if (Array.isArray(name)) {
        return name.some((item) => this.buttons.includes(item))
      }
      return this.buttons.includes(name)
    },
    // 获取用户信息
    async getUserInfo() {
      try {
        const { status, data, message } = await getSsoUserApi()
        if ([200].includes(status)) {
          this.setUserInfo(data)
          const menuList = arrayToTree(data.menus || [])
          this.setMenuList(menuList)
          const btns = (data.buttons || []).map((item: any) => item.name)
          this.setButtons(btns)
          const routerList: any[] = fliterRouteList(menuList)
          await this.getSystemInfo()
          return Promise.resolve(routerList)
        } else {
          ElMessage.error(message)
        }
      } catch (error) {}
    },
    // 获取用户token
    async getToken() {
      const ticket = getUrlParam(window.location.href, 'ticket')
      if (!ticket) return
      try {
        const { data, message, status } = await getSsoTokenApi(ticket)
        if ([200].includes(status)) {
          this.setToken(data)
          // 获取当前URL
          const currentUrl = new URL(window.location.href)
          // 删除指定的查询参数
          if (currentUrl.searchParams.has('ticket')) {
            currentUrl.searchParams.delete('ticket')
          }
          window.location.href = currentUrl.toString()
        } else {
          ElMessage.error(message)
        }
      } catch (error) {}
    },
    // 退出登录
    async handleLogout() {
      try {
        const { status, message } = await logoutApi()
        if ([200].includes(status)) {
          this.setToken(null)
          window.location.reload()
        } else {
          ElMessage.error(message)
        }
      } catch (error) {}
    },
    // 设置系统信息
    setSystemInfo(info: State['systemInfo']) {
      this.systemInfo = info
    },
    // 设置主题色
    setPrimaryColor(color: State['primaryColor']) {
      this.primaryColor = color
      const el = document.documentElement
      // 设置 css 变量
      el.style.setProperty('--el-color-primary', color)
      el.style.setProperty('--el-color-primary-light-1', getNewRgbaColor(color, 0.9))
      el.style.setProperty('--el-color-primary-light-2', getNewRgbaColor(color, 0.8))
      el.style.setProperty('--el-color-primary-light-3', getNewRgbaColor(color, 0.7))
      el.style.setProperty('--el-color-primary-light-4', getNewRgbaColor(color, 0.6))
      el.style.setProperty('--el-color-primary-light-5', getNewRgbaColor(color, 0.5))
      el.style.setProperty('--el-color-primary-light-6', getNewRgbaColor(color, 0.4))
      el.style.setProperty('--el-color-primary-light-7', getNewRgbaColor(color, 0.3))
      el.style.setProperty('--el-color-primary-light-8', getNewRgbaColor(color, 0.2))
      el.style.setProperty('--el-color-primary-light-9', getNewRgbaColor(color, 0.1))
      el.style.setProperty('--el-color-primary-dark-2', color)
      // el.style.setProperty("--el-fill-color-light", getNewRgbaColor(color, 0.1));
      el.style.setProperty('--el-border-color-extra-light', getNewRgbaColor(color, 0.1))
    },
    // 获取系统信息
    async getSystemInfo() {
      try {
        const { status, data, message } = await getLogoApi({ appCode: this.appCode })
        if ([200].includes(status)) {
          this.setSystemInfo(data)
          let link: any = document.querySelector("link[rel*='icon']")
          link.href = data.pictureUrl
          window.document.title = data.name
          this.setPrimaryColor(this.primaryColor)
        } else {
          ElMessage.error(message)
        }
      } catch (error) {}
    },
  },
  persist: {
    storage: localStorage,
    // 持久化存储 这两个字段
    pick: ['token'],
  },
})

export default useUserInfo
