import { ImageCut, MapTools } from '@/types/mapbox'
import { reactive, ref } from 'vue'
import * as turf from '@turf/turf'
import { getTransformExtentData } from '@/utils/mapConfig'
import { Marker, Popup } from 'mapbox-gl'

export default function useImageCut(mapTools: MapTools, map: any) {
  const BtnRef = ref<HTMLDivElement | any>()

  const imageCutData = reactive<ImageCut['imageCutData']>({
    type: '',
    bbox: [],
    markers: new Popup({
      className: 'location-popup',
      closeButton: false,
      offset: [-66, 20],
    }),
    isElement: false,
  })

  const handleImageCut: ImageCut['handleImageCut'] = (type) => {
    imageCutData.type = type
    mapTools.drawTools.addRectangle()
    console.log('handleImageCut')
  }
  const addMarker: ImageCut['addMarker'] = (polygon) => {
    const bbox = turf.bbox(polygon)
    const bbox3857: any = getTransformExtentData(`EPSG:4326`, bbox, 'EPSG:3857')
    imageCutData.bbox = bbox3857
    const center: any = polygon.geometry.coordinates[0][polygon.geometry.coordinates[0].length - 3]
    imageCutData.isElement = true
    imageCutData.markers = new Marker({
      element: BtnRef.value,
      offset: [-66, 20],
    })
      .setLngLat(center)
      .addTo(map.value)
  }

  const clearedImageCut: ImageCut['clearedImageCut'] = () => {
    mapTools.handleClearTools()
    imageCutData.type = ''
    imageCutData.bbox = []
    imageCutData.markers?.remove()
    imageCutData.isElement = false
  }

  return { imageCutData, handleImageCut, addMarker, clearedImageCut, BtnRef }
}
