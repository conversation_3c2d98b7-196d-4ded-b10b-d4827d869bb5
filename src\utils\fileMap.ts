import unknown from '@/assets/fileIconImg/file_unknown.png'
import dir from '@/assets/fileIconImg/file_dir.png'
import avi from '@/assets/fileIconImg/file_avi.png'
import bat from '@/assets/fileIconImg/file_powershell.png'
import c from '@/assets/fileIconImg/file_c.png'
import cAdd from '@/assets/fileIconImg/file_cAdd.png'
import cW from '@/assets/fileIconImg/file_cW.png'
import css from '@/assets/fileIconImg/file_css.png'
import go from '@/assets/fileIconImg/file_go.png'
import python from '@/assets/fileIconImg/file_python.png'
import stylus from '@/assets/fileIconImg/file_stylus.png'
import less from '@/assets/fileIconImg/file_less.png'
import nginx from '@/assets/fileIconImg/file_nginx.png'
import objective_c from '@/assets/fileIconImg/file_objective_c.png'
import scss from '@/assets/fileIconImg/file_scss.png'
import sass from '@/assets/fileIconImg/file_sass.png'
import csv from '@/assets/fileIconImg/file_csv.png'
import dmg from '@/assets/fileIconImg/file_dmg.png'
import word from '@/assets/fileIconImg/file_word.svg'
import exe from '@/assets/fileIconImg/file_exe.png'
import html from '@/assets/fileIconImg/file_html.png'
import jar from '@/assets/fileIconImg/file_jar.png'
import java from '@/assets/fileIconImg/file_java.png'
import js from '@/assets/fileIconImg/file_js.png'
import json from '@/assets/fileIconImg/file_json.png'
import jsp from '@/assets/fileIconImg/file_jsp.png'
import kotlin from '@/assets/fileIconImg/file_kotlin.png'
import music from '@/assets/fileIconImg/file_music.png'
import flac from '@/assets/fileIconImg/file_flac.svg'
import oa from '@/assets/fileIconImg/file_oa.png'
import open from '@/assets/fileIconImg/file_open.png'
import pdf from '@/assets/fileIconImg/file_pdf.png'
import php from '@/assets/fileIconImg/file_php.png'
import ppt from '@/assets/fileIconImg/file_ppt.svg'
import properties from '@/assets/fileIconImg/file_properties.png'
import r from '@/assets/fileIconImg/file_r.png'
import rar from '@/assets/fileIconImg/file_rar.png'
import rust from '@/assets/fileIconImg/file_rust.png'
import rtf from '@/assets/fileIconImg/file_rtf.png'
import shell from '@/assets/fileIconImg/file_shell.png'
import sql from '@/assets/fileIconImg/file_sql.png'
import svg from '@/assets/fileIconImg/file_svg.png'
import swift from '@/assets/fileIconImg/file_swift.png'
import typescript from '@/assets/fileIconImg/file_typescript.png'
import txt from '@/assets/fileIconImg/file_txt.png'
import vue from '@/assets/fileIconImg/file_vue.png'
import excel from '@/assets/fileIconImg/file_excel.svg'
import xml from '@/assets/fileIconImg/file_xml.png'
import zip from '@/assets/fileIconImg/file_zip.png'
import z from '@/assets/fileIconImg/file_7z.svg'
import tar from '@/assets/fileIconImg/file_tar.svg'
import markdown from '@/assets/fileIconImg/file_markdown.png'
import yaml from '@/assets/fileIconImg/file_yaml.png'
import shp from '@/assets/fileIconImg/file_shp.png'
import tif from '@/assets/fileIconImg/file_tif.png'

/**
 * 未知文件类型图标
 */
export const unknownImg: any = unknown
// 文件类型图标map集合
export const fileImgMap: Map<any, any> = new Map([
  ['dir', dir],
  ['avi', avi],
  ['bat', bat],
  ['c', c],
  ['c++', cAdd],
  ['c#', cW],
  ['css', css],
  ['go', go],
  ['py', python],
  ['styl', stylus],
  ['less', less],
  ['conf', nginx],
  ['m', objective_c],
  ['scss', scss],
  ['sass', sass],
  ['csv', csv],
  ['dmg', dmg],
  ['doc', word],
  ['docx', word],
  ['exe', exe],
  ['html', html],
  ['jar', jar],
  ['java', java],
  ['js', js],
  ['json', json],
  ['jsp', jsp],
  ['kt', kotlin],
  ['mp3', music],
  ['flac', flac],
  ['oa', oa],
  ['open', open],
  ['pdf', pdf],
  ['php', php],
  ['ppt', ppt],
  ['pptx', ppt],
  ['properties', properties],
  ['r', r],
  ['rar', rar],
  ['rs', rust],
  ['rtf', rtf],
  ['sh', shell],
  ['sql', sql],
  ['svg', svg],
  ['swift', swift],
  ['ts', typescript],
  ['txt', txt],
  ['vue', vue],
  ['xls', excel],
  ['xlsx', excel],
  ['xml', xml],
  ['zip', zip],
  ['7z', z],
  ['tar', tar],
  ['md', markdown],
  ['markdown', markdown],
  ['yaml', yaml],
  ['yml', yaml],
  ['shp', shp],
  ['tif', tif],
  ['tiff', tif],
])

/**
 * language：解析语言,
 * mime: codemirror 解析模式
 */
export const fileSuffixCodeModeMap: string[] = [
  'c',
  'c++',
  'c#',
  'm',
  'go',
  'kt',
  'css',
  'less',
  'php',
  'bat',
  'py',
  'properties',
  'R',
  'rs',
  'scss',
  'sass',
  'sh',
  'styl',
  'xml',
  'html',
  'http',
  'conf',
  'java',
  'js',
  'ts',
  'json',
  'jsp',
  'txt',
  'vue',
  'sql',
  'swift',
  'yml',
  'yaml',
]

// markdown 文件后缀
export const markdownFileType = ['markdown', 'md']

// 数据目录音频、视频、图片可群组预览
export const previewList: any[] = ['43', '44', '45']

// gis数据预览
export const gisPreview: any[] = ['13', '14', '15', '16', '22', '23']

// cesium地图预览
export const cesiumPreview: any[] = ['32', '33', '34', '35']

// office文件类型预览
export const officeFileType = ['ppt', 'pptx', 'doc', 'docx', 'xls', 'xlsx', 'pdf', 'csv']

// 三维实景
export const threePreview: any[] = ['gltf', 'glb', 'dae', 'fbx', 'obj', '3ds', 'json', 'ply']

//切片处理判断条件
export const sliceFileType = ['tif', 'tiff', 'dem', 'las', 'shp', 'obj', 'fbx', 'clm', 'osgb']
