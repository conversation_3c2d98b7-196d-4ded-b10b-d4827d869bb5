import { RouterView } from 'vue-router'
import DataLake from '@/pages/DataCatalog/DataLake/index.vue'
import TabLayout from '@/components/TabLayout/index.vue'

const routerPage = {
  // 首页
  home: () => import('@/pages/Home/index.vue'),
  // 数据集成
  dataIntegration: () => import('@/pages/DataIntegration/index.vue'),
  // 适配器
  adapter: () => import('@/pages/DataIntegration/Adapter/index.vue'),
  // 数据源
  dataSource: () => import('@/pages/DataIntegration/DataSource/index.vue'),
  // 数据源操作
  dataSourceAddEditDetail: () =>
    import('@/pages/DataIntegration/DataSource/AddEditDetail/index.vue'),
  // 数据源数据库详情
  sourceDataDetail: () => import('@/pages/DataIntegration/DataSource/SourceDataDetail/index.vue'),
  // 数据源表详情
  sourceFileDetail: () => import('@/pages/DataIntegration/DataSource/SourceFileDetail/index.vue'),
  // 数据源表详情
  sourceEsDetail: () => import('@/pages/DataIntegration/DataSource/SourceEsDetail/index.vue'),
  // 数据源表详情
  sourceDBDetail: () => import('@/pages/DataIntegration/DataSource/SourceDBDetail/index.vue'),
  // 数据采集
  dataGather: () => import('@/pages/DataIntegration/DataGather/index.vue'),
  addGather: () => import('@/pages/DataIntegration/DataGather/AddGather/index.vue'),
  // 数据采集详情
  dataGatherDetail: () => import('@/pages/DataIntegration/DataGather/DataDetail/index.vue'),
  // 数据采集编辑详情
  gatherDetail: () => import('@/pages/DataIntegration/DataGather/GatherDetail/index.vue'),
  // 流数据采集
  streamData: () => import('@/pages/DataIntegration/StreamData/index.vue'),
  // 数据目录
  dataCatalog: RouterView,
  // 数据仓
  dataHouse: () => import('@/pages/DataCatalog/DataHouse/index.vue'),
  // 数据湖
  dataLake: DataLake,
  // 目录配置
  catalogConfig: () => import('@/pages/DataCatalog/CatalogConfig/index.vue'),
  // 目录版本
  catalogVersion: () => import('@/pages/DataCatalog/CatalogConfig/CatalogVersion/index.vue'),
  // 编辑目录
  editCatalog: () => import('@/pages/DataCatalog/CatalogConfig/EditCatalog/index.vue'),
  // 在线模型
  onlineModel: () => import('@/pages/OnlineModel/index.vue'),
  // 模型库
  modelBase: () => import('@/pages/OnlineModel/ModelBase/index.vue'),
  // 在线建模
  createModel: () => import('@/pages/OnlineModel/CreateModel/index.vue'),
  // 任务管理
  taskManage: () => import('@/pages/OnlineModel/TaskManage/index.vue'),
  // 任务详情
  taskManageDetail: () => import('@/pages/OnlineModel/TaskManage/TaskManageDetail/index.vue'),
  //自定义算子
  customModel: () => import('@/pages/OnlineModel/CustomModel/index.vue'),
  // 时序场景
  sequentialScene: () => import('@/pages/SequentialScene/index.vue'),
  // 场景列表
  sceneList: () => import('@/pages/SequentialScene/SceneList/index.vue'),
  // 场景构建
  sceneStructure: () => import('@/pages/SequentialScene/SceneStructure/index.vue'),
  // 生成视频
  generateVideo: () => import('@/pages/SequentialScene/SceneList/GenerateVideo/index.vue'),
  // 数据安全
  dataSecurity: () => import('@/pages/DataSecurity/index.vue'),
  // 灾备管理
  disasterRecovery: () => import('@/pages/DataSecurity/DisasterRecovery/index.vue'),
  // 数据加密脱密
  dataEncrypte: () => import('@/pages/DataSecurity/DataEncrypte/index.vue'),
  // 数据查询D
  dataSearch: () => import('@/pages/DataSearch/index.vue'),
  // 数据治理
  dataGovernance: () => import('@/pages/DataGovernance/index.vue'),
  // 数据质量
  dataQuality: RouterView,
  // 质量监控
  qualityMonitor: () => import('@/pages/DataGovernance/DataQuality/QualityMonitor/index.vue'),
  // 质量规则
  qualityRules: () => import('@/pages/DataGovernance/DataQuality/QualityRules/index.vue'),
  // 质量规则编辑
  addEditRules: () =>
    import('@/pages/DataGovernance/DataQuality/QualityRules/AddEditRules/index.vue'),
  // 质量模型
  qualityModel: () => import('@/pages/DataGovernance/DataQuality/QualityModel/index.vue'),
  // 自动治理规则
  governanceRules: () => import('@/pages/DataGovernance/DataQuality/GovernanceRules/index.vue'),
  // 数据标准
  dataStandard: RouterView,
  //健康度评分标准
  scoreCriteria: () => import('@/pages/DataGovernance/DataStandard/ScoreCriteria/index.vue'),
  // 数据治理
  dataAdminister: RouterView,
  // 治理任务
  governanceTask: () => import('@/pages/DataGovernance/DataAdminister/GovernanceTask/index.vue'),
  // 设置
  setting: () => import('@/pages/Setting/index.vue'),
  // 审核管理
  audit: () => import('@/pages/Audit/index.vue'),
  // 采集审核
  gatherAudit: TabLayout,
  // 我提交的
  submitGather: () => import('@/pages/Audit/GatherAudit/SubmitGather/index.vue'),
  // 审批管理
  approval: () => import('@/pages/Audit/GatherAudit/Approval/index.vue'),
  // 审批详情
  approvalInfo: () => import('@/pages/Audit/GatherAudit/Approval/ApprovalInfo/index.vue'),
  // 白名单
  whiteList: () => import('@/pages/Audit/GatherAudit/WhiteList/index.vue'),
  // 目录管理
  dirAudit: TabLayout,
  // 我提交的
  submitDir: () => import('@/pages/Audit/DirAudit/SubmitDir/index.vue'),
  // 已审核
  reviewedDir: () => import('@/pages/Audit/DirAudit/ReviewedDir/index.vue'),
  // 目录详情
  dirInfo: () => import('@/pages/Audit/DirAudit/DirInfo/index.vue'),
}

export default routerPage
