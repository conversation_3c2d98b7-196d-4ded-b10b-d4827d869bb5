<template>
  <ElScrollbar :height="500">
    <div class="fileFiler">
      <div class="file-type">数据类型</div>
      <div class="file-list">
        <div class="list-item" v-for="(p, i) in globalData.catalogMenuList" :key="i">
          <div class="item-title">{{ p.fileTypeName }}</div>
          <ul v-if="p.childVOList && p.childVOList.length > 0" style="flex: 1">
            <li class="item-list" v-for="(item, i) in p.childVOList" :key="i">
              <span style="margin-right: 10px">{{ item.fileTypeName }}:</span
              >{{ item.notes || '--' }}
            </li>
          </ul>
          <div v-else class="list-juge">
            <span style="margin-right: 10px">{{ p.fileTypeName }}:</span>{{ p.notes || '--' }}
          </div>
        </div>
      </div>
    </div>
  </ElScrollbar>
</template>

<script lang="ts" setup>
import useGlobalData from '@/store/useGlobalData'
import { ElScrollbar } from 'element-plus'

const globalData = useGlobalData()
</script>

<style lang="less" scoped>
.fileFiler {
  width: 100%;
  .file-type {
    text-align: center;
    margin-bottom: 10px;
  }
  .file-list {
    border: 1px solid #dcdfe6;
    border-bottom: none;
    .list-item {
      display: flex;
      width: 100%;
      .item-title {
        width: 100px;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 10px 0px;
        border-bottom: 1px solid #dcdfe6;
      }
      .item-list {
        border-left: 1px solid #dcdfe6;
        border-bottom: 1px solid #dcdfe6;
        padding: 10px 10px 10px 20px;
      }
      .list-juge {
        border-left: 1px solid #dcdfe6;
        border-bottom: 1px solid #dcdfe6;
        flex: 1;
        padding: 10px 0px 10px 20px;
      }
    }
  }
}
</style>
