<template>
  <ElDialog
    v-model="dialogFormVisible"
    top="10vh"
    width="500px"
    :title="title"
    :close-on-click-modal="false"
    @close="handleDialogClose"
  >
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="auto">
      <el-form-item label="目录名称" prop="catalogAlias">
        <el-input v-model="formData.catalogAlias" placeholder="请输入目录名称" />
      </el-form-item>
      <ElFormItem v-if="[1].includes(optType)" label="目录分类" prop="ptid">
        <ElTreeSelect
          v-model="formData.ptid"
          :data="dataSource"
          check-strictly
          :props="{
            label: 'catalogAlias',
            value: 'tid',
            children: 'childVOList',
            disabled: 'disabled',
          }"
          value-key="tid"
          style="width: 100%"
        />
      </ElFormItem>
      <el-form-item v-if="[1].includes(props.dataType)" label="目录分类" prop="bsType">
        <ElRadioGroup v-model="formData.bsType" :disabled="[1].includes(optType)">
          <ElRadio label="菜单" :value="1"></ElRadio>
          <ElRadio label="数据库" :value="2"></ElRadio>
        </ElRadioGroup>
      </el-form-item>
      <el-form-item label="描述信息" prop="catalogDesc">
        <el-input
          v-model="formData.catalogDesc"
          :rows="3"
          type="textarea"
          placeholder="请填写分类的描述信息"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="setDialogFormVisible(false)">取 消</el-button>
        <el-button type="primary" :loading @click="handleSubmit">确 定</el-button>
      </span>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
import { configAddApi, configModifyByIdApi } from '@/api/catalog_config'
import useDialogForm from '@/hooks/useDialogForm'
import {
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElMessage,
  ElRadio,
  ElRadioGroup,
} from 'element-plus'
import { computed, ref } from 'vue'

const props = defineProps<{ dataType: number; treeData: any[] }>()
const dataSource = computed<any[]>(() => [
  {
    tid: '-1',
    childVOList: props.treeData,
    catalogAlias: '全部',
  },
])

const { dialogFormVisible, handleDialogClose, setDialogFormVisible, formRef, validateForm } =
  useDialogForm()
const formData = ref<any>({ bsType: 1 })
const optType = ref<number>(0)
const rules = ref<any>({
  catalogAlias: [{ required: true, message: '目录名称必填！', trigger: 'blur' }],
})

const title = computed<string>(() =>
  [0].includes(optType.value)
    ? '新建一级目录'
    : [1].includes(optType.value)
      ? '修改目录'
      : '添加子目录',
)

// type: 0 新增 1 修改 2添加子节点
const handleOpen = (row: any, type: number) => {
  optType.value = type
  if (row && type === 1) {
    formData.value = { ...row }
  }
  if (row && type === 2) {
    formData.value.tid = row.tid
  }
  setDialogFormVisible(true)
}

const loading = ref<boolean>(false)
const handleSubmit = async () => {
  try {
    loading.value = true
    await validateForm()
    const { catalogAlias, catalogDesc, bsType, tid } = formData.value
    const ptid = [0].includes(optType.value)
      ? -1
      : [1].includes(optType.value)
        ? formData.value.ptid
        : formData.value.tid
    const { message, status } = [1].includes(optType.value)
      ? await configModifyByIdApi({
          catalogAlias,
          catalogDesc,
          dataType: props.dataType,
          bsType,
          ptid,
          tid,
        })
      : await configAddApi({ catalogAlias, catalogDesc, dataType: props.dataType, bsType, ptid })
    if ([200].includes(status)) {
      ElMessage.success(message)
      setDialogFormVisible(false)
      emit('handleRefresh')
    } else {
      ElMessage.error(message)
    }
    loading.value = false
  } catch (error) {
    loading.value = false
    console.error(error)
  }
}

defineExpose({ handleOpen })

const emit = defineEmits<{
  (e: 'handleRefresh'): void
}>()
</script>

<style lang="less" scoped></style>
