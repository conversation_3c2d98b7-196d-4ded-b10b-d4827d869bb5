<template>
  <ElDialog
    title="治理详情"
    top="10vh"
    :close-on-click-modal="false"
    v-model="dialogFormVisible"
    @close="handleDialogClose"
    @opened="uploadRef.clearFiles()"
  >
    <ElScrollbar max-height="50vh">
      <ElForm ref="formRef" :model="formData" label-width="100px">
        <ElFormItem label="文件名称">
          <span>{{ getFileNameComplete(formData) }}</span>
        </ElFormItem>
        <ElFormItem label="健康度">
          <span :style="{ color: healthColor(formData.scoreLevel) }">{{
            formData.scoreLevel || '--'
          }}</span>
        </ElFormItem>
        <ElFormItem label="得分">
          <span :style="{ color: healthColor(formData.scoreLevel) }">{{
            `${formData.scoreValue}分`
          }}</span>
        </ElFormItem>
        <ElFormItem label="问题及建议">
          <ElTable
            :data="formData.tableData"
            border
            size="small"
            :max-height="150"
            style="width: 100%"
          >
            <ElTableColumn label="当前问题" prop="problem" />
            <ElTableColumn label="调整建议" prop="suggest" />
          </ElTable>
        </ElFormItem>
        <ElFormItem label="治理类型" prop="autoGovern">
          <el-radio-group v-model="formData.autoGovern">
            <el-radio label="自动治理" value="1"></el-radio>
            <el-radio label="手动治理" value="0"></el-radio>
          </el-radio-group>
        </ElFormItem>
        <ElFormItem v-show="['1'].includes(formData.autoGovern)">
          <p style="color: rgb(245, 108, 108)">
            注意：系统将按照如下治理规则进行自动治理，请确认是否确定进行自动治理？
          </p>
          <ElTable
            :data="formData.listData"
            border
            size="small"
            :max-height="150"
            style="width: 100%"
          >
            <ElTableColumn label="当前问题" prop="problem" />
            <ElTableColumn label="调整建议" prop="autoRuleContent" />
          </ElTable>
        </ElFormItem>
        <ElFormItem v-show="['0'].includes(formData.autoGovern)">
          <p style="color: rgb(245, 108, 108); width: 100%">提示：请手动调整数据并上传最新数据</p>
          <div style="margin-top: 10px; width: 100%">
            <ElUpload
              ref="uploadRef"
              :action="uploadRepairFileApi()"
              :limit="1"
              :on-exceed="handleExceed"
              :auto-upload="false"
              :headers="{
                token: permission.token,
              }"
              :data="setData"
              :on-success="handleSuccessUpload"
            >
              <template #trigger>
                <ElButton type="primary">选择文件</ElButton>
              </template>
              <span style="margin-left: 20px">请选择调整后的文件</span>
            </ElUpload>
          </div>
        </ElFormItem>
      </ElForm>
    </ElScrollbar>

    <template #footer>
      <ElButton @click="setDialogFormVisible(false)">取消</ElButton>
      <ElButton type="primary" @click="handleSubmit" :disabled="isDisabled">确定</ElButton>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
import {
  ElRadio,
  ElRadioGroup,
  ElButton,
  ElDialog,
  ElForm,
  ElFormItem,
  ElMessage,
  ElScrollbar,
  ElTable,
  ElTableColumn,
  ElUpload,
  UploadProps,
  UploadRawFile,
  genFileId,
} from 'element-plus'
import useDialogForm from '@/hooks/useDialogForm'
import { ref } from 'vue'
import { getFileNameComplete } from '@/utils/fileUtils'
import { healthColor } from '@/utils'
import { qaDetailListApi, autoRepairApi, uploadRepairFileApi } from '@/api/data_catalog'
import useUserInfo from '@/store/useUserInfo'
import SparkMD5 from 'spark-md5'

const permission = useUserInfo()

const formData = ref<any>({ autoGovern: '1' })
const { dialogFormVisible, handleDialogClose, setDialogFormVisible, formRef } = useDialogForm()

const handleOpen = async (row: any) => {
  try {
    const { data, message, status } = await qaDetailListApi({ userFileId: row.tid })
    if ([200].includes(status)) {
      formData.value = {
        autoGovern: '1',
        ...row,
        tableData: data,
        listData: (data as any[]).filter((item: any) => !!item.autoRuleName),
      }
      setDialogFormVisible(true)
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}

const uploadRef = ref<any>()
const handleExceed: UploadProps['onExceed'] = (files: any[]) => {
  uploadRef.value!.clearFiles()
  const file = files[0] as UploadRawFile
  file.uid = genFileId()
  uploadRef.value!.handleStart(file)
}
const getFileMd5 = (file: any) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onloadend = () => {
      const arrayBuffer: any = reader.result
      // 生成MD5值
      const spark = new SparkMD5.ArrayBuffer()
      spark.append(arrayBuffer)
      const md5Hash = spark.end()
      resolve(md5Hash)
    }
    reader.onerror = () => {
      reject(new Error('无法读取文件对象'))
    }
    reader.readAsArrayBuffer(file)
  })
}
const setData = async (rawFile: UploadRawFile) => {
  const identifier = await getFileMd5(rawFile)
  return {
    identifier,
    fileName: rawFile.name,
    totalSize: rawFile.size,
    currentChunkSize: rawFile.size,
    chunkSize: rawFile.size,
    totalChunks: 1,
    chunkNumber: 1,
    userFileId: formData.value.tid,
  }
}

const handleSuccessUpload = (response: any) => {
  if ([200].includes(response.status) && [1].includes(response.data.uploadCode)) {
    formData.value.userFileId = response.data.userFileId
    ElMessage.success('手动治理操作成功！')
    setDialogFormVisible(false)
  } else {
    uploadRef.value?.clearFiles()
    ElMessage.error(response.message || '上传失败！')
  }
  isDisabled.value = false
}

const isDisabled = ref<boolean>(false)
const handleSubmit = async () => {
  if (['1'].includes(formData.value.autoGovern) && !formData.value.listData.length) {
    ElMessage.warning('当前文件不支持自动治理！')
    return
  }
  isDisabled.value = true
  if (['1'].includes(formData.value.autoGovern)) {
    const { status, message } = await autoRepairApi({ userFileId: formData.value.tid })
    if ([200].includes(status)) {
      ElMessage.success(message || '操作成功')
      setDialogFormVisible(false)
    } else {
      ElMessage.error(message)
    }
    isDisabled.value = false
    try {
    } catch (error) {
      isDisabled.value = false
    }
  } else {
    uploadRef.value!.submit()
  }
}

defineExpose({ handleOpen })
</script>

<style scoped lang="less">
.mian-content {
  box-sizing: border-box;
}
</style>
