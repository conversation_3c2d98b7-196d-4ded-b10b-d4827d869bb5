<template>
  <div class="mainLayout">
    <header class="header">
      <slot name="header"></slot>
    </header>
    <main class="main">
      <slot></slot>
    </main>
    <footer class="footer">
      <slot name="footer"></slot>
    </footer>
  </div>
</template>

<style scoped lang="less">
.mainLayout {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  .header {
    padding: 15px;
    background-color: @withe;
    // box-shadow: 0px 8px 8px 8px rgba(0, 0, 0, 0.05);
  }
  .main {
    flex: 1;
    margin-top: 20px;
    background-color: @withe;
    box-shadow: 0px 8px 8px 8px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    box-sizing: border-box;
    padding: 15px;
  }
  .footer {
    background-color: @withe;
    box-sizing: border-box;
    padding: 0px 15px 15px 15px;
    display: flex;
    justify-content: flex-end;
  }
}
</style>
