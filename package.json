{"name": "my-vue-app", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite --mode dev", "build:dev": "vue-tsc -b && vite build --mode dev", "build:prod": "vue-tsc -b && vite build --mode production", "preview": "vite preview"}, "dependencies": {"@codemirror/lang-javascript": "^6.2.2", "@codemirror/lang-sql": "^6.7.1", "@mapbox/mapbox-gl-draw": "^1.4.3", "@turf/circle": "^7.1.0", "@turf/distance": "^7.1.0", "@turf/helpers": "^7.1.0", "@turf/turf": "^7.1.0", "axios": "^1.9.0", "cesium": "^1.121.1", "clipboard-copy": "^4.0.1", "codemirror": "^6.0.1", "dayjs": "^1.11.13", "echarts": "^5.5.1", "element-plus": "^2.8.3", "file-saver": "^2.0.5", "mapbox-gl": "^3.6.0", "maplibre-gl": "^5.4.0", "maplibre-grid": "^1.0.0", "mavon-editor": "^2.10.4", "nprogress": "^0.2.0", "ol": "^10.2.0", "pinia": "^2.2.2", "pinia-plugin-persistedstate": "^4.0.2", "proj4": "^2.12.1", "recordrtc": "^5.6.2", "relation-graph-vue3": "^2.2.6", "screenfull": "^6.0.2", "spark-md5": "^3.0.2", "three": "^0.168.0", "uuid": "^10.0.0", "video.js": "^8.17.4", "vue": "^3.5.8", "vue-codemirror": "^6.1.1", "vue-router": "^4.4.5", "x2js": "^3.4.4", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/js": "^9.11.0", "@types/file-saver": "^2.0.7", "@types/mapbox__mapbox-gl-draw": "^1.4.8", "@types/mapbox-gl": "^3.4.0", "@types/node": "^22.5.5", "@types/nprogress": "^0.2.3", "@types/proj4": "^2.5.5", "@types/recordrtc": "^5.6.14", "@types/spark-md5": "^3.0.4", "@types/three": "^0.168.0", "@types/uuid": "^10.0.0", "@vitejs/plugin-legacy": "^5.4.2", "@vitejs/plugin-vue": "^5.1.4", "eslint": "^9.11.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-vue": "^9.28.0", "globals": "^15.9.0", "less": "^4.2.0", "prettier": "^3.3.3", "typescript": "^5.5.3", "typescript-eslint": "^8.6.0", "unplugin-auto-import": "^0.18.3", "unplugin-vue-components": "^0.27.4", "vite": "^5.4.7", "vite-plugin-cesium": "^1.2.23", "vite-plugin-compression": "^0.5.1", "vite-plugin-svg-icons": "^2.0.1", "vue-tsc": "^2.1.6"}}