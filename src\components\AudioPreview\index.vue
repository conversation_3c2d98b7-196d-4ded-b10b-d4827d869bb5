<template>
  <transition name="fade">
    <div v-show="showViewer" class="audio-preview-wrapper">
      <img class="audio-background" :src="musicImgUrl" alt="背景图" />
      <!-- 右上角操作 -->
      <div class="operate-box">
        <div></div>
        <div class="tip-right">
          <SvgIcon name="closeBold" @click="handleClose" class="close-icon" />
        </div>
      </div>
      <audio
        ref="audioRef"
        @loadedmetadata="handleLoadedmetadata"
        @timeupdate="handleTimeUpdate"
        @ended="handleAudioEnd"
        :src="activeFileObj.url"
        controls
        style="display: none"
      ></audio>
      <!-- 音频列表 -->
      <div class="audio-list-wrapper">
        <ul class="audio-list">
          <li class="audio-list-header">
            <span class="name">音频名称</span>
            <span class="audio-size">大小</span>
          </li>
          <ul class="audio-list-body">
            <li
              class="audio-item"
              v-for="(p, i) in audioList"
              :key="i"
              :class="{ active: [i].includes(activeIndex) }"
              :title="audioObj.isPlay ? '暂停' : '播放'"
              @click="handleSelectAudio(i)"
            >
              <span class="name">
                <span class="sequence" v-show="![i].includes(activeIndex)">
                  {{ i + 1 }}
                </span>
                <img
                  class="wave"
                  :src="activePlayIcon"
                  alt="波浪动图"
                  v-show="[i].includes(activeIndex) && audioObj.isPlay"
                />
                <SvgIcon
                  class="no-wave"
                  name="histogram"
                  v-show="[i].includes(activeIndex) && !audioObj.isPlay"
                />
                <span class="text">{{ getFileNameComplete(p) }}</span>
              </span>
              <SvgIcon
                class="play-icon"
                name="videoPlay"
                v-show="[i].includes(activeIndex) && !audioObj.isPlay"
              />
              <SvgIcon
                class="play-icon"
                name="videoPause"
                v-show="[i].includes(activeIndex) && audioObj.isPlay"
              />
              <SvgIcon class="play-icon" name="download" @click.stop="handleDownload" />
              <span class="audio-size">{{ calculateFileSize(Number(p.fileSize)) }}</span>
            </li>
          </ul>
        </ul>
      </div>
      <!-- 底部音乐控件 -->
      <div class="control-wrapper">
        <div class="control-left">
          <SvgIcon class="operate-icon" name="retreat" @click="handleChangeAudio('pre')" />
          <SvgIcon
            v-if="!audioObj.isPlay"
            @click="handleClickPlayIcon"
            class="operate-icon play-icon"
            name="videoPlay"
          />
          <SvgIcon
            v-if="audioObj.isPlay"
            @click="handleClickPauseIcon"
            class="operate-icon pause-icon"
            name="videoPause"
          />
          <SvgIcon class="operate-icon" name="forward" @click="handleChangeAudio('next')" />
          <el-slider
            class="progress-bar"
            v-model="audioObj.currentTime"
            :step="progressStep"
            :max="audioObj.duration"
            @mousedown.native="audioObj.isDrop = true"
            @mouseup.native="audioObj.isDrop = false"
            :format-tooltip="(val: number) => transferSecondsToTime(val)"
            @change="handleChangeProgress"
          ></el-slider>
          <span
            >{{ transferSecondsToTime(audioObj.currentTime) }} /
            {{ transferSecondsToTime(audioObj.duration) }}
          </span>
        </div>
        <div class="control-right">
          <SvgIcon
            class="operate-icon"
            @click="handleChangeCycleType"
            :name="cycleTypeMap[audioObj.cycleType].icon"
          />
          <SvgIcon class="operate-icon" @click="handleChangeAudio('next')" name="download" />
          <SvgIcon
            class="operate-icon"
            @click="handleClickVolumeIcon"
            :name="[0].includes(audioObj.volume) ? 'mute' : 'volume'"
          />
          <el-slider
            class="volume-bar"
            v-model="audioObj.volume"
            :step="0.01"
            :max="1"
            :format-tooltip="(val: number) => Math.floor(val * 100)"
            @input="handleChangeVolumeBar"
          ></el-slider>
        </div>
      </div>
    </div>
  </transition>
</template>

<script setup lang="ts">
import { computed, nextTick, reactive, ref } from 'vue'
import { downloadFileApi } from '@/api/common'
import activePlayIcon from '@/assets/commonImg/wave.gif'
import { fileImgMap } from '@/utils/fileMap'
import { transferSecondsToTime } from '@/utils'
import { calculateFileSize, getFileNameComplete, downloadFile } from '@/utils/fileUtils'
import useUserInfo from '@/store/useUserInfo'

interface AudioObj {
  volume: number
  currentTime: number
  isPlay: boolean
  isDrop: boolean
  cycleType: number
  duration: number
}

const userInfo = useUserInfo()

const audioRef = ref<any>()

const audioObj = reactive<AudioObj>({
  volume: 0, // 音量
  currentTime: 0, //当前时间
  isPlay: false, // 是否播放
  isDrop: false, //是否正在拖拽播放进度滑块
  cycleType: 1, //  音频播放的循环模式
  duration: 0,
})

// 播放进度条步长
const progressStep = computed<number>(() => (audioRef.value?.duration || 0) / 100)

// 点击音量图标
const handleClickVolumeIcon = () => {
  audioObj.volume = [0].includes(audioObj.volume) ? 0.5 : 0
  handleChangeVolumeBar(audioObj.volume)
}

// 音量滑块改变时触发
const handleChangeVolumeBar = (volume: number) => {
  audioRef.value.volume = Number(volume.toFixed(1))
}

// 切换循环播放类型
const handleChangeCycleType = () => {
  if (audioObj.cycleType === 3) {
    audioObj.cycleType = 1
  } else if (audioObj.cycleType >= 1) {
    audioObj.cycleType++
  }
}

// 拖动播放进度滑块触发
const handleChangeProgress = (progress: number) => {
  audioRef.value.currentTime = progress
  audioObj.isDrop = false
}

// 选择播放音乐
const handleSelectAudio = (index: number) => {
  if (activeIndex.value === index) {
    if (audioObj.isPlay) {
      handleClickPauseIcon()
      return
    }
    handleClickPlayIcon()
  } else {
    activeIndex.value = index
  }
}

// 音乐播放结束处理
const handleAudioEnd = () => {
  if (audioList.value.length === 1 || [2].includes(audioObj.cycleType)) {
    audioRef.value.currentTime = 0
    handleClickPlayIcon()
    return
  }
  handleClickPauseIcon()
  if ([3].includes(audioObj.cycleType)) {
    let index = 0
    do {
      index = Math.floor(Math.random() * (audioList.value.length - 1)) + 1
    } while (activeIndex.value === index)
    return
  }
  handleChangeAudio('next')
}

/**
 *  音乐上下播放按钮
 *  @param {string} type pre - 上一首 | next - 下一首
 */
const handleChangeAudio = (type: string) => {
  if (['pre'].includes(type)) {
    if (activeIndex.value === 0) {
      activeIndex.value = audioList.value.length - 1
      return
    }
    activeIndex.value--
  } else {
    if (activeIndex.value === audioList.value.length - 1) {
      activeIndex.value = 0
      return
    }
    activeIndex.value++
  }
}

// 当前播放时间改变时触发
const handleTimeUpdate = (event: any) => {
  // 如果正在拖拽进度滑块，函数结束，不计算当前时间
  if (audioObj.isDrop) return
  audioObj.currentTime = event.target.currentTime
}

// 获取播放器参数
const handleLoadedmetadata = (event: any) => {
  const audioDom: any = event.target
  audioObj.volume = audioDom.volume || 0.5
  audioObj.currentTime = audioDom.currentTime
  audioObj.duration = audioDom.duration
  handleClickPlayIcon()
}

// 开始播放音频
const handleClickPlayIcon = () => {
  audioObj.isPlay = true
  audioRef.value?.play()
}

// 暂停播放
const handleClickPauseIcon = () => {
  audioObj.isPlay = false
  audioRef.value?.pause()
}

// 下载音乐
const handleDownload = () => {
  downloadFile(`${downloadFileApi()}?tid=${activeFileObj.value.tid}&token=${userInfo.token}`)
}

// 音乐列表
const audioList = ref<any[]>([])

// 音乐列表选中的音乐
const activeIndex = ref<number>(0)

// 当前播放的音乐
const activeFileObj = computed<any>(() =>
  audioList.value.length ? audioList.value[activeIndex.value] : {},
)

// 音乐背景图
const musicImgUrl = computed<any>(() => fileImgMap.get('mp3'))

// 是否显示音频播放页面
const showViewer = ref<boolean>(false)

// 打开预览页面
const handleOpenPreview = (urlList: any[], initialIndex: number) => {
  showViewer.value = true
  nextTick(() => {
    audioList.value = urlList
    activeIndex.value = initialIndex
  })
}

// 关闭预览页面
const handleClose = () => {
  showViewer.value = false
  handleClickPauseIcon()
  audioObj.cycleType = 1
  audioList.value = []
}

//音频循环模式和图标对应的 Map
const cycleTypeMap: any = {
  1: {
    // 列表循环
    icon: 'circulate',
  },
  2: {
    // 单曲循环
    icon: 'singleCycle',
  },
  3: {
    // 随机播放
    icon: 'random',
  },
}

defineExpose({ handleOpenPreview })
</script>

<style scoped lang="less">
.audio-preview-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  width: 100vw;
  z-index: 10;
  color: #dcdfe6;
  background-color: #303133;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  .audio-background {
    position: fixed;
    top: -50%;
    left: 0;
    width: 100vw;
    height: auto;
    filter: blur(65px);
    opacity: 0.6;
    z-index: -1;
  }
  .operate-box {
    padding: 0 48px;
    width: 100%;
    height: 48px;
    line-height: 48px;
    color: @withe;
    font-size: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-sizing: border-box;
    .tip-right {
      display: flex;
      height: 100%;
      align-items: center;
      .close-icon {
        font-size: 24px;
        cursor: pointer;
      }
    }
  }
  .audio-list-wrapper {
    flex: 1;
    box-sizing: border-box;
    padding: 0 10%;
    display: flex;
    .audio-list {
      flex: 1;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      .audio-list-header,
      .audio-item {
        border-radius: 8px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 56px;
        cursor: pointer;
        padding: 0 16px;
        box-sizing: border-box;
        &:hover {
          background: rgba(0, 0, 0, 0.1);
        }
        &.active {
          background: rgba(0, 0, 0, 0.1);
          color: #e6a23c;
        }
        .name {
          flex: 1;
          box-sizing: border-box;
          display: flex;
          align-items: center;
          .sequence {
            display: inline-block;
            margin-right: 8px;
            width: 14px;
            text-align: center;
          }
          .wave {
            margin-right: 10px;
            width: 12px;
            height: 12px;
          }
          .no-wave {
            margin-right: 6px;
            font-size: 16px;
          }
          .text {
            flex: 1;
            .ellipseLine();
          }
        }
        .play-icon,
        .pause-icon,
        .download-icon,
        .share-icon {
          margin-right: 16px;
          font-size: 22px;
          cursor: pointer;
          &:hover {
            color: #e6a23c;
          }
        }
        .download {
          color: inherit;
          &:hover {
            color: #e6a23c;
          }
        }
        .audio-size {
          width: 120px;
          padding-right: 24px;
          text-align: right;
        }
      }
      .audio-list-body {
        flex: 1;
        box-sizing: border-box;
        overflow-y: auto;
        .no-scrollbar(0);
      }
    }
  }
  .control-wrapper {
    padding: 0 10%;
    height: 120px;
    display: flex;
    align-items: center;
    .control-left {
      flex: 1;
      height: 100%;
      display: flex;
      align-items: center;
      text-align: center;
      .operate-icon {
        margin-right: 16px;
        font-size: 40px;
        cursor: pointer;
        &:hover {
          color: #e6a23c;
        }
      }
      .play-icon,
      .pause-icon {
        font-size: 55px;
      }
      .progress-bar {
        margin-right: 16px;
        flex: 1;
        :deep(.el-slider__runway) {
          height: 2px;
          .el-slider__button {
            width: 15px;
            height: 15px;
          }
          .el-slider__button-wrapper {
            top: -17px;
            .el-slider__button {
              border: none;
            }
          }
          .el-slider__bar {
            height: 100%;
            background: #e6a23c;
          }
        }
      }
    }
    .control-right {
      width: 340px;
      font-size: 24px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 32px;
      margin-left: 20px;
      .operate-icon {
        margin-right: 16px;
        cursor: pointer;
        &:nth-last-of-type {
          margin-right: 0;
        }
        &:hover {
          color: #e6a23c;
        }
      }
      .volume-icon {
        margin-right: 8px;
      }
      .volume-bar {
        flex: 1;
        :deep(.el-slider__runway) {
          height: 2px;
          .el-slider__button {
            width: 15px;
            height: 15px;
          }
          .el-slider__button-wrapper {
            top: -24px;
            .el-slider__button {
              border: none;
            }
          }
          .el-slider__bar {
            height: 100%;
            background: #e6a23c;
          }
        }
      }
    }
  }
}
</style>
