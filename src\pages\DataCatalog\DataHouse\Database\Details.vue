<template>
  <div>
    <ElDialog
      v-model="dialogFormVisible"
      title="详情"
      top="10vh"
      width="500px"
      :close-on-click-modal="false"
    >
      <ElScrollbar max-height="50vh" wrap-style="overflow-x: hidden;">
        <ElForm ref="formRef" :model="formData" label-width="100px">
          <ElFormItem label="表名">
            <span>{{ formData.tableName }}</span>
          </ElFormItem>
          <ElFormItem label="空间数据">
            <span>{{ formData.metadataTableVO.isSpace ? '是' : '否' }}</span>
          </ElFormItem>
          <ElFormItem label="健康度">
            <span :style="{ color: healthColor(formData.scoreLevel) }">{{
              formData.scoreLevel
            }}</span>
          </ElFormItem>
          <ElFormItem label="质检状态">
            <span>{{ formData.serverStatusName }}</span>
          </ElFormItem>
          <ElFormItem label="数据量（条）">
            <span>{{ formData.metadataTableVO.dataVolume }}</span>
          </ElFormItem>
          <ElFormItem label="占用空间">
            <span>{{ formData.metadataTableVO.fileSize }}</span>
          </ElFormItem>
          <template v-if="formData.metadataTableVO?.isSpace">
            <ElFormItem label="坐标系">
              <span>{{ formData.metadataTableVO?.srid }}</span>
            </ElFormItem>
            <ElFormItem label="数据范围">
              <ElFormItem style="width: 100%" label="右上角X坐标">
                <span>{{ formData.metadataTableVO?.maxX }}</span>
              </ElFormItem>
              <ElFormItem style="width: 100%" label="左下角X坐标">
                <span>{{ formData.metadataTableVO?.minX }}</span>
              </ElFormItem>
              <ElFormItem style="width: 100%" label="右上角Y坐标">
                <span>{{ formData.metadataTableVO?.maxY }}</span>
              </ElFormItem>
              <ElFormItem style="width: 100%" label="左下角Y坐标">
                <span>{{ formData.metadataTableVO?.minY }}</span>
              </ElFormItem>
            </ElFormItem>
          </template>
          <ElFormItem label="创建时间">
            <span>{{ formData.createTime }}</span>
          </ElFormItem>
        </ElForm>
      </ElScrollbar>
    </ElDialog>
  </div>
</template>
<script setup lang="ts">
import useDialogForm from '@/hooks/useDialogForm'
import { healthColor } from '@/utils'
import { ElDialog, ElScrollbar } from 'element-plus'
import { ref } from 'vue'
const { dialogFormVisible, formRef, setDialogFormVisible } = useDialogForm()

const formData = ref<any>({})
const handleOpen = (row: any) => {
  formData.value = row
  setDialogFormVisible(true)
}

defineExpose({ handleOpen })
</script>
<style lang="less" scoped></style>
