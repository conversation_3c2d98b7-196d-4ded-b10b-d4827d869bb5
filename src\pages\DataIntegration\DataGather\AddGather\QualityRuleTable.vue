<template>
  <ElDialog
    v-model="dialogFormVisible"
    title="规则详情"
    top="10vh"
    width="900px"
    :close-on-click-modal="false"
  >
    <ElForm class="formData" ref="formRef" :model="formData" label-width="0px">
      <ElTable
        :data="formData.ruleList"
        header-cell-class-name="common-table-header"
        cell-class-name="common-table-cell"
        max-height="50vh"
        border
      >
        <el-table-column
          v-if="[3].includes(props.openType)"
          property="tableName"
          label="表名"
          width="200px"
        >
          <template #default="scope">
            <ElFormItem :prop="`ruleList.${scope.$index}.tableName`" :rules="rules">
              <ElSelect
                @change="
                  (p: any) => {
                    handleTableChange(scope.row, p)
                  }
                "
                value-key="userTableId"
                v-model="scope.row.tableName"
                placeholder="请选择表名"
              >
                <ElOption
                  v-for="p in tableList"
                  :key="p.tableName"
                  :label="p.tableName"
                  :value="p"
                ></ElOption>
              </ElSelect>
            </ElFormItem>
          </template>
        </el-table-column>
        <el-table-column width="150px" property="columnName" label="字段名">
          <template #default="scope">
            <ElFormItem :prop="`ruleList.${scope.$index}.columnName`" :rules="rules">
              <ElSelect v-model="scope.row.columnName" placeholder="请选择">
                <ElOption
                  v-for="p in [3].includes(props.openType)
                    ? tableOptions(scope.row.tableName)
                    : fieldOptions"
                  :key="p.fieldName"
                  :label="p.fieldName"
                  :value="p.fieldName"
                ></ElOption>
              </ElSelect>
            </ElFormItem>
          </template>
        </el-table-column>
        <el-table-column width="150px" property="ruleName" label="规则名称">
          <template #default="scope">
            <ElFormItem :prop="`ruleList.${scope.$index}.ruleName`" :rules="rules">
              <ElSelect
                @change="
                  (p: any) => {
                    handleChange(p, scope.row)
                  }
                "
                v-model="scope.row.ruleName"
                value-key="tid"
                placeholder="请选择"
              >
                <ElOption v-for="p in ruleOptions" :label="p.ruleName" :value="p"></ElOption>
              </ElSelect>
            </ElFormItem>
          </template>
        </el-table-column>
        <el-table-column property="adapterName" label="规则配置">
          <template #default="scope">
            <ul class="formList" v-if="scope.row.detailList && scope.row.detailList.length">
              <li v-for="(p, index) in scope.row.detailList" :key="index">
                <ElFormItem
                  :prop="`ruleList.${scope.$index}.detailList.${index}.value`"
                  :rules="rules"
                >
                  <ElInput
                    v-if="['text'].includes(p.textType)"
                    v-model="p.value"
                    placeholder="请输入"
                  ></ElInput>
                  <ElSelect
                    v-if="['select'].includes(p.textType)"
                    v-model="p.value"
                    placeholder="请选择"
                  >
                    <ElOption
                      v-for="(item, pindex) in p.valueList"
                      :key="pindex"
                      :label="item.dictName"
                      :value="item.dictValue"
                    ></ElOption>
                  </ElSelect>
                </ElFormItem>
              </li>
            </ul>
            <span v-else>--</span>
          </template>
        </el-table-column>
        <el-table-column property="adapterName" label="操作" width="80">
          <template #default="scope">
            <ElButton @click="handleDel(scope.$index)" type="primary" link>删除</ElButton>
          </template>
        </el-table-column>
      </ElTable>
      <div class="btn">
        <ElButton type="primary" @click="handleAdd" link>添加规则</ElButton>
      </div>
    </ElForm>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="setDialogFormVisible(false)">取消</el-button>
        <template v-if="[1].includes(props.openType)">
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </template>
        <template v-if="[2].includes(props.openType)">
          <el-button type="primary" @click="handleSave">保存</el-button>
          <el-button type="warning" @click="handeChecked">立即质检</el-button>
        </template>
        <template v-if="[3].includes(props.openType)">
          <el-button type="primary" @click="handleQualitySave">确定</el-button>
        </template>
      </span>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
import { ModelListTableApi, ModelListTableRuleApi, ModelUpdateRuleApi } from '@/api/data_governance'
import {
  listTableColumnApi,
  listTableRuleApi,
  userTableStartQaApi,
  userTableUpdateRuleApi,
} from '@/api/data_house'
import { listAllRuleApi } from '@/api/data_import'
import { queryPgTableFieldApi } from '@/api/data_source'
import useDialogForm from '@/hooks/useDialogForm'
import { ElButton, ElForm, ElFormItem, ElSelect, ElOption, ElDialog, ElMessage } from 'element-plus'
import { ref } from 'vue'

// openType 1 数据采集 2 数据目录 3 数据质量
const props = withDefaults(defineProps<{ openType?: number }>(), {
  openType: 1,
})

const { dialogFormVisible, formRef, setDialogFormVisible, validateForm } = useDialogForm()

const rules = { required: true, message: '必填！', trigger: 'change' }

const formData = ref<any>({
  ruleList: [],
})

const handleChange = (p: any, row: any) => {
  row.qaRuleId = p.tid
  row.ruleName = p.ruleName
  row.ruleMethod = p.ruleMethod
  row.detailList = p.detailList
}

const handleAdd = () => {
  formData.value.ruleList.push({})
}
const handleDel = (index: number) => {
  formData.value.ruleList.splice(index, 1)
}

const ruleOptions = ref<any[]>([])
const getRuleOptions = async () => {
  try {
    const { data, message, status } = await listAllRuleApi()
    if ([200].includes(status)) {
      ruleOptions.value = data
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}

const fieldOptions = ref<any[]>([])

// 数据采集打开入口
const handleOpen = async (row: any, dataSourceId: string) => {
  try {
    getRuleOptions()
    const { data, message, status } = await queryPgTableFieldApi({
      tableName: row.fileName,
      dataSourceId,
    })
    if ([200].includes(status)) {
      fieldOptions.value = data
      formData.value = row
      setDialogFormVisible(true)
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}
const handleSubmit = async () => {
  try {
    await validateForm()
    setDialogFormVisible(false)
  } catch (error) {}
}

// 数据目录打开入口
const getFieldOptions = async (tid: string) => {
  try {
    const { data, message, status } = await listTableColumnApi({ userTableId: tid })
    if ([200].includes(status)) {
      fieldOptions.value = data
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}
const handleCatalogOpen = async ({ tid }: any) => {
  try {
    getRuleOptions()
    getFieldOptions(tid)
    const { data, message, status } = await listTableRuleApi({ userTableId: tid })
    if ([200].includes(status)) {
      formData.value.userTableId = tid
      formData.value.ruleList = data
      setDialogFormVisible(true)
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}
const handleSave = async () => {
  try {
    await validateForm()
    const { message, status } = await userTableUpdateRuleApi(formData.value)
    if ([200].includes(status)) {
      ElMessage.success(message)
      emit('handleRefresh')
      setDialogFormVisible(false)
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}
const handeChecked = async () => {
  try {
    await validateForm()
    const { message, status } = await userTableStartQaApi(formData.value)
    if ([200].includes(status)) {
      ElMessage.success(message)
      emit('handleRefresh')
      setDialogFormVisible(false)
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}

// 数据治理打开入口
const tableList = ref<any[]>([])

const tableOptions = (tableName?: string): any[] => {
  if (!tableName) return []
  const tableObj: any = tableList.value.find((item: any) => item.tableName === tableName)
  return tableObj?.fieldList || []
}
const handleTableChange = (row: any, p: any) => {
  row.tableName = p.tableName
  row.userTableId = p.userTableId
  row.columnName = ''
}
const getTableList = async (dataCatalogId: string) => {
  try {
    const { data, message, status } = await ModelListTableApi({ dataCatalogId })
    if ([200].includes(status)) {
      tableList.value = data
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}

const handleQualityOpen = async ({ dataCatalogId, tid }: any) => {
  try {
    getRuleOptions()
    getTableList(dataCatalogId)
    const { data, message, status } = await ModelListTableRuleApi({ tid })
    if ([200].includes(status)) {
      formData.value.qaModelId = tid
      formData.value.ruleList = data
      setDialogFormVisible(true)
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}

const handleQualitySave = async () => {
  try {
    await validateForm()
    const { message, status } = await ModelUpdateRuleApi(formData.value)
    if ([200].includes(status)) {
      ElMessage.success(message)
      emit('handleRefresh')
      setDialogFormVisible(false)
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}

const emit = defineEmits<{ (e: 'handleRefresh'): void }>()
defineExpose({ handleOpen, handleCatalogOpen, handleQualityOpen })
</script>

<style lang="less" scoped>
.formData {
  width: 100%;
  box-sizing: border-box;
  .btn {
    text-align: right;
  }
  :deep(.el-form-item) {
    margin-bottom: 0;
  }
  .formList {
    width: 100%;
    display: flex;
    align-items: center;
    box-sizing: border-box;
    overflow: hidden;
    li {
      flex: 1;
      box-sizing: border-box;
      overflow: hidden;
    }
    li + li {
      margin-left: 10px;
    }
  }
}
</style>
