<template>
  <commonNav>
    <TableInfo v-if="route.query.tableName" />
    <div v-else class="dataHouse">
      <ul class="topNav">
        <li
          v-for="(item, index) in navList"
          @click="handleClick(item.path)"
          :key="index"
          :class="{ active: item.path === active }"
        >
          {{ item.name }}
        </li>
      </ul>
      <div class="mainContent">
        <component @handeTo="handleClick" :is="activeComponent" />
      </div>
    </div>
  </commonNav>
</template>
<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import Database from './Database/index.vue'
import Sql from './Sql/index.vue'
import BaseInfo from './BaseInfo/index.vue'
import commonNav from '../components/commonNav.vue'
import TableInfo from './Database/TableInfo/index.vue'
const route = useRoute()

const active = ref<string>(route.query.bsType == '2' ? 'Database' : 'Sql')

const activeComponent = computed(() => {
  return active.value === 'Database' ? Database : active.value === 'Sql' ? Sql : BaseInfo
})

watch(
  () => route.query.bsType,
  (val) => {
    active.value = val === '2' ? 'Database' : 'Sql'
  },
)

const navList = computed(() => {
  const list = [
    { name: 'SQL命令', path: 'Sql' },
    { name: '基本信息', path: 'BaseInfo' },
  ]
  if (route.query.bsType === '2') {
    return [{ name: '数据表', path: 'Database' }, ...list]
  }
  return list
})
const handleClick = (path: any) => {
  active.value = path
}
</script>
<style lang="less" scoped>
.dataHouse {
  height: 100%;
  box-sizing: border-box;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  .topNav {
    height: 50px;
    box-shadow: 0px 0px 7px 1px rgba(0, 0, 0, 0.05);
    background-color: @withe;
    display: flex;
    align-items: center;
    padding: 0 1.0417vw;
    margin-bottom: 1.0417vw;
    li {
      line-height: 50px;
      padding: 0 5px;
      cursor: pointer;
      font-size: 14px;
      color: #333333;
    }
    li + li {
      margin-left: 40px;
    }
    .active {
      color: var(--el-color-primary);
      border-bottom: 1px solid var(--el-color-primary);
    }
  }
  .mainContent {
    flex: 1;
    box-sizing: border-box;
    overflow: hidden;
  }
}
</style>
