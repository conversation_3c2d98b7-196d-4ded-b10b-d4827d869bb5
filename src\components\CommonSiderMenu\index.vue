<template>
  <ul class="common-sider-menu">
    <li
      v-for="p in siderMenu"
      :key="p.path"
      :class="{ active: active.includes(p.path) }"
      @click="handleRouter(p)"
    >
      {{ p.name }}
    </li>
  </ul>
</template>

<script setup lang="ts">
import { useRouter, useRoute } from 'vue-router'
import useRouterMenu from '@/hooks/useRouterMenu'

const { siderMenu, active } = useRouterMenu()

const router = useRouter()
const route = useRoute()

const handleRouter = (p: any) => {
  if ([p.path].includes(route.name)) return
  router.push({ name: p.path })
}
</script>
