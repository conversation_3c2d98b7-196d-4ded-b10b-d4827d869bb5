<template>
  <div class="addEditRules">
    <div class="form-data">
      <ElScrollbar>
        <ElForm ref="formRef" :model="formData" :rules="rules" class="form-main" label-width="auto">
          <ElRow :gutter="20" style="box-sizing: border-box">
            <ElCol :span="12">
              <ElFormItem label="规则名称" prop="ruleName">
                <ElInput
                  :disabled="route.query.optype == '3'"
                  v-model="formData.ruleName"
                  placeholder="请输入规则名称"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="数据类型" prop="dataType">
                <ElSelect disabled v-model="formData.dataType" placeholder="请选择数据类型">
                  <ElOption label="数据仓" :value="1" />
                  <ElOption label="数据湖" :value="2" />
                </ElSelect>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <el-form-item label="维度" prop="dimension">
                <el-select
                  :disabled="route.query.optype == '3'"
                  style="width: 100%"
                  v-model="formData.dimension"
                  placeholder="请选择维度"
                  clearable
                >
                  <el-option
                    v-for="(p, i) in options"
                    :key="i"
                    :label="p.dictName"
                    :value="p.dictValue"
                  />
                </el-select>
              </el-form-item>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="适用数据" prop="fileTypeId">
                <el-cascader
                  :disabled="route.query.optype == '3'"
                  :props="catalogProps"
                  :options="globalData.catalogMenuList"
                  style="width: 100%"
                  v-model="formData.fileTypeId"
                  clearable
                  placeholder="请选择适用数据"
                  @change="handleChange"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="24">
              <ElFormItem label="规则接口地址" prop="url">
                <ElInput
                  :disabled="route.query.optype == '3'"
                  v-model="formData.url"
                  placeholder="请输入规则接口地址"
                >
                  <template #append>
                    <ElButton
                      @click="SelectFileRef.handleOpen(formData.fileTypeId, formData.url)"
                      type="primary"
                      >校验接口</ElButton
                    >
                  </template>
                </ElInput>
              </ElFormItem>
            </ElCol>
            <ElCol :span="24">
              <ElFormItem label="规则说明" prop="notes">
                <ElInput
                  :disabled="route.query.optype == '3'"
                  :rows="3"
                  type="textarea"
                  v-model="formData.notes"
                  placeholder="请输入规则说明"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="24" style="margin-top: 30px">
              <el-tabs v-model="activeName" class="demo-tabs">
                <el-tab-pane label="接口模板" name="first">
                  <ElRow :gutter="20" style="box-sizing: border-box">
                    <ElCol :span="14">
                      <ElButton type="primary">请求方法</ElButton>
                      <ElInput
                        style="margin-top: 10px"
                        v-model="demoData.requestType"
                        placeholder="请输入"
                      />
                    </ElCol>
                    <ElCol style="margin-top: 20px" :span="14">
                      <ElButton type="primary">请求参数示例</ElButton>
                      <ElInput
                        style="margin-top: 10px"
                        :rows="5"
                        type="textarea"
                        v-model="demoData.requestExample"
                        placeholder="请输入"
                      />
                    </ElCol>
                    <ElCol style="margin-top: 20px" :span="14">
                      <ElButton type="primary">响应参数示例</ElButton>
                      <ElInput
                        style="margin-top: 10px"
                        :rows="10"
                        type="textarea"
                        v-model="demoData.responsetExample"
                        placeholder="请输入"
                      />
                    </ElCol>
                  </ElRow>
                </el-tab-pane>
                <el-tab-pane label="编码实例" name="second">
                  <ElRow :gutter="20" style="box-sizing: border-box">
                    <ElCol :span="14">
                      <ElInput
                        :rows="30"
                        type="textarea"
                        v-model="demoData.codeExample"
                        placeholder="请输入"
                      />
                    </ElCol>
                  </ElRow>
                </el-tab-pane>
              </el-tabs>
            </ElCol>
          </ElRow>
        </ElForm>
      </ElScrollbar>
    </div>
    <div class="footer-btn">
      <el-button @click="router.back">取消</el-button>
      <el-button
        v-if="route.query.optype != '3'"
        @click="handleSubmit"
        :loading="loading"
        type="primary"
        >确定</el-button
      >
    </div>
    <SelectFile ref="SelectFileRef" @get-file-id="getFileId" />
  </div>
</template>
<script setup lang="ts">
import {
  getInterfaceDemoApi,
  uqaRuleAddApi,
  uqaRuleGetByIdApi,
  uqaRuleUpdateApi,
} from '@/api/data_governance'
import useDictData from '@/hooks/useDictData'
import useGlobalData from '@/store/useGlobalData'
import {
  ElForm,
  ElRow,
  ElScrollbar,
  FormInstance,
  ElCol,
  CascaderProps,
  ElButton,
  ElMessage,
} from 'element-plus'
import { ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import SelectFile from './SelectFile.vue'

const route = useRoute()
const router = useRouter()

const SelectFileRef = ref<any>()
const getFileId = (id: string) => {
  formData.value.userFileId = id
}
const handleChange = () => {
  formData.value.userFileId = ''
}

const activeName = ref<string>('first')
const demoData = ref<any>({})
const getDemoData = async () => {
  try {
    const { data, message, status } = await getInterfaceDemoApi()
    if ([200].includes(status)) {
      demoData.value = data
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}
getDemoData()

const getDetail = async () => {
  if (!route.query.tid) return
  try {
    const { data, message, status } = await uqaRuleGetByIdApi({ tid: route.query.tid })
    if (status === 200) {
      formData.value = { ...data, userFileId: data.userFileId || data.tid }
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}
getDetail()

const loading = ref<boolean>(false)
const handleSubmit = async () => {
  if (!formData.value.userFileId) return ElMessage.warning('请先校验且成功后在进行提交!')
  try {
    loading.value = true
    const { message, status } = formData.value.tid
      ? await uqaRuleUpdateApi(formData.value)
      : await uqaRuleAddApi(formData.value)
    if ([200].includes(status)) {
      ElMessage.success(message)
      router.back()
    } else {
      ElMessage.error(message)
    }
    loading.value = false
  } catch (error) {
    loading.value = false
  }
}

const catalogProps: CascaderProps = {
  expandTrigger: 'hover',
  value: 'tid',
  label: 'fileTypeName',
  children: 'childVOList',
  emitPath: false,
}
const globalData = useGlobalData()

const options = useDictData('QA_DIMENSION')

const formRef = ref<FormInstance>()
const formData = ref<any>({ dataType: 2 })
const rules = ref<any>({
  ruleName: [{ required: true, message: '请输入规则名称', trigger: 'blur' }],
  dataType: [{ required: true, message: '请选择数据类型', trigger: 'blur' }],
  dimension: [{ required: true, message: '请选择维度', trigger: 'blur' }],
  fileTypeId: [{ required: true, message: '请选择适用数据', trigger: 'blur' }],
  url: [{ required: true, message: '请输入规则接口地址', trigger: 'blur' }],
})
</script>
<style lang="less" scoped>
.addEditRules {
  height: 100%;
  background-color: @withe;
  overflow: hidden;
  box-sizing: border-box;
  padding: 30px;
  display: flex;
  flex-direction: column;
  .form-data {
    flex: 1;
    box-sizing: border-box;
    overflow: hidden;
    .form-main {
      width: 900px;
    }
  }
  .footer-btn {
    width: 900px;
    text-align: right;
  }
}
</style>
