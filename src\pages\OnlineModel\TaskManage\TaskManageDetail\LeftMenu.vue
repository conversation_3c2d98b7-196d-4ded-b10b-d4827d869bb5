<template>
  <ul class="LeftMenu">
    <el-scrollbar>
      <li
        v-for="p in tableData"
        :key="p.tid"
        :class="{ active: active === p.tid }"
        @click="handleClick(p.tid)"
      >
        <SvgIcon name="onlineModelTask" />
        <span :title="`${route.query.taskName}${p.createTimeStr}`"
          >{{ route.query.taskName }}{{ p.createTimeStr }}</span
        >
      </li>
    </el-scrollbar>
    <div class="page">
      <el-button
        @click="pageChange.handlePage('prev')"
        class="icon"
        plain
        link
        :disabled="pageData.page <= 1"
      >
        <template #icon>
          <SvgIcon name="arrowLeft" />
        </template>
      </el-button>
      <ElInput
        type="number"
        class="page-int"
        size="small"
        :min="1"
        :validate-event="false"
        :max="Math.ceil(pageData.total / pageData.limit)"
        :model-value="innerValue"
        @update:model-value="pageChange.handleInput"
        @change="pageChange.handleChange"
      />
      <span class="page-s">/</span>
      <span class="page-total">{{ Math.ceil(pageData.total / pageData.limit) }}</span>
      <el-button
        @click="pageChange.handlePage('next')"
        class="icon"
        plain
        link
        :disabled="pageData.page >= Math.ceil(pageData.total / pageData.limit)"
      >
        <template #icon>
          <SvgIcon name="arrowRight" />
        </template>
      </el-button>
    </div>
  </ul>
</template>

<script setup lang="ts">
import { computed, reactive, ref } from 'vue'
import { useRoute } from 'vue-router'
import usePageData from '@/hooks/usePageData'
import { queryTaskModelApi } from '@/api/online_model'
import { ElInput } from 'element-plus'
const route = useRoute()
const { tableData, pageData } = usePageData(queryTaskModelApi, false)

const initData = async () => {
  const { tid }: any = route.query
  await pageData.handleSearch({ taskId: tid }, 1)
  if (tableData.value.length > 0) {
    handleClick(tableData.value[0].tid)
  }
}

initData()

const innerValue = computed(() => pageChange.userInput ?? pageData.page)
const pageChange = reactive<any>({
  userInput: undefined,
  handleInput: (val: number | string) => {
    pageChange.userInput = val ? +val : ''
  },
  handleChange: (val: number | string) => {
    val = Math.trunc(+val)
    const counts: number = Math.ceil(pageData.total / pageData.limit)
    if (val < 1) {
      val = 1
    }
    if (val > counts) {
      val = counts
    }
    const { tid }: any = route.query
    pageData.handleSearch({ taskId: tid }, val)
    pageChange.userInput = undefined
  },
  handlePage: (str: string) => {
    const { tid }: any = route.query
    pageData.handleSearch(
      { taskId: tid },
      ['next'].includes(str) ? pageData.page + 1 : pageData.page - 1,
    )
  },
})

const active = ref<string>()
const handleClick = (id: string) => {
  active.value = id
  emit('updateTaskId', active.value)
}

const emit = defineEmits<{
  updateTaskId: [id: string]
}>()
</script>

<style lang="less" scoped>
.LeftMenu {
  height: 100%;
  width: 200px;
  background-color: @withe;
  box-shadow: 0 8px 8px 8px rgba(0, 0, 0, 0.05);
  padding: 10px 0;
  box-sizing: border-box;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  cursor: pointer;
  li {
    padding: 8px 10px;
    display: flex;
    align-items: center;
    :nth-child(1) {
      font-size: 16px;
    }
    span {
      font-size: 14px;
      font-weight: 400;
      color: #333333;
      margin-left: 10px;
      flex: 1;
      .ellipseLine();
    }
  }
  .active {
    background-color: #f5f7fa;
  }
  .page {
    padding-right: 10px;
    margin-top: 15px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    font-size: 14px;
    color: #606266;
    .page-int {
      width: 40px;
      :deep(.el-input__inner) {
        text-align: center;
        &:focus {
          outline: none;
        }
        &::-webkit-inner-spin-button,
        &::-webkit-outer-spin-button {
          -webkit-appearance: none;
          margin: 0;
        }
      }
    }
    .page-s {
      margin: 0 10px;
    }
    .icon {
      font-size: 16px;
    }
  }
}
</style>
