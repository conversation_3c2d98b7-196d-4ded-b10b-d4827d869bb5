import { onMounted, reactive, ref, watch } from 'vue'
import { RGL<PERSON>, RGLink, RGNode, RGOptions } from 'relation-graph-vue3'
import {
  queryTaskModelStatusApi,
  queryListByParamApi,
  queryNodeConfigApi,
  checkNodeLineProtocolApi,
} from '@/api/online_model'
import { ElMessage } from 'element-plus'
import useSocket from '@/store/useSocket'

export default function useRelationGraph() {
  const RelationGraphRef = ref<any>()

  // 建模总体配置
  const options = ref<RGOptions>({
    backgroundColor: '#F8F9FA', // 背景色
    defaultNodeColor: '#ffffff', // 节点颜色
    defaultNodeFontColor: '#333333', // 节点文字颜色
    defaultNodeBorderColor: '#666666', // 节点边框颜色
    defaultNodeShape: 1, // 节点形状
    defaultLineColor: '#666666', // 线条颜色
    defaultLineShape: 1, // 线条样式
    defaultLineWidth: 1, // 线条粗细
    defaultNodeBorderWidth: 1,
    defaultNodeWidth: 50,
    defaultNodeHeight: 50,
    disableNodeClickEffect: true,
    checkedLineColor: 'rgba(90, 139, 254, 1)',
    allowShowMiniToolBar: false,
    moveToCenterWhenRefresh: false,
    defaultLineMarker: {
      markerWidth: 15,
      markerHeight: 15,
      refX: 30,
      refY: 7,
      data: 'M 14 7 L 1 .3 L 4 7 L .4 13 L 14 7, Z',
    },
    layout: {
      layoutName: 'center',
    },
  })

  // 获取全局配置
  const getOptions = async () => {
    try {
      const { data, message, status } = await queryNodeConfigApi()
      if ([200].includes(status)) {
        setOptions(data)
      } else {
        ElMessage.error(message)
      }
    } catch (error) {}
  }

  onMounted(() => {
    getOptions()
  })

  // 设置全局配置
  const setOptions = (data: any) => {
    options.value = { ...options.value, ...data }
    const getInstance = RelationGraphRef.value?.getInstance()
    getInstance.setOptions(options.value)
  }

  // 添加节点
  const addNode = (node: any, e: any) => {
    const getInstance = RelationGraphRef.value?.getInstance()
    const { x, y } = getInstance.getCanvasCoordinateByClientCoordinate({
      x: e.clientX,
      y: e.clientY,
    })
    getInstance.addNodes([
      {
        ...node,
        x,
        y,
      },
    ])
  }

  // 添加连线
  const addLine = (e: any) => {
    const getInstance = RelationGraphRef.value?.getInstance()
    getInstance.startCreatingLinePlot(e, {
      template: {
        text: '',
      },
      onCreateLine: async (from: RGNode, to: RGNode) => {
        try {
          const { message, status } = await checkNodeLineProtocolApi({
            fromId: from.data?.nodeId,
            toId: to.data?.nodeId,
          })
          if ([200].includes(status)) {
            getInstance.addLines([{ from: from.id, to: to.id, text: '' }])
          } else {
            ElMessage.error(message)
          }
        } catch (error) {
          ElMessage.error('连接是失败！')
        }
      },
    })
  }

  // 删除节点
  const removeNode = (node: RGNode) => {
    const getInstance = RelationGraphRef.value?.getInstance()
    getInstance.removeNode(node)
  }

  // 删除连接线
  const removeLine = (link: RGLink) => {
    const getInstance = RelationGraphRef.value?.getInstance()
    getInstance.removeLink(link)
  }

  // 缩放到合适大小
  const zoomToFit = () => {
    const getInstance = RelationGraphRef.value?.getInstance()
    getInstance.zoomToFit()
  }

  // 节点剧中
  const moveToCenter = () => {
    const getInstance = RelationGraphRef.value?.getInstance()
    getInstance.moveToCenter()
  }

  // 自适应屏幕尺寸
  const onGraphResize = () => {
    RelationGraphRef.value?.onGraphResize()
  }

  // 是否全屏
  const setFullScreen = async () => {
    const getInstance = RelationGraphRef.value?.getInstance()
    getInstance.fullscreen()
  }

  // 设置节点大小
  const setZoom = (buff: number) => {
    const getInstance = RelationGraphRef.value?.getInstance()
    getInstance.zoom(buff)
  }

  // 生成图片
  const getImage = (format: string = 'png') => {
    const getInstance = RelationGraphRef.value?.getInstance()
    return getInstance.getImageBase64(format)
  }

  // 取消选中节点连线
  const clearChecked = () => {
    const getInstance = RelationGraphRef.value?.getInstance()
    getInstance.clearChecked()
  }

  // 清空所有数据
  const clearAll = () => {
    const getInstance = RelationGraphRef.value?.getInstance()
    getInstance.clearGraph()
    // getInstance._setJsonData({ nodes: [], lines: [] })
  }

  // 设置上图
  const setJsonData = (data: any, resetViewSize?: boolean) => {
    const getInstance = RelationGraphRef.value?.getInstance()
    return getInstance?.setJsonData(data, resetViewSize)
  }

  // 获取当前节点所有的数据
  const getGraphJsonData = () => {
    const getInstance = RelationGraphRef.value?.getInstance()
    return getInstance.getGraphJsonData()
  }

  // 设置选中节点
  const setCheckedNode = (nodeId: string) => {
    const getInstance = RelationGraphRef.value?.getInstance()
    getInstance.setCheckedNode(nodeId)
  }

  // 轮询获取各个节点的状态
  interface TaskModelStatus {
    timer: any
    taskModelId: string
    isRunning: boolean
    getTaskStatus: () => Promise<void>
    initStatus: (taskModelId: string) => Promise<void>
    stopTask: () => void
    filterNode: (nodes: any[]) => void
    filterLine: (lines: any[]) => void
  }
  const taskModelStatus = reactive<TaskModelStatus>({
    timer: null,
    taskModelId: '',
    isRunning: false,
    filterNode: async (nodes: any[]) => {
      const getInstance = RelationGraphRef.value?.getInstance()
      const listNode: RGNode[] = await getInstance.getNodes()
      const commonList: any[] = nodes.filter((item: any) => {
        return !!listNode.find((node: any) => node.id === item.nodePkId)
      })
      commonList.forEach((item: any) => {
        const node: any = listNode.find((node: any) => node.id === item.nodePkId)
        if (node) {
          node.data.nodeStatus = item.nodeStatus
          node.borderColor = item.borderColor
          node.data.tid = item.tid
        }
      })
    },
    filterLine: (lines: any) => {
      const getInstance = RelationGraphRef.value?.getInstance()
      const listLink: RGLink[] = getInstance.getLinks()
      const listLine: RGLine[] = listLink.reduce(
        (pre: RGLine[], item: RGLink) =>
          Array.isArray(item.relations) ? [...pre, ...item.relations] : pre,
        [],
      )
      const commonList: any[] = lines.filter((item: any) => {
        return !!listLine.find((line: RGLine) => line.from === item.fromId && line.to === item.toId)
      })
      commonList.forEach((item: any) => {
        const line: RGLine | undefined = listLine.find(
          (line: RGLine) => line.from === item.fromId && line.to === item.toId,
        )
        if (line) {
          line.color = item.color
          line.animation = [3].includes(item.lineStatus) ? 1 : undefined
        }
      })
    },
    getTaskStatus: async () => {
      try {
        const { data, message, status } = await queryTaskModelStatusApi(taskModelStatus.taskModelId)
        if ([200].includes(status)) {
          if (data) {
            taskModelStatus.filterNode(data.taskModelNodeVOList)
            taskModelStatus.filterLine(data.taskModelNodeLineVOList)
            if ([0, 1, 4].includes(data.runStatus)) {
              taskModelStatus.stopTask()
            }
          }
        } else {
          ElMessage.error(message || '系统出错了！')
          taskModelStatus.stopTask()
        }
      } catch (error) {
        taskModelStatus.stopTask()
      }
    },
    initStatus: async (taskModelId: string) => {
      taskModelStatus.taskModelId = taskModelId
      taskModelStatus.isRunning = true
      taskModelStatus.getTaskStatus()
      taskModelStatus.timer = setInterval(() => {
        taskModelStatus.getTaskStatus()
      }, 1000)
    },
    stopTask: async () => {
      if (taskModelStatus.timer) {
        clearInterval(taskModelStatus.timer)
        taskModelStatus.timer = null
        taskModelStatus.taskModelId = ''
        taskModelStatus.isRunning = false
      }
    },
  })

  // 日志信息
  const socket = useSocket()
  interface LogData {
    logList: any[]
    taskModelId: string
    setTaskModelId: (taskModelId: string) => void
    getLogData: () => Promise<void>
    getAllLogData: (taskModelId: string) => Promise<void>
    clearLogData: () => void
    clearData: () => void
  }
  const logData = reactive<LogData>({
    logList: [],
    taskModelId: '',
    setTaskModelId: (taskModelId: string) => {
      logData.taskModelId = taskModelId
    },
    getLogData: async () => {
      socket.handleCreateSocket()
    },
    getAllLogData: async (taskModelId: string) => {
      try {
        logData.taskModelId = taskModelId
        const { data, status, message } = await queryListByParamApi({
          taskModelId: logData.taskModelId,
        })
        if ([200].includes(status)) {
          logData.logList = data.map((item: any) => ({ messageDesc: item.log }))
        } else {
          ElMessage.error(message || '系统出错了！')
        }
      } catch (error) {}
    },
    clearLogData: () => {
      socket.handleCloseSocket()
      logData.logList = []
    },
    clearData: () => {
      logData.logList = []
    },
  })
  watch(
    () => socket.socketData,
    (val: any) => {
      const flag: Boolean =
        val &&
        Object.keys(val).length &&
        [3].includes(val.messageType) &&
        val.taskModelId === logData.taskModelId
      if (flag) {
        logData.logList.push(val)
      }
    },
  )

  return {
    setOptions,
    logData,
    taskModelStatus,
    RelationGraphRef,
    options,
    addNode,
    removeNode,
    setFullScreen,
    setZoom,
    getImage,
    addLine,
    zoomToFit,
    setJsonData,
    removeLine,
    clearChecked,
    clearAll,
    getGraphJsonData,
    onGraphResize,
    setCheckedNode,
    getOptions,
    moveToCenter,
  }
}
