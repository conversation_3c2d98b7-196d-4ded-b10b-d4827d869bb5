import request from '@/utils/request'

// 质量规则查询列表
export const uqaRuleListApi = (data: any) => request.get(`/qaRule/list`, data)

// 质量规则查询详情
export const uqaRuleGetByIdApi = (data: any) => request.get(`/qaRule/getById`, data)

// 质量规则新增规则
export const uqaRuleAddApi = (data: any) => request.post(`/qaRule/add`, data)

// 质量规则编辑详情
export const uqaRuleUpdateApi = (data: any) => request.post(`/qaRule/update`, data)

// 质量规则查询接口模板
export const getInterfaceDemoApi = () => request.post(`/qaRule/getInterfaceDemo`)

// 质量规则-删除规则
export const uqaRuleRemoveByIdApi = (data: any) => request.get(`/qaRule/removeById`, data)

// 质量规则-校验接口
export const uqaRuleValidateApi = (data: any) => request.post(`/qaRule/validate`, data)

// 质量模型查询列表
export const qaModelListApi = (data: any) => request.get(`/qaModel/list`, data)

// 质量模型编辑
export const qaModelUpdateApi = (data: any) => request.post(`/qaModel/update`, data)

// 质量模型新增模型
export const qaModelAddApi = (data: any) => request.post(`/qaModel/add`, data)

// 质量模型-删除模型
export const qaModelRemoveByIdApi = (data: any) => request.get(`/qaModel/removeById`, data)

// 质量模型查询详情
export const qaModelGetByIdApi = (data: any) => request.get(`/qaModel/getById`, data)

// 自治治理规则查询列表
export const qaAutoRuleListApi = (data: any) => request.get(`/qaAutoRule/list`, data)

// 标准管理查询列表
export const qaStandardListApi = (data: any) => request.get(`/qaStandard/list`, data)

//健康度评分标准列表
export const qaScoreListApi = (data: any) => request.get(`/qaScore/list`, data)

// 健康度评分标准编辑
export const qaScoreUpdateApi = (data: any) => request.post(`/qaScore/update`, data)

// 健康度评分标准新增
export const qaScoreAddApi = (data: any) => request.post(`/qaScore/add`, data)

//删除标准
export const qaScoreRemoveByIdApi = (data: any) => request.get(`/qaScore/removeById`, data)

// 治理任务查询列表
export const qaTaskListApi = (data: any) => request.get(`/qaTask/list`, data)

// 查询质量监控结果
export const queryResultApi = () => request.get(`/qaMonitor/queryResult`)

// 查询数据库所有表
export const ModelListTableApi = (data: any) => request.get(`/qaTableRule/listTable`, data)

// 查询当前模型规则
export const ModelListTableRuleApi = (data: any) => request.get(`/qaModel/listTableRule`, data)

export const ModelUpdateRuleApi = (data: any) =>
  request.post(`/qaModel/updateRule/${data.qaModelId}`, data.ruleList)
