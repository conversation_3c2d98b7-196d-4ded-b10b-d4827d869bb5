<template>
  <el-form ref="ruleFormRef" :model="ruleForm">
    <div class="search-content">
      <div class="search-input">
        <div class="search-item">
          <el-form-item prop="ruleName">
            <el-input
              class="form-item"
              v-model="ruleForm.ruleName"
              placeholder="请输入规则名称"
              @keydown.enter="handleSearch"
            >
              <template #suffix>
                <SvgIcon @click="handleSearch" style="cursor: pointer" name="search" />
              </template>
            </el-input>
          </el-form-item>
        </div>
        <div class="search-item">
          <el-form-item prop="dimension">
            <el-select
              style="width: 100%"
              @change="handleSearch"
              v-model="ruleForm.dimension"
              placeholder="请选择维度"
              clearable
            >
              <el-option
                v-for="(p, i) in options"
                :key="i"
                :label="p.dictName"
                :value="p.dictValue"
              />
            </el-select>
          </el-form-item>
        </div>
        <div class="search-item">
          <ElFormItem prop="dataType">
            <ElSelect
              v-model="ruleForm.dataType"
              @change="handleSearch"
              placeholder="请选择数据类型"
              clearable
            >
              <ElOption label="数据仓" :value="1" />
              <ElOption label="数据湖" :value="2" />
            </ElSelect>
          </ElFormItem>
        </div>
      </div>
      <div v-if="permission.hasButton('qualityRules_add')" class="search-btn">
        <el-button type="primary" @click="emit('handleCreate')"> 新增规则 </el-button>
      </div>
    </div>
  </el-form>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import type { FormInstance } from 'element-plus'
import useDictData from '@/hooks/useDictData'
import useUserInfo from '@/store/useUserInfo'

const permission = useUserInfo()

const options = useDictData('QA_DIMENSION')

const ruleFormRef = ref<FormInstance>()
const ruleForm = ref<any>({})

const handleSearch = () => {
  emit('handleSearch', ruleForm.value)
}

const emit = defineEmits<{
  (e: 'handleSearch', form: any): void
  (e: 'handleCreate'): void
}>()
</script>
