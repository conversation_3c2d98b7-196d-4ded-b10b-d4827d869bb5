// 字典值常量枚举
export enum DicType {
  // 存储适配器编码（对象存储、文件存储）
  AdapterOssDsCode = 'storeAdapterOssDsCode',
  // 存储适配器编码
  AdapterCode = 'storeAdapterCode',
  // 存储适配器类型
  AdapterTypeCode = 'storeAdapterTypeCode',
  // 数据源状态
  DatasourceStatusCode = 'datasourceStatusCode',
  // 数据目录服务发布状态
  ServerStatusCode = 'serverStatusCode',
  // 数据导入任务状态
  TaskStatusCode = 'taskStatusCode',
  //  数据安全任务状态
  TaskBackUpStatusCode = 'taskBackUpStatusCode'
}
