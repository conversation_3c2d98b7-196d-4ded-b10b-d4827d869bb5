<template>
  <div class="setting" id="CesiumRef"></div>
</template>

<script setup lang="ts">
import * as Cesium from 'cesium'
import useCesium from '@/hooks/useCesium'
import { onMounted } from 'vue'
import { center, getBaseMap, markMap } from '@/utils/mapConfig'

const { initCesium, cesiumViewer } = useCesium()

const setViewZoom = (coordinate: [number, number] = center, zoom: number = 6) => {
  const position = Cesium.Cartesian3.fromDegrees(...coordinate, 10000000)
  cesiumViewer.value?.camera.setView({
    destination: position,
    orientation: {
      heading: Cesium.Math.toRadians(0.0),
      pitch: Cesium.Math.toRadians(-90.0),
      roll: 0.0,
    },
  })
  cesiumViewer.value?.camera.zoomIn(zoom)
}

// const url = '/e-server/model/webqxsy/DM-1803628854604554242/tileset.json'

onMounted(() => {
  initCesium('CesiumRef', () => {
    const baseImageryProvider = new Cesium.UrlTemplateImageryProvider({
      url: getBaseMap(),
      maximumLevel: 18,
      tileHeight: 256,
      tileWidth: 256,
    })
    cesiumViewer.value?.imageryLayers.addImageryProvider(baseImageryProvider)
    const markImageryProvider = new Cesium.UrlTemplateImageryProvider({
      url: markMap,
      maximumLevel: 18,
      tileHeight: 256,
      tileWidth: 256,
    })
    cesiumViewer.value?.imageryLayers.addImageryProvider(markImageryProvider)
    setViewZoom()
  })
})
</script>

<style scoped lang="less">
.setting {
  width: 100%;
  height: 100vh;
  box-sizing: border-box;
  position: relative;
}
</style>
