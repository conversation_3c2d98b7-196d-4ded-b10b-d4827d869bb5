@import './reset.less';
@import 'nprogress/nprogress.css';
@import 'element-plus/dist/index.css';
@import 'mapbox-gl/dist/mapbox-gl.css';
@import '@mapbox/mapbox-gl-draw/dist/mapbox-gl-draw.css';
@import 'ol/ol.css';
@import './element.less';

//nprogress样式重置
#nprogress .spinner {
  display: none;
}

// cesium
.cesium-viewer-bottom {
  display: none;
}

// 常用搜索框样式
.search-content {
  display: flex;
  box-sizing: border-box;
  .search-input {
    flex: 1;
    margin-right: 10px;
    box-sizing: border-box;
    display: flex;
    .search-item {
      flex: 1;
      max-width: 300px;
      .el-form-item {
        margin-bottom: 0;
      }
      .form-item {
        width: 100%;
      }
    }
    .search-item + .search-item {
      margin-left: 10px;
    }
  }
  .search-btn {
    margin-left: 15px;
  }
}

// 常用表格表头颜色
.common-table-header {
  height: 45px;
  background-color: #f0f1f2 !important;
  border-bottom: none !important;
  .cell {
    font-size: 14px;
    font-weight: 800;
    color: #333333;
  }
}
.common-table-cell {
  height: 45px;
  padding: 0 !important;
  box-sizing: border-box !important;
}

// 常用图表按钮大小
.common-icon-btn {
  font-size: 18px !important;
}
.common-icon-btn + .common-icon-btn {
  margin-left: 8px !important;
}

// 常用预览弹框
.common-preview-wrapper {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  overflow: auto;
  width: 100%;
  height: 100vh;
  z-index: 100;
  animation: imgPreviewAnimation 0.3s;
  animation-iteration-count: 0.3;
  animation-fill-mode: forwards;

  @keyframes imgPreviewAnimation {
    0% {
      background: transparent;
    }

    100% {
      background: rgba(0, 0, 0, 0.8);
    }
  }
}
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s ease;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

// 通用侧边栏菜单样式
.common-sider-menu {
  width: 150px;
  height: 100%;
  background-color: @withe;
  box-shadow: 0px 0px 7px 1px rgba(0, 0, 0, 0.05);
  padding: 20px 10px;
  box-sizing: border-box;
  li {
    height: 36px;
    line-height: 36px;
    font-size: 16px;
    color: #21333f;
    text-align: center;
    cursor: pointer;
  }
  li + li {
    margin-top: 10px;
  }
  li.active {
    background: var(--el-color-primary);
    color: @withe;
  }
}
// 同用二级目录布局
.common-layout {
  display: flex;
  height: 100%;
  box-sizing: border-box;
  overflow: hidden;
  .common-layout-main {
    flex: 1;
    box-sizing: border-box;
    overflow: hidden;
    margin-left: 20px;
  }
}

// 在线建模连接线动画样式

.graph .rg-line-anm-1 {
  animation: my-line-anm1 2s infinite !important;
}

@keyframes my-line-anm1 {
  0% {
    stroke-dashoffset: 100px;
    stroke-dasharray: 5, 5, 5;
  }
  100% {
    stroke-dasharray: 5, 5, 5;
    stroke-dashoffset: 3px;
  }
}
