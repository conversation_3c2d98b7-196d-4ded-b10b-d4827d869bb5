<template>
  <ElDialog
    title="注册服务"
    top="10vh"
    width="600px"
    :close-on-click-modal="false"
    v-model="dialogFormVisible"
    @close="handleDialogClose"
  >
    <ElScrollbar height="60vh">
      <ElForm ref="formRef" scroll-to-error :model="formData" :rules="formRule" label-width="80px">
        <ElRow class="row" :gutter="20">
          <ElCol class="title" :span="24">基本信息</ElCol>
          <ElCol :span="24">
            <ElFormItem label="文件名" prop="fileName">
              <span>{{ formData.fileName }}</span>
            </ElFormItem>
          </ElCol>
          <ElCol :span="24">
            <ElFormItem label="资源名称" prop="name">
              <el-input v-model="formData.name" placeholder="请输入资源名称" />
            </ElFormItem>
          </ElCol>
          <ElCol :span="24">
            <ElFormItem label="服务协议" prop="protocol">
              <el-select
                v-model="formData.protocol"
                @change="handleProtocolChange"
                placeholder="请选择数据分类"
                value-key="protocol"
              >
                <el-option v-for="p in serverList" :key="p.tid" :label="p.protocol" :value="p" />
              </el-select>
            </ElFormItem>
          </ElCol>
          <ElCol :span="24">
            <ElFormItem label="缩略图" prop="pictureUrl">
              <ImageUpload
                :action="dateWareHouseUploadThumbnailApi"
                v-model="formData.pictureUrl"
              />
            </ElFormItem>
          </ElCol>
          <template v-if="!['JSON'].includes(formData.protocol)">
            <ElCol :span="24">
              <ElFormItem label="坐标系" prop="srid">
                <el-input readonly v-model="formData.srid" placeholder="请输入坐标系"> </el-input>
              </ElFormItem>
            </ElCol>
            <ElCol :span="24">
              <ElFormItem label="数据范围" prop="maxX">
                <el-input
                  class="dataItem"
                  readonly
                  v-model="formData.maxX"
                  placeholder="请输入最大经度"
                >
                  <template #append>
                    <span>最大经度</span>
                  </template>
                </el-input>
              </ElFormItem>
            </ElCol>
            <ElCol :span="24">
              <ElFormItem prop="maxY">
                <el-input
                  class="dataItem"
                  readonly
                  v-model="formData.maxY"
                  placeholder="请输入最大纬度"
                >
                  <template #append>
                    <span>最大纬度</span>
                  </template>
                </el-input>
              </ElFormItem>
            </ElCol>
            <ElCol :span="24">
              <ElFormItem prop="minX">
                <el-input
                  class="dataItem"
                  readonly
                  v-model="formData.minX"
                  placeholder="请输入最小经度"
                >
                  <template #append>
                    <span>最小经度</span>
                  </template>
                </el-input>
              </ElFormItem>
            </ElCol>
            <ElCol :span="24">
              <ElFormItem prop="minY">
                <el-input
                  class="dataItem"
                  readonly
                  v-model="formData.minY"
                  placeholder="请输入最小纬度"
                >
                  <template #append>
                    <span>最小纬度</span>
                  </template>
                </el-input>
              </ElFormItem>
            </ElCol>
          </template>

          <ElCol :span="24">
            <ElFormItem label="服务分类" prop="productTypeId">
              <el-tree-select
                v-model="formData.productTypeId"
                :data="productTypeData"
                :render-after-expand="false"
                style="width: 100%"
                :props="treeProps"
                value-key="id"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="24">
            <ElFormItem label="行政区划" prop="admincode">
              <el-tree-select
                v-model="formData.admincode"
                check-strictly
                :data="asdData"
                style="width: 100%"
                :props="treeAsdProps"
                value-key="value"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="24">
            <ElFormItem label="数据标签" prop="productLabelIdList">
              <el-select
                v-model="formData.productLabelIdList"
                multiple
                collapse-tags
                collapse-tags-tooltip
                placeholder="请选择数据标签"
              >
                <el-option
                  v-for="item in productLabelData"
                  :key="item.id"
                  :value="item.id"
                  :label="item.name"
                />
              </el-select>
            </ElFormItem>
          </ElCol>
          <ElCol :span="24">
            <ElFormItem label="描述信息" prop="remark">
              <el-input
                v-model="formData.remark"
                :rows="3"
                type="textarea"
                placeholder="请填写描述信息"
              />
            </ElFormItem>
          </ElCol>
          <ElCol class="title" :span="24">
            发布信息
            <span>发布信息将会在时空数据门户资源详情中显示，您可以自行选择填写。</span>
          </ElCol>
          <ElCol :span="24">
            <ElFormItem label="发布人" prop="pubUserName">
              <el-input v-model="formData.pubUserName" placeholder="请输入发布人" />
            </ElFormItem>
          </ElCol>
          <ElCol :span="24">
            <ElFormItem label="发布单位" prop="pubUnitName">
              <el-input v-model="formData.pubUnitName" placeholder="请输入发布单位" />
            </ElFormItem>
          </ElCol>
          <ElCol :span="24">
            <ElFormItem label="联系电话" prop="pubUserPhone">
              <el-input v-model="formData.pubUserPhone" placeholder="请输入联系电话" />
            </ElFormItem>
          </ElCol>
          <ElCol :span="24">
            <ElFormItem label="公开范围" prop="sharedScope">
              <el-radio-group v-model="formData.sharedScope">
                <el-radio-button
                  v-for="p in sharedScopeOptions"
                  :key="p.code"
                  :value="p.code"
                  :label="p.value"
                >
                </el-radio-button>
              </el-radio-group>
            </ElFormItem>
          </ElCol>
        </ElRow>
      </ElForm>
    </ElScrollbar>
    <template #footer>
      <el-text v-if="isRegister" type="danger">该数据已注册！</el-text>
      <ElButton @click="setDialogFormVisible(false)">取消</ElButton>
      <ElButton v-if="!isRegister" :loading="loading" type="primary" @click="handleSubmit"
        >确定</ElButton
      >
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
import { ElForm, ElFormItem, ElDialog, ElScrollbar, ElRow, ElCol, ElMessage } from 'element-plus'
import ImageUpload from '@/components/ImageUpload/index.vue'
import useDialogForm from '@/hooks/useDialogForm'
import { ref } from 'vue'
import { productTypeListApi } from '@/api/data_catalog'
import {
  dateWareHouseUploadThumbnailApi,
  getUserTableExcellentDetailApi,
  getUserTableProductDetailByUserFileIdApi,
  userTableproductRegisterApi,
  userTableServerListApi,
} from '@/api/data_house'

const { dialogFormVisible, handleDialogClose, setDialogFormVisible, formRef, validateForm } =
  useDialogForm()

defineProps<{
  treeAsdProps: any
  productLabelData: any[]
  sharedScopeOptions: any[]
  asdData: any[]
}>()

// 数据分类
const treeProps = {
  children: 'childList',
  value: 'id',
  label: 'name',
}
const productTypeData = ref<any>([])
const getProductTypeData = async () => {
  try {
    const { data, message, status } = await productTypeListApi({ productType: 2 })
    if ([200].includes(status)) {
      productTypeData.value = data
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}
getProductTypeData()

const formData = ref<any>({})
const formRule = ref<any>({
  name: [{ required: true, message: '应用数据名称', trigger: 'change' }],
  userFileId: [{ required: true, message: '请选择文件', trigger: 'change' }],
  protocol: [{ required: true, message: '请选择协议', trigger: 'change' }],
  provider: [{ required: true, message: '请输入服务商', trigger: 'change' }],
  fileTypeId: [{ required: true, message: '请选择文件数据类型', trigger: 'change' }],
  pictureUrl: [{ required: true, message: '请上传图片', trigger: 'blur' }],
  productTypeId: [{ required: true, message: '请选择数据分类', trigger: 'blur' }],
  pubUserName: [{ required: true, message: '请输入发布人', trigger: 'blur' }],
  pubUnitName: [{ required: true, message: '请输入发布单位', trigger: 'blur' }],
  pubUserPhone: [{ required: true, message: '请输入联系电话', trigger: 'blur' }],
  sharedScope: [{ required: true, message: '请选择公开范围', trigger: 'blur' }],
})

const serverList = ref<any[]>([])
const getServerList = async (tid: string) => {
  try {
    const { data, message, status } = await userTableServerListApi(tid)
    if ([200].includes(status)) {
      serverList.value = data
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}

const handleProtocolChange = (p: any) => {
  formData.value.protocol = p.protocol
  formData.value.url = p.serverUrl
  formData.value.serverId = p.serverId
  const data = productData.value.find((item: any) => item.protocol === p.protocol)
  if (data) {
    isRegister.value = true
    formData.value = { ...data }
  } else {
    isRegister.value = false
  }
}

const isRegister = ref<boolean>(false)
const productData = ref<any>([])
const handleOpen = async (row: any) => {
  try {
    isRegister.value = false
    const { data, message, status } = await getUserTableProductDetailByUserFileIdApi({
      tableId: row.tid,
    })
    if ([200].includes(status)) {
      productData.value = data.filter((item: any) => item.productType === 2)
      await getServerList(row.tid)
      await getFileData(row.tid)
      setDialogFormVisible(true)
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
  setDialogFormVisible(true)
}

const getFileData = async (tableId: string) => {
  try {
    const { data, message, status } = await getUserTableExcellentDetailApi({ tableId })
    if ([200].includes(status)) {
      const {
        fileTypeId,
        fileTypeName,
        tableId,
        pictureUrl,
        fileName,
        srid,
        maxX,
        maxY,
        minX,
        minY,
        fileSize,
      } = data
      formData.value.fileTypeId = fileTypeId
      formData.value.fileTypeName = fileTypeName
      formData.value.userFileId = tableId
      formData.value.pictureUrl = pictureUrl
      formData.value.fileName = `${fileName}`
      formData.value.fileSize = fileSize
      formData.value.srid = srid
      formData.value.maxX = maxX
      formData.value.maxY = maxY
      formData.value.minX = minX
      formData.value.minY = minY
      const protocol = serverList.value.find((item: any) => item.protocol === 'TMS')
      formData.value.previewUrl = protocol?.serverUrl || ''
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}

const loading = ref<boolean>(false)
const handleSubmit = async () => {
  try {
    loading.value = true
    await validateForm()
    const { status, message } = await userTableproductRegisterApi({
      ...formData.value,
      productType: 2,
      source: '5',
      provider: '3',
    })
    if ([200].includes(status)) {
      ElMessage.success('注册成功')
      setDialogFormVisible(false)
    } else {
      ElMessage.error(message)
    }
    loading.value = false
  } catch (error) {
    loading.value = false
  }
}

defineExpose({ handleOpen })
</script>

<style scoped lang="less">
.row {
  width: 100%;
  box-sizing: border-box;
  .title {
    font-weight: bold;
    font-size: 14px;
    color: #333333;
    margin-bottom: 20px;
    > span {
      font-size: 12px;
      color: #999999;
      margin-left: 15px;
      font-weight: 400;
    }
  }
  .dataRange {
    width: 100%;
    display: flex;
    .dataItem {
      flex: 1;
    }
    .dataItem + .dataItem {
      margin-left: 20px;
    }
  }
  .dataRange + .dataRange {
    margin-top: 18px;
  }
}
</style>
