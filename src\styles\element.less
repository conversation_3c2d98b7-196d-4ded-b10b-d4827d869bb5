.el-dialog {
  padding: 0px !important;
  .el-dialog__header {
    padding: 10px 20px !important;
    margin: 0px !important;
    box-sizing: border-box;
    border-bottom: 1px solid #e9ecf0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: var(--el-color-primary);
    .el-dialog__title {
      color: @withe;
    }
    .el-dialog__headerbtn {
      position: static;
      height: auto;
      width: auto;
      display: flex;
      align-items: center;
      .el-dialog__close {
        color: @withe !important;
      }
    }
  }

  .el-dialog__body {
    padding: 20px !important;
  }

  .el-dialog__footer {
    padding: 15px !important;
  }
}

.el-button:focus-visible {
  outline: none !important;
  outline-offset: 1px;
}

.el-select {
  width: 100%;
}
