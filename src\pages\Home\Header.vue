<template>
  <div class="header">
    <div class="header-left">
      <img :src="userInfo.systemInfo?.pictureUrl || logo" alt="logo" />
      <p>{{ userInfo.systemInfo?.name }}</p>
    </div>
    <div class="header-right">
      <div class="inlet" @click="handleServer">
        <span>数据目录</span>
        <img :src="inlet" alt="logo" />
      </div>
      <el-dropdown>
        <span class="el-dropdown-link">
          <img :src="logo" alt="logo" />
          <span>{{ userInfo.userInfo?.name || 'admin' }}</span>
          <SvgIcon class="icon" name="arrowDown" />
        </span>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item @click="userInfo.handleLogout">退出登录</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>

<script setup lang="ts">
import logo from '@/assets/commonImg/logo.png'
import inlet from '@/assets/homeImg/inlet.svg'
import { useRouter } from 'vue-router'
import useUserInfo from '@/store/useUserInfo'

const router = useRouter()

const userInfo = useUserInfo()

const handleServer = () => {
  router.push({ name: 'layout' })
}
</script>

<style scoped lang="less">
.header {
  height: 3.6458vw;
  background: #252d4b;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 2.0833vw;
  .header-left {
    height: 100%;
    display: flex;
    align-items: center;
    img {
      height: 2.1875vw;
      width: 2.1875vw;
    }
    p {
      font-size: 1.1458vw;
      font-family: SourceHanSansCN-Medium, SourceHanSansCN;
      font-weight: 500;
      color: @withe;
      margin-left: 1.0417vw;
    }
  }
  .header-right {
    display: flex;
    align-items: center;
    .inlet {
      display: flex;
      align-items: center;
      margin-right: 2.0833vw;
      cursor: pointer;
      span {
        font-size: 0.8333vw;
        font-family: MicrosoftYaHei;
        color: #fefefe;
      }
      img {
        width: 1.6667vw;
        margin-left: 0.7813vw;
      }
    }
    .el-dropdown-link {
      display: flex;
      align-items: center;
      cursor: pointer;
      img {
        width: 1.6667vw;
      }
      span,
      .icon {
        font-size: 0.7292vw;
        color: #fefefe;
        margin-left: 0.5208vw;
      }
    }
  }
}
:global(:focus-visible) {
  outline: none;
}
</style>
