<template>
  <div class="mainLayout">
    <div class="header">
      <SearchTool @handle-search="handleSearch" :fieldOptions="fieldOptions" />
    </div>
    <div class="main">
      <el-table
        v-loading="pageData.loading"
        header-cell-class-name="common-table-header"
        cell-class-name="common-table-cell"
        ref="multipleTableRef"
        :data="tableData"
        height="100%"
        style="width: 100%"
      >
        <el-table-column
          v-for="p in fieldOptions"
          :key="p.fieldName"
          :prop="p.fieldName"
          :label="p.fieldName"
          min-width="150"
        >
          <template #header>
            <span :title="p.fieldName">{{ p.fieldName }}</span>
          </template>
          <template #default="scope">
            <span :title="scope.row[p.fieldName]">{{ scope.row[p.fieldName] || '--' }}</span>
          </template>
        </el-table-column>

        <el-table-column
          v-if="permission.hasButton(['tableInfo_edit', 'tableInfo_del'])"
          label="操作"
          width="80"
          fixed="right"
        >
          <template #default="scope">
            <el-button
              v-if="permission.hasButton('tableInfo_edit')"
              @click="EditDetailRef.handleOpen(scope.row)"
              class="common-icon-btn"
              type="primary"
              plain
              link
            >
              <template #icon>
                <SvgIcon name="edit" />
              </template>
            </el-button>
            <el-button
              v-if="permission.hasButton('tableInfo_del')"
              @click="handleDel(scope.row)"
              title="删除"
              class="common-icon-btn"
              type="danger"
              plain
              link
            >
              <template #icon>
                <SvgIcon name="delete" />
              </template>
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="footer">
      <el-pagination
        v-model:currentPage="pageData.page"
        v-model:page-size="pageData.limit"
        :page-sizes="pageData.pageSizes"
        background
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageData.total"
        @size-change="pageData.handleSizeChange"
        @current-change="pageData.handleCurrentChange"
      />
    </div>
    <EditDetail
      :fieldOptions="fieldOptions"
      @handle-refresh="initData"
      :open-type="2"
      ref="EditDetailRef"
    />
  </div>
</template>

<script setup lang="ts">
import SearchTool from '@/pages/DataIntegration/DataSource/components/SearchTool.vue'
import usePageData from '@/hooks/usePageData'
import { queryPgTableFieldApi, queryPgDetailListApi } from '@/api/data_source'
import { useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ref } from 'vue'
import EditDetail from '@/pages/DataIntegration/DataSource/SourceDataDetail/EditDetail.vue'
import { removeDataApi } from '@/api/data_house'
import useUserInfo from '@/store/useUserInfo'

const permission = useUserInfo()

const route = useRoute()

const { tableData, pageData } = usePageData(queryPgDetailListApi, false)

// 搜索数据
const searchData = ref<any>({})
const handleSearch = (form: any) => {
  searchData.value = form
  initData()
}

const initData = () => {
  const { dataCatalogId, tableName } = route.query
  pageData.handleSearch({ dataCatalogId, tableName, ...searchData.value }, 1)
}

const EditDetailRef = ref<any>()

const handleDel = (map: any) => {
  ElMessageBox.confirm('确定删除此数据表数据吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    try {
      const { dataCatalogId, tableName } = route.query
      const { status, message } = await removeDataApi({ map, dataCatalogId, tableName })
      if ([200].includes(status)) {
        ElMessage.success(message)
        pageData.handleSearch(null, 1)
      } else {
        ElMessage.error(message)
      }
    } catch (error) {
      console.error(error)
    }
  })
}

const fieldOptions = ref<any[]>([])
const getPgTableField = async () => {
  try {
    const { dataCatalogId, tableName } = route.query
    const { data, status, message } = await queryPgTableFieldApi({ dataCatalogId, tableName })
    if ([200].includes(status)) {
      fieldOptions.value = data
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}
getPgTableField()
initData()
</script>

<style scoped lang="less">
.mainLayout {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  :global(.common-table-header .cell) {
    .ellipseLine();
  }
  :global(.common-table-cell .cell) {
    .ellipseLine();
  }
  .header {
    padding: 15px;
    background-color: @withe;
  }
  .main {
    flex: 1;
    background-color: @withe;
    overflow: hidden;
    box-sizing: border-box;
    padding: 0 15px;
  }
  .footer {
    background-color: @withe;
    box-sizing: border-box;
    padding: 15px;
    display: flex;
    justify-content: flex-end;
  }
}
</style>
