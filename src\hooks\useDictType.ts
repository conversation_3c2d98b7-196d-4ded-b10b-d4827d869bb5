import { dictDropDwonApi } from '@/api/common'
import { ElMessage } from 'element-plus'
import { ref } from 'vue'

const useDictType = (type: string) => {
  const dicData = ref<any[]>([])

  const getDict = async () => {
    try {
      const { data, message, status } = await dictDropDwonApi({ code: type })
      if ([200].includes(status)) {
        dicData.value = data
      } else {
        ElMessage.error(message)
      }
    } catch (error) {}
  }

  getDict()

  return dicData
}

export default useDictType
