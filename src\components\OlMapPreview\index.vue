<template>
  <transition name="fade">
    <div v-if="showViewer" class="common-preview-wrapper">
      <div class="tip-wrapper">
        <div></div>
        <p>{{ getFileNameComplete(rowData) }}</p>
        <div class="tip-right">
          <el-select
            size="small"
            style="width: 100px; margin-right: 20px"
            v-model="basemapType"
            placeholder="底图"
            @change="handleChangeMap"
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
          <SvgIcon name="closeBold" @click="handleClose" class="close-icon" />
        </div>
      </div>
      <div class="map-wrapper">
        <div class="olMap" ref="mapRef"></div>
      </div>
    </div>
  </transition>
</template>

<script setup lang="ts">
import { nextTick, ref } from 'vue'
import { userFileListDetailApi } from '@/api/data_catalog'
import useOlMap from '@/hooks/useOlMap'
import { ElMessage } from 'element-plus'
import { getFileNameComplete } from '@/utils/fileUtils'

const showViewer = ref<boolean>(false)
const handleClose = () => {
  showViewer.value = false
}

const { mapRef, handleChangeMap, setPreviewLayers, initMap } = useOlMap(false)

const rowData = ref<any>({})
const handleOpenPreview = async (row: any, isApi: boolean = true) => {
  if (!isApi) {
    setPreviewData(row)
    return
  }
  try {
    const { data, status, message } = await userFileListDetailApi({ tid: row.tid })
    if ([200].includes(status)) {
      setPreviewData(data)
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}

const setPreviewData = (data: any) => {
  const { gisPreviewVO, metadataVO } = data
  const row = {
    bbox: JSON.parse(`[${gisPreviewVO.bbox}]`),
    maxzoom: gisPreviewVO.maxZoom,
    minzoom: gisPreviewVO.minZoom,
    url: gisPreviewVO.url,
    srid: metadataVO.srid,
    ...data,
  }
  rowData.value = row
  showViewer.value = true
  nextTick(() => {
    initMap()
    setPreviewLayers(row)
  })
}

const basemapType = ref<string>('img')
const options: any[] = [
  { value: 'img', label: '星图影像' },
  { value: 'vec', label: '星图矢量' },
]

defineExpose({ handleOpenPreview })
</script>

<style scoped lang="less">
.common-preview-wrapper {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  .tip-wrapper {
    background: rgba(0, 0, 0, 0.5);
    padding: 0 48px;
    width: 100%;
    height: 48px;
    line-height: 48px;
    color: @withe;
    font-size: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-sizing: border-box;
    .tip-right {
      display: flex;
      height: 100%;
      align-items: center;
      .close-icon {
        cursor: pointer;
      }
    }
  }
  .map-wrapper {
    flex: 1;
    box-sizing: border-box;
    padding: 30px 40px;
    .olMap {
      height: 100%;
      width: 100%;
      box-sizing: border-box;
      background-color: rgba(0, 0, 0, 0.9);
    }
  }
}
</style>
