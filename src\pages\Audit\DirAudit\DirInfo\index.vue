<template>
  <div class="DirDetails">
    <div class="breadcrumb">
      <ElBreadcrumb separator="/">
        <ElBreadcrumbItem @click="router.go(-1)" class="link">我审核的</ElBreadcrumbItem>
        <ElBreadcrumbItem>详情</ElBreadcrumbItem>
      </ElBreadcrumb>
    </div>
    <div class="content">
      <div class="info">
        <ElRow :gutter="20" class="row">
          <ElCol :span="8">
            <el-text class="text">提交人：{{ tableData.applyUname }}</el-text>
          </ElCol>
          <ElCol :span="8">
            <el-text class="text">提交时间：{{ tableData.applyTime }}</el-text>
          </ElCol>
          <ElCol :span="24">
            <el-text class="text">描述：{{ tableData.applyDesc }}</el-text>
          </ElCol>
        </ElRow>
        <div class="btn">
          <ElButton
            v-if="route.query.isApproval"
            @click="approvalDialogRef?.handleOpen([route.query.tid])"
            type="primary"
            >审核</ElButton
          >
        </div>
      </div>
      <div class="table">
        <el-table
          header-cell-class-name="common-table-header"
          cell-class-name="common-table-cell"
          ref="multipleTableRef"
          :data="tableData.dataCatalogApplyRelationVOList"
          height="100%"
          style="width: 100%"
          row-key="tid"
          :tree-props="treeProps"
          default-expand-all
        >
          <el-table-column property="catalogAlias" label="目录名称" show-overflow-tooltip />
          <el-table-column property="level" label="目录级别">
            <template #default="scope">{{ scope.row.bsType === 1 ? '菜单' : '数据库' }}</template>
          </el-table-column>
          <el-table-column property="catalogDesc" label="描述" show-overflow-tooltip />
          <el-table-column property="catalogDesc" label="状态">
            <template #default="scope">
              <ElTag v-if="scope.row.editFlag === 2" effect="dark" type="success">新增</ElTag>
              <ElTag v-else-if="scope.row.editFlag === 3" effect="dark" type="warning">修改</ElTag>
              <ElTag v-else-if="scope.row.editFlag === 4" effect="dark" type="danger">删除</ElTag>
              <ElTag v-else effect="dark" type="primary">默认</ElTag>
            </template>
          </el-table-column>
          <el-table-column property="createTime" label="创建时间" show-overflow-tooltip />
        </el-table>
      </div>
    </div>
    <ApprovalDialog ref="approvalDialogRef" @handle-refresh="handleRefresh" />
  </div>
</template>
<script lang="ts" setup>
import { dataCatalogQueryByIdApi } from '@/api/lake_catalog'
import { ElButton, ElMessage } from 'element-plus'
import { ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import ApprovalDialog from './ApprovalDialog.vue'

const route: any = useRoute()
const router = useRouter()

const approvalDialogRef = ref<InstanceType<typeof ApprovalDialog>>()
const handleRefresh = async () => {
  try {
    await router.replace({ name: 'dirInfo', query: { tid: route.query.tid } })
    getDataInfo()
  } catch (error) {}
}

const treeProps = { children: 'childVOList' }

const tableData = ref<any>({})

const getDataInfo = async () => {
  try {
    const { data, message, status } = await dataCatalogQueryByIdApi(route.query.tid)
    if ([200].includes(status)) {
      tableData.value = data
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}

getDataInfo()
</script>
<style lang="less" scoped>
.DirDetails {
  overflow: hidden;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .breadcrumb {
    height: 40px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    line-height: 40px;
    .link {
      :deep(.el-breadcrumb__inner) {
        color: #303133;
        font-weight: bold;
        cursor: pointer;
        &:hover {
          color: var(--el-color-primary);
        }
      }
    }
  }
  .content {
    flex: 1;
    background-color: @withe;
    padding: 15px;
    box-sizing: border-box;
    overflow: hidden;
    .info {
      display: flex;
      .row {
        flex: 1;
        box-sizing: border-box;
        .el-col {
          line-height: 30px;
        }
        .btn {
          margin-left: 30px;
        }
      }
    }
    .table {
      flex: 1;
      box-sizing: border-box;
      overflow: hidden;
    }
  }
}
</style>
