<template>
  <div class="mainLayout">
    <div class="header">
      <SearchTool @handle-search="handleSearch" :fieldOptions="fieldOptions" />
    </div>
    <div class="main">
      <el-table
        v-loading="pageData.loading"
        header-cell-class-name="common-table-header"
        cell-class-name="common-table-cell"
        ref="multipleTableRef"
        :data="tableData"
        height="100%"
        style="width: 100%"
      >
        <el-table-column
          v-for="p in fieldOptions"
          :key="p.fieldName"
          :prop="p.fieldName"
          :label="p.fieldName"
          min-width="150"
        >
          <template #header>
            <span :title="p.fieldName">{{ p.fieldName }}</span>
          </template>
          <template #default="scope">
            <span :title="scope.row[p.fieldName]">{{ scope.row[p.fieldName] || '--' }}</span>
          </template>
        </el-table-column>

        <el-table-column
          v-if="permission.hasButton('sourceDataDetail_edit')"
          label="操作"
          width="80"
          fixed="right"
        >
          <template #default="scope">
            <el-button
              @click="EditDetailRef.handleOpen(scope.row)"
              class="common-icon-btn"
              type="primary"
              plain
              link
            >
              <template #icon>
                <SvgIcon name="edit" />
              </template>
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="footer">
      <el-pagination
        v-model:currentPage="pageData.page"
        v-model:page-size="pageData.limit"
        :page-sizes="pageData.pageSizes"
        background
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageData.total"
        @size-change="pageData.handleSizeChange"
        @current-change="pageData.handleCurrentChange"
      />
    </div>
    <EditDetail :fieldOptions="fieldOptions" @handle-refresh="initData" ref="EditDetailRef" />
  </div>
</template>

<script setup lang="ts">
import SearchTool from '@/pages/DataIntegration/DataSource/components/SearchTool.vue'
import usePageData from '@/hooks/usePageData'
import { queryPgTableFieldApi, queryPgDetailListApi } from '@/api/data_source'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ref, watch } from 'vue'
import EditDetail from './EditDetail.vue'
import useUserInfo from '@/store/useUserInfo'

const permission = useUserInfo()

const route = useRoute()

const { tableData, pageData } = usePageData(queryPgDetailListApi, false)

// 搜索数据
const searchData = ref<any>({})
const handleSearch = (form: any) => {
  searchData.value = form
  initData()
}

const initData = () => {
  const { id, dbname } = route.query
  pageData.handleSearch({ dataSourceId: id, tableName: dbname, ...searchData.value }, 1)
}

const EditDetailRef = ref<any>()

const fieldOptions = ref<any[]>([])
const getPgTableField = async () => {
  try {
    const { id, dbname } = route.query
    const { data, status, message } = await queryPgTableFieldApi({
      dataSourceId: id,
      tableName: dbname,
    })
    if ([200].includes(status)) {
      fieldOptions.value = data
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}

watch(
  () => route.query.dbname,
  (val: any) => {
    if (val) {
      getPgTableField()
      handleSearch({})
    }
  },
  { immediate: true },
)
</script>

<style scoped lang="less">
.mainLayout {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  :global(.common-table-header .cell) {
    .ellipseLine();
  }
  :global(.common-table-cell .cell) {
    .ellipseLine();
  }
  .header {
    padding: 15px;
    background-color: @withe;
  }
  .main {
    flex: 1;
    background-color: @withe;
    overflow: hidden;
    box-sizing: border-box;
    padding: 0 15px;
  }
  .footer {
    background-color: @withe;
    box-sizing: border-box;
    padding: 15px;
    display: flex;
    justify-content: flex-end;
  }
}
</style>
