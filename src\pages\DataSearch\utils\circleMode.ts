import * as turf from '@turf/turf'

const createCircleFeature = (startPos: any, endPos: any) => {
  const radius = calDistance(startPos, endPos)
  return turf.circle(startPos, radius)
}
const calDistance = (start: any, end: any) => {
  let from = turf.point(start)
  let to = turf.point(end)
  return turf.distance(from, to, { units: 'kilometers' })
}

let CircleMode: any = {}

CircleMode.onSetup = function (opts: any) {
  let state = { clickState: false, count: opts.count || 0 }
  return state
}

CircleMode.onClick = function (state: any, e: any) {
  state.clickState = !state.clickState // true: 当前正要点击划半径；false: 画完半径，要创建圆
  if (state.clickState) {
    state.coordinates = [e.lngLat.lng, e.lngLat.lat]
  } else {
    // this.deleteFeature('draw_circle_temp') // 删除鼠标滑动过程中创建及更新的临时圆形
    let circle = this.newFeature(
      createCircleFeature(state.coordinates, [e.lngLat.lng, e.lngLat.lat]),
    )
    this.addFeature(circle)
    // 以下为触发map的draw.create事件，返回创建的图形的Feature，为了跟其他已有模式统一动作
    let coordinates = JSON.parse(JSON.stringify(circle.coordinates))
    // 生成闭合图形，根据业务需要使用；不需要的话就没必要作此处理
    coordinates[0].push(coordinates[0][0])
    const feature = JSON.parse(
      JSON.stringify({
        id: circle.id,
        type: 'Feature',
        geometry: {
          type: 'Polygon',
          properties: {},
          coordinates: coordinates,
        },
      }),
    )
    this.map.fire('draw.create', { features: [feature] })
    return this.changeMode('simple_select')
  }
}

CircleMode.onMouseMove = function (state: any, e: any) {
  if (state.clickState) {
    // 创建半径随鼠标移动而改变的临时圆形
    let tempFeature = this.getFeature('draw_circle_temp')
    if (!tempFeature) {
      this.addFeature(
        this.newFeature(
          Object.assign(createCircleFeature(state.coordinates, [e.lngLat.lng, e.lngLat.lat]), {
            id: 'draw_circle_temp',
          }),
        ),
      )
    } else {
      let newFeature: any = createCircleFeature(state.coordinates, [e.lngLat.lng, e.lngLat.lat])
      // draw的Feature类自带的setProperty（更新单个属性），setCoordinates（更新全部坐标）
      tempFeature.setProperty('radiusInKm', newFeature.properties.radiusInKm)
      tempFeature.setCoordinates(newFeature.geometry.coordinates)
    }
  }
}

CircleMode.toDisplayFeatures = function (_state: any, geojson: any, display: any) {
  display(geojson)
}

export default CircleMode
