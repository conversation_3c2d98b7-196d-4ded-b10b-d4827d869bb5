<template>
  <div class="tableData">
    <div class="header">
      <SvgIcon @click="emit('handleClick', 'handleBack')" class="icon" name="arrowLeft" />
      <div>
        <ElButton
          v-if="tableData.length && permssion.hasButton('dataSearch-export')"
          @click="handleExport"
          class="btn"
          type="primary"
          size="small"
        >
          <template #icon>
            <SvgIcon name="upload" />
          </template>
          导出
        </ElButton>
        <ElButton
          v-if="searchType"
          @click="handleBatchDownload"
          class="btn"
          type="primary"
          size="small"
        >
          <template #icon>
            <SvgIcon name="download" />
          </template>
          批量下载
        </ElButton>
      </div>
    </div>
    <div class="tableRef">
      <el-table
        @selection-change="handleSelectionChange"
        v-loading="pageData.loading"
        header-cell-class-name="table-header"
        cell-class-name="table-cell"
        ref="multipleTableRef"
        :data="tableData"
        height="100%"
        style="width: 100%"
        row-key="id"
      >
        <el-table-column type="selection" width="30" />
        <el-table-column property="name" label="名称" show-overflow-tooltip>
          <template #default="scope">
            {{
              scope.row.properties.scene_type || ['iot'].includes(scope.row.properties.queryType)
                ? scope.row.properties.file_name
                : `${scope.row.properties.file_name}.${scope.row.properties.suffix}`
            }}
          </template>
        </el-table-column>
        <el-table-column label="数据类型" show-overflow-tooltip width="90px">
          <template #default="scope">
            {{ scope.row.properties?.file_type_name || '--' }}
          </template>
        </el-table-column>
        <el-table-column property="mobile" label="大小" show-overflow-tooltip width="90px">
          <template #default="scope">
            {{
              scope.row.properties.file_size
                ? `${calculateFileSize(Number(scope.row.properties.file_size))}`
                : '--'
            }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template #default="scope">
            <el-button
              class="icon"
              type="primary"
              @click="emit('handleClick', 'handlePreview', scope.row.properties)"
              plain
              link
            >
              <template #icon>
                <SvgIcon name="view" />
              </template>
            </el-button>
            <el-button
              class="icon"
              type="primary"
              @click="emit('handleClick', 'handleDetails', scope.row.properties)"
              plain
              link
            >
              <template #icon>
                <SvgIcon name="details" />
              </template>
            </el-button>
            <el-button
              v-if="!['iot'].includes(scope.row.properties.queryType)"
              class="icon"
              type="primary"
              @click="emit('handleClick', 'handleDownload', scope.row.properties)"
              plain
              link
              title="下载"
            >
              <template #icon>
                <SvgIcon name="download" />
              </template>
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="page">
      <el-pagination
        size="small"
        v-model:currentPage="pageData.startIndex"
        v-model:page-size="pageData.maxFeatures"
        :page-sizes="pageData.pageSizes"
        layout="prev, pager, next, jumper"
        :background="false"
        :total="pageData.total"
        @size-change="pageData.handleSizeChange"
        @current-change="pageData.handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { downloadFileApi } from '@/api/common'
import useUserInfo from '@/store/useUserInfo'
import { calculateFileSize } from '@/utils/fileUtils'
import { ElButton, ElMessage } from 'element-plus'
import { ref } from 'vue'

const permssion = useUserInfo()

const selectData = ref<any[]>()
const handleSelectionChange = (list: any[]) => {
  selectData.value = list.map((item: any) => item.properties.user_file_id)
}

const handleBatchDownload = async () => {
  if (!selectData.value?.length) return ElMessage.warning('请选择要下载的数据!')
  selectData.value.forEach((item) => {
    window.open(`${downloadFileApi()}?tid=${item}&token=${permssion.token}`, '_blank')
  })
}

defineProps<{ tableData: any[]; pageData: any; handleExport: any; searchType: number }>()

const emit = defineEmits<{
  handleClick: [name: string, row?: any]
}>()
</script>

<style scoped lang="less">
.tableData {
  height: 60vh;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  background-color: rgba(37, 45, 75, 0.8);
  padding: 0 20px;
  .header {
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .icon {
      color: rgba(141, 151, 155, 1);
      font-size: 18px;
      cursor: pointer;
    }
    .btn {
      background-color: transparent;
      border: 1px solid var(--el-color-primary);
      color: var(--el-color-primary);
      &:hover {
        background-color: var(--el-color-primary);
        color: @withe;
      }
      &:active {
        background-color: var(--el-color-primary);
      }
    }
  }
  .tableRef {
    height: calc(60vh - 80px);
    box-sizing: border-box;
    :deep(.el-table) {
      background-color: transparent !important;
      .el-table__inner-wrapper::before {
        background-color: rgba(51, 57, 81, 0.5);
      }
      tr,
      .el-table__body-wrapper tr td.el-table-fixed-column--right {
        background-color: transparent !important;
      }
      td.el-table__cell,
      th.el-table__cell.is-leaf {
        border-bottom: 1px solid rgba(51, 57, 81, 0.5);
      }
      .el-table__body tr:hover > td.el-table__cell {
        background-color: transparent !important;
      }
      .el-checkbox__inner {
        background-color: transparent;
        border: 1px solid #8d979b;
        &:hover {
          border-color: var(--el-color-primary);
        }
      }
      .el-checkbox__input.is-checked .el-checkbox__inner,
      .el-checkbox__input.is-indeterminate .el-checkbox__inner {
        background-color: var(--el-checkbox-checked-bg-color);
        border-color: var(--el-checkbox-checked-input-border-color);
      }
    }
  }

  .page {
    height: 40px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    :deep(.el-pagination) {
      background-color: transparent;
      button {
        background-color: transparent !important;
        color: rgba(141, 151, 155, 1);
        &:hover {
          color: var(--el-color-primary);
        }
      }
      button:disabled {
        color: var(--el-pagination-button-disabled-color);
      }
      .el-pager {
        li {
          background-color: transparent;
          color: rgba(141, 151, 155, 1);
          &:hover {
            color: var(--el-color-primary);
          }
        }
        li.is-active {
          background-color: var(--el-color-primary);
          color: @withe;
        }
      }
      .el-pagination__jump {
        span {
          color: rgba(141, 151, 155, 1);
        }
        .el-input__wrapper {
          background-color: transparent;
          box-shadow: none;
          border: 1px solid rgba(141, 151, 155, 1);
          .el-input__inner {
            color: rgba(141, 151, 155, 1);
          }
        }
      }
    }
  }
  .icon {
    font-size: 16px;
  }
}

:global(.table-header) {
  height: 40px;
  font-size: 14px;
  font-family: SourceHanSansCN-Regular, SourceHanSansCN;
  font-weight: 400 !important;
  color: #b1e3ff;
  background-color: rgba(37, 45, 75, 1) !important;
}
:global(.table-cell) {
  height: 40px;
  color: #8799b7;
}
</style>
