import { TrellisCode } from '@/types/mapbox'
import { reactive } from 'vue'
import { getBdJsonApi, getGridApi } from '@/api/data_search'

export default function useTrellisCode(map: any) {
  const codeData = reactive<TrellisCode['codeData']>({
    // 选中的网格码数据
    selectedSquare: [],
    // 是否可选择网格码
    isSelectCode: false,
    // 是否是geosot网格码
    isGeoSot: false,
  })
  // 设置是否可选择网格码
  const setIsSelectCode: TrellisCode['setIsSelectCode'] = (isSelectCode) => {
    codeData.isSelectCode = isSelectCode
  }

  // 设置是否可选择网格码
  const setTrellisCode: TrellisCode['setTrellisCode'] = async () => {
    try {
      const { _ne, _sw }: any = map.value?.getBounds()
      const zoom: any = map.value?.getZoom()
      const params = [_sw.lng, _sw.lat, _ne.lng, _ne.lat, zoom]
      const { data, status } = codeData.isGeoSot
        ? await getGridApi(params)
        : await getBdJsonApi(params)
      if ([200].includes(status)) {
        const grid: any = map.value?.getSource('trellis')
        if (grid) {
          grid.setData(data)
        } else {
          map.value?.addSource('trellis', {
            type: 'geojson',
            data: data,
          })
          map.value?.addLayer({
            id: 'measure-box-grid',
            type: 'fill',
            source: 'trellis',
            paint: {
              'fill-color': 'rgb(255, 255, 255)',
              'fill-opacity': 0,
            },
          })
          map.value?.addLayer({
            id: 'measure-box-line',
            type: 'line',
            source: 'trellis',
            paint: {
              'line-color': 'white',
              'line-width': 2,
            },
          })
        }
      }
    } catch (error) {}
  }

  // 分区加载网格码事件
  const handleMoveend: TrellisCode['handleMoveend'] = () => {
    setTrellisCode()
  }

  // 点击事件
  const handleClickEvent: TrellisCode['handleClickEvent'] = (e: any) => {
    if (!codeData.isSelectCode) return
    const features: any[] | any = map.value?.queryRenderedFeatures(e.point)
    if (!features || !features.length) return
    const featIndex: number = codeData.selectedSquare.findIndex(
      (item: any) => item.properties.code === features[0].properties.code,
    )
    if (featIndex === -1) {
      codeData.selectedSquare.push(features[0])
    } else {
      codeData.selectedSquare.splice(featIndex, 1)
    }
    const square: any = map.value?.getSource('clicked-grid-square')
    if (square) {
      square.setData({ type: 'FeatureCollection', features: codeData.selectedSquare })
    } else {
      map.value?.addSource('clicked-grid-square', {
        type: 'geojson',
        data: {
          type: 'FeatureCollection',
          features: codeData.selectedSquare,
        },
      })
      map.value?.addLayer({
        id: 'clicked-grid-square',
        type: 'fill',
        source: 'clicked-grid-square',
        paint: {
          'fill-color': 'orange',
          'fill-opacity': 0.5,
        },
      })
    }
  }

  // 清除网格码
  const clearSelectedSquare: TrellisCode['clearSelectedSquare'] = () => {
    map.value?.getLayer('clicked-grid-square') && map.value?.removeLayer('clicked-grid-square')
    map.value?.getSource('clicked-grid-square') && map.value?.removeSource('clicked-grid-square')
    codeData.selectedSquare = []
  }
  const clearTrelisCode: TrellisCode['clearTrelisCode'] = () => {
    if (!map.value) return
    map.value?.off('moveend', handleMoveend)
    map.value?.off('click', handleClickEvent)
    map.value?.getLayer('measure-box-grid') && map.value?.removeLayer('measure-box-grid')
    map.value?.getLayer('measure-box-line') && map.value?.removeLayer('measure-box-line')
    map.value?.getSource('trellis') && map.value?.removeSource('trellis')
    clearSelectedSquare()
  }

  // 初始化网格码
  const initTrellisCode: TrellisCode['initTrellisCode'] = (isGeoSot) => {
    console.log(map, 'map')
    if (!map.value) return
    codeData.isGeoSot = isGeoSot
    setTrellisCode()
    map.value?.on('moveend', handleMoveend)
    map.value?.on('click', handleClickEvent)
  }
  return { codeData, setIsSelectCode, initTrellisCode, clearSelectedSquare, clearTrelisCode }
}
