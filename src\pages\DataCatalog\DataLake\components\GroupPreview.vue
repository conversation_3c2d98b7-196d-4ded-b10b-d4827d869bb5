<template>
  <ElDialog
    title="图层组预览"
    width="800px"
    v-model="dialogFormVisible"
    :close-on-click-modal="false"
    @opened="handlePreview"
    @close="handleClose"
  >
    <div ref="mapRef" class="groupPreview"></div>
    <template #footer>
      <ElButton @click="setDialogFormVisible(false)">取消</ElButton>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
import useDialogForm from '@/hooks/useDialogForm'
import useMapbox from '@/hooks/useMapbox'
import { getTransformExtentData } from '@/utils/mapConfig'
import { ElDialog } from 'element-plus'
import { ref } from 'vue'
const { setDialogFormVisible, dialogFormVisible } = useDialogForm()

const { mapRef, initMap, map } = useMapbox('mercator')

const handlePreview = () => {
  initMap(() => {
    const {
      SERVICE,
      VERSION,
      REQUEST,
      FORMAT,
      TRANSPARENT,
      LAYERS,
      STYLES,
      SRS,
      style,
      srid,
      bbox,
    } = previewData.value
    const bbox4326: any = getTransformExtentData(`EPSG:${srid}`, bbox, 'EPSG:4326')
    map.value?.addSource('mapTiff', {
      type: 'raster',
      tiles: [
        `${import.meta.env.VITE_BASE_SERVER}/wms?SERVICE=${SERVICE}&VERSION=${VERSION}&REQUEST=${REQUEST}&FORMAT=${FORMAT}&TRANSPARENT=${TRANSPARENT}&LAYERS=${LAYERS}&BBOX={bbox-epsg-3857}&STYLES=${STYLES}&SRS=${SRS}&style=${style}&token=c6147141a761f1af9dfac0c6b69a8f62&WIDTH=256&HEIGHT=256`,
      ],
      tileSize: 256,
    })
    map.value?.addLayer({
      id: 'mapTiff',
      type: 'raster',
      source: 'mapTiff',
      paint: {
        // 'raster-opacity': 1,
      },
    })

    map.value?.fitBounds(bbox4326, {})
  })
}

const handleClose = () => {
  map.value?.remove()
}

const previewData = ref<any>({})
const handleOpen = (params: any) => {
  previewData.value = params
  setDialogFormVisible(true)
}

defineExpose({ handleOpen })
</script>
<style lang="less" scoped>
.groupPreview {
  height: 350px;
}
</style>
