import { onMounted, reactive, ref } from 'vue'
import Map from 'ol/Map'
import View from 'ol/View'
import XYZ from 'ol/source/XYZ'
import TileLayer from 'ol/layer/Tile'
import { getBaseMap, center, getTransformExtentData, projection } from '@/utils/mapConfig'

export default function useOlMap(isInit: boolean = true) {
  const mapRef = ref<any>()
  const map = ref<Map | null>(null)
  const olObj = reactive<any>({
    projection,
    baseMapLayer: null
  })

  //切换判断底图
  const handleChangeMap = (type: string) => {
    olObj.baseMapLayer.setSource(getXYZSource(type))
  }

  // 加载星图地图
  const getXYZSource = (type?: any) => {
    return new XYZ({ url: getBaseMap(type) })
  }

  // 加载其他服务预览
  const setPreviewLayers = (row: any) => {
    const { bbox, maxZoom, minZoom, srid, url } = row
    const bbox3857 = getTransformExtentData(`EPSG:${srid}`, bbox)
    map.value?.addLayer(
      new TileLayer({
        source: new XYZ({
          url: `${import.meta.env.VITE_BASE_SERVER}/${url}`,
          maxZoom,
          minZoom,
          projection: olObj.projection
        })
      })
    )
    map.value?.getView().fit(bbox3857 || [], map.value.getSize() as any)
  }

  // 初始化地图
  const initMap = () => {
    olObj.baseMapLayer = new TileLayer({ source: getXYZSource() })
    map.value = new Map({
      target: mapRef.value,
      layers: [],
      view: new View({
        projection: olObj.projection,
        center,
        zoom: 1
      })
    })
    map.value.addLayer(olObj.baseMapLayer)
  }

  onMounted(() => {
    if (isInit) {
      initMap()
    }
  })

  return { map, mapRef, initMap, handleChangeMap, setPreviewLayers }
}
