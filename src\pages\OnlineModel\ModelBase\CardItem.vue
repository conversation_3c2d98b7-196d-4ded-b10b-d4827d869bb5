<template>
  <div class="cardItem">
    <div
      @click="emit('handleEdit', card, card.isModify)"
      class="card-top"
      :style="{ 'background-image': `url(${card.thumbnailUrl || modelBg})` }"
    >
      <p>{{ card.createTime || card.updateTime }}</p>
      <span> {{ card.isModify ? '我的模型' : '系统内置' }} </span>
    </div>
    <div class="cardBottom">
      <p>{{ card.modelName }}</p>
      <p>备注：{{ card.notes }}</p>
      <el-button
        v-if="card.isModify"
        @click="SaveModelRef.handleOpen(0, card)"
        class="common-icon-btn"
        type="primary"
        plain
        link
      >
        <template #icon>
          <SvgIcon name="editPen" />
        </template>
      </el-button>
      <div class="line"></div>
      <div class="btnList">
        <span
          v-if="permission.hasButton('modelBase_open')"
          @click="emit('handleEdit', card, card.isModify)"
          >打开</span
        >
        <template v-if="card.isModify && permission.hasButton('modelBase_del')">
          <span @click="emit('handleDel', card)" class="del">删除</span>
        </template>
      </div>
    </div>
    <SaveModelDialog @handle-submit="handleSubmit" ref="SaveModelRef" />
  </div>
</template>

<script setup lang="ts">
import modelBg from '@/assets/commonImg/modelBg.png'
import SaveModelDialog from '@/pages/OnlineModel/components/SaveModelDialog.vue'
import { ref } from 'vue'
import { modifyModelByIdApi } from '@/api/online_model'
import { ElMessage } from 'element-plus'
import useUserInfo from '@/store/useUserInfo'

const permission = useUserInfo()

defineProps<{ card: any }>()

const SaveModelRef = ref<any>(null)
const handleSubmit = async (setDialogFormVisible: any, formData: any) => {
  try {
    const { status, message } = await modifyModelByIdApi(formData)
    if ([200].includes(status)) {
      ElMessage.success(message)
      emit('handleRefresh')
      setDialogFormVisible(false)
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}

const emit = defineEmits<{
  handleEdit: [row: any, isModify: number]
  handleDel: [row: any]
  handleRefresh: []
}>()
</script>

<style lang="less" scoped>
.cardItem {
  // width: 270px;
  height: 276px;
  background-color: @withe;
  box-sizing: border-box;
  box-shadow: 0px 0px 7px 1px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(0, 0, 0, 0.05);
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  .common-icon-btn {
    position: absolute;
    transition: all 0.5s;
    right: 15px;
    top: 10px;
    opacity: 0;
  }
  &:hover {
    .common-icon-btn {
      opacity: 1 !important;
    }
  }
  .card-top {
    margin: 5px;
    height: 155px;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    position: relative;
    &:hover {
      cursor: pointer;
    }
    > p {
      position: absolute;
      bottom: 10px;
      left: 10px;
      color: @withe;
      font-size: 12px;
      text-shadow: 0px 0px 2px rgba(36, 36, 36, 0.9);
    }
    > span {
      position: absolute;
      top: 10px;
      right: 10px;
      padding: 3px 10px;
      display: inline-block;
      background: #1f56b8;
      border-radius: 10px;
      font-size: 12px;
      font-weight: 400;
      color: @withe;
    }
  }
  .cardBottom {
    flex: 1;
    box-sizing: border-box;
    padding: 10px 15px;
    position: relative;
    p {
      font-size: 12px;
      font-weight: 400;
      color: #666666;
      line-height: 24px;
      padding-right: 20px;
      .ellipseLine();
    }

    .line {
      height: 1px;
      background: #dbdbdb;
      margin-top: 10px;
    }
    .btnList {
      margin-top: 15px;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      > span {
        text-align: center;
        line-height: 16px;
        width: 46px;
        height: 16px;
        border-radius: 8px 8px 8px 8px;
        border: 1px solid var(--el-color-primary);
        font-size: 12px;
        color: var(--el-color-primary);
        cursor: pointer;
      }
      span + span {
        margin-left: 10px;
      }
      .del {
        border: 1px solid #ff7070;
        color: #ff7070;
      }
    }
  }
}
</style>
