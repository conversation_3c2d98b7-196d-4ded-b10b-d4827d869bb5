<template>
  <div class="modelBase">
    <NavBar @handle-node-click="handleNodeClick" />
    <div class="main">
      <div class="searchTool">
        <el-input
          v-model="formData.likeModelName"
          placeholder="请输入模型名称"
          style="width: 300px"
          @keydown.enter="pageData.handleSearch(formData, 0)"
        >
          <template #suffix>
            <SvgIcon
              @click="pageData.handleSearch(formData, 0)"
              style="cursor: pointer"
              name="search"
            />
          </template>
        </el-input>
      </div>
      <div class="mainContent">
        <el-scrollbar>
          <div class="cardList">
            <CardItem
              v-for="p in tableData"
              @handle-refresh="pageData.handleSearch(formData, 0)"
              @handle-del="handleDel"
              @handle-edit="handleEdit"
              :key="p.tid"
              :card="p"
            />
          </div>
        </el-scrollbar>
      </div>
      <div class="mainFooter">
        <el-pagination
          v-model:currentPage="pageData.page"
          v-model:page-size="pageData.limit"
          :page-sizes="pageData.pageSizes"
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="pageData.total"
          @size-change="pageData.handleSizeChange"
          @current-change="pageData.handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import NavBar from '@/pages/OnlineModel/ModelBase/NavBar.vue'
import CardItem from '@/pages/OnlineModel/ModelBase/CardItem.vue'
import { reactive } from 'vue'
import usePageData from '@/hooks/usePageData.ts'
import { queryPageByParamApi, removeByIdApi } from '@/api/online_model'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'

const router = useRouter()

// 删除
const handleDel = async ({ tid }: any) => {
  ElMessageBox.confirm('确定删除该模型？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    try {
      const { status, message } = await removeByIdApi(tid)
      if ([200].includes(status)) {
        ElMessage.success(message || '删除成功！')
        pageData.handleSearch(formData, 0)
      } else {
        ElMessage.error(message)
      }
    } catch (error) {}
  })
}

// 编辑使用
const handleEdit = (row: any, isModify: number) => {
  router.push({ name: 'createModel', query: { tid: row.tid, isModify } })
}

const formData = reactive<any>({
  likeModelName: '',
  modelTypeId: '',
})
const { pageData, tableData } = usePageData(queryPageByParamApi, false)
// 节点查询
const handleNodeClick = (tid: any) => {
  formData.modelTypeId = tid
  pageData.handleSearch({ ...formData }, 0)
}
</script>

<style scoped lang="less">
.modelBase {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  overflow: hidden;
  display: flex;

  .main {
    margin-left: 20px;
    flex: 1;
    background-color: @withe;
    box-sizing: border-box;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    padding: 0 20px;
    box-shadow: 0px 0px 7px 1px rgba(0, 0, 0, 0.05);
    .searchTool {
      height: 32px;
      margin: 15px 0;
    }
    .mainContent {
      flex: 1;
      overflow: hidden;
      box-sizing: border-box;

      .cardList {
        display: grid;
        justify-content: space-evenly;
        grid-template-columns: repeat(auto-fill, minmax(270px, 1fr));
        grid-gap: 15px 10px;
        padding: 0 0 15px 0;
      }
    }
    .mainFooter {
      box-sizing: border-box;
      margin: 15px 0;
      display: flex;
      justify-content: flex-end;
    }
  }
}
</style>
