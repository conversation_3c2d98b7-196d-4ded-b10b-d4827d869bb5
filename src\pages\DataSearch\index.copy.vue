<template>
  <div class="dataSearch">
    <div class="mapbox" ref="mapRef">
      <!-- 时序场景时间线 -->
      <MapboxTimeLine
        v-show="sceneData.rowList.length"
        @hanlde-is-paly="hanldeIsPaly"
        :palyList="sceneData.rowList"
        :currentIndex="sceneData.currentIndex"
        :isPlay="sceneData.isPlay"
      />
    </div>
    <!-- 搜索框 -->
    <SearchTool
      @handle-reset-map="handleResetMap"
      @handle-tool="handleTool"
      @handle-preview="handleLayersPreview"
      :coordinateObj="coordinateObj"
      :trellisCode="trellisCode"
    />
    <!-- 右下角工具栏 -->
    <IconTool @handle-icon="handleIcon" />

    <!-- 切换地图影像、矢量底图 -->
    <div class="change-baseMap-box">
      <div
        v-for="p in baseMapList"
        :key="p.type"
        :class="{ active: [p.type].includes(baseMapType) }"
        @click="handleChangeBaseMap(p.type)"
      >
        <img :src="p.url" />
        <span>{{ p.name }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import SearchTool from './SearchTool/index.vue'
import IconTool from './IconTool.vue'
import useMapbox from '@/hooks/useMapbox'
import { userFileListDetailApi } from '@/api/data_catalog'
import { ElMessage } from 'element-plus'
import yx from '@/pages/DataSearch/images/yx.png'
import sl from '@/pages/DataSearch/images/sl.png'
import { ref, h, onMounted } from 'vue'
import MapboxTimeLine from '@/components/MapboxTimeLine/index.vue'
import useScenePreview from '@/hooks/useScenePreview'
import { querySceneDetailApi } from '@/api/sequential_scene'
import { useRouter } from 'vue-router'
import useTrellisCode from '@/hooks/useTrellisCode'

const router = useRouter()

//切换地图影像、矢量底图
const baseMapList = [
  {
    name: '星图影像',
    type: 'img',
    url: yx,
  },
  {
    name: '星图矢量',
    type: 'vec',
    url: sl,
  },
]
const baseMapType = ref<string>('img')
const handleChangeBaseMap = (type: string) => {
  baseMapType.value = type
  handleChangeMap(type)
}

const {
  map,
  mapRef,
  mapOperate,
  mapTools,
  initMap,
  initMapDraw,
  setPreviewLayers,
  handleChangeMap,
  clearPreviewLayers,
} = useMapbox()

const { sceneData, handleScenePreview, handlePreview, hanldeIsPaly, clearData } =
  useScenePreview(setPreviewLayers)

const trellisCode = useTrellisCode(map)
const coordinateObj = ref<any>()

onMounted(() => {
  initMap(() => {
    initMapDraw((e: any) => {
      console.log(e, 'e')
      coordinateObj.value = e.features[0].geometry.coordinates
    })
  })
})

// 重置所有数据
const handleResetMap = (isDel?: boolean) => {
  console.log(isDel)
  trellisCode.clearSelectedSquare()
  mapTools.handleClearTools()
  clearPreviewLayers()
  clearData()
}

// 右下角按钮处理
const handleIcon = (key: string) => {
  switch (key) {
    case 'handleClear':
      handleResetMap()
      break
    case 'handleHeatMap':
      break
    case 'handleReset':
      mapOperate.resetMap()
      break
    case 'handleAdd':
      mapOperate.zoomInMap()
      break
    default:
      mapOperate.zoomOutMap()
      break
  }
}

// 图形工具
const handleTool = (key: any) => {
  console.log(key)
  switch (key) {
    case '0':
      mapTools.drawTools.addRectangle()
      return
    case '1':
      mapTools.drawTools.addCircle()
      return
    default:
      mapTools.drawTools.addPolygon()
      return
  }
}

// 普通图层预览
const ordinaryLayerPreview = async (user_file_id: any) => {
  try {
    const { data, status, message } = await userFileListDetailApi({ tid: user_file_id })
    if ([200].includes(status) && data) {
      if ([0].includes(data.serverStatus)) {
        ElMessage.warning('服务发布失败，暂时无法预览!')
        return
      }
      if ([2].includes(data.serverStatus)) {
        ElMessage.warning('服务发布中，请稍后预览!')
        return
      }
      if ([3].includes(data.serverStatus)) {
        ElMessage({
          message: h('p', [
            h('span', null, '服务暂未发布，暂时无法预览! '),
            h(
              'i',
              {
                style: 'color: #598bfd;cursor: pointer;',
                onClick: () => {
                  router.push({ name: 'dataCatalog' })
                },
              },
              '发布服务',
            ),
          ]),
          type: 'warning',
        })

        return
      }
      handlePreview(data)
    } else {
      ElMessage.error(message || '预览失败！')
    }
  } catch (error) {}
}

// 时序图层预览
const timeSequenceLayerPreview = async (user_file_id: any) => {
  try {
    const { data, status, message } = await querySceneDetailApi({ tid: user_file_id })
    if ([200].includes(status)) {
      handleScenePreview(
        data.fileList.map((item: any) => ({
          fileName: item.fileName,
          sceneTimeFormat: item.sceneTimeFormat,
          tid: item.userFileId,
          gisPreviewVO: item.detail.gisPreviewVO,
          metadataVO: item.detail.metadataVO,
        })),
        data.interval,
      )
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}

// 图层预览
const handleLayersPreview = async ({ user_file_id, scene_type }: any) => {
  clearData()
  if (scene_type) {
    timeSequenceLayerPreview(user_file_id)
    return
  }
  ordinaryLayerPreview(user_file_id)
}
</script>

<style scoped lang="less">
.dataSearch {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  position: relative;
  .mapbox {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    position: relative;
    :global(.mapboxgl-ctrl-bottom-left) {
      display: none;
    }
  }

  .change-baseMap-box {
    position: absolute;
    z-index: 100;
    bottom: 0;
    right: 20px;
    background-color: #293a5e;
    white-space: nowrap;
    width: 85px;
    transition: width 0.5s;
    overflow: hidden;
    padding: 4px;
    &:hover {
      width: 180px;
    }
    div {
      display: inline-block;
      position: relative;
      height: 60px;
      width: 86px;
      cursor: pointer;
      img {
        width: 100%;
        height: 100%;
      }
      span {
        position: absolute;
        right: 0px;
        bottom: 0px;
        color: @withe;
        background: #5994f1;
        font-size: 12px;
        padding: 3px 5px;
      }
    }
    div + div {
      margin-left: 5px;
    }
    div.active {
      border: 1px solid #5994f1;
    }
  }
}
</style>
