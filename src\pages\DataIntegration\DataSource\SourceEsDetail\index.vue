<template>
  <div class="SourceEsDBDetail">
    <Breadcrumb />
    <div class="SourceEsDBDetail-main">
      <TableList
        v-if="route.query.dbname"
        :permission="permission.hasButton('sourceEsDetail_edit')"
      />
      <DataBaseDetail v-else :rowData="rowData" />
    </div>
  </div>
</template>

<script setup lang="ts">
import Breadcrumb from '@/pages/DataIntegration/DataSource/components/Breadcrumb.vue'
import DataBaseDetail from '@/pages/DataIntegration/DataSource/components/DataBaseDetail.vue'
import TableList from '@/pages/DataIntegration/DataSource/components/TableList.vue'
import { useRoute } from 'vue-router'
import { queryByIdApi } from '@/api/data_source'
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import useUserInfo from '@/store/useUserInfo'

const permission = useUserInfo()

const route = useRoute()

// 获取详情
const rowData = ref<any>({})
const getDataDetail = async () => {
  try {
    const { data, status, message } = await queryByIdApi(route.query.id)
    if ([200].includes(status)) {
      rowData.value = data
    } else {
      ElMessage.error(message)
    }
  } catch (error) {
    console.error(error)
  }
}
getDataDetail()
</script>

<style scoped lang="less">
.SourceEsDBDetail {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  .SourceEsDBDetail-main {
    margin-top: 15px;
    flex: 1;
    display: flex;
    box-sizing: border-box;
    overflow: hidden;
    background-color: @withe;
  }
}
</style>
