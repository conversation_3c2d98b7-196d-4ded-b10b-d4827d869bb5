<template>
  <div class="time-line">
    <div class="icon" @click="emit('hanldeIsPaly')">
      <SvgIcon :name="props.isPlay ? 'videoPause' : 'videoPlay'" />
    </div>
    <el-steps class="step-line" :active="props.currentIndex + 1" align-center>
      <el-step v-for="p in props.palyList" :key="p.tid" :title="p.sceneTimeFormat">
        <template #icon><div></div></template>
      </el-step>
    </el-steps>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

const props = defineProps<{ isPlay: boolean; palyList: any[]; currentIndex: number }>()

const width = computed(() => {
  return `-${100 / props.palyList.length / 2}%`
})

// const currentIndex = () => props.currentIndex + 1

const emit = defineEmits<{
  (e: 'hanldeIsPaly'): void
}>()
</script>

<style lang="less" scoped>
.time-line {
  position: absolute;
  width: 80%;
  bottom: 40px;
  left: 0;
  right: 0;
  box-sizing: border-box;
  margin: 0 auto;
  height: 60px;
  z-index: 50;
  background: rgba(74, 74, 74, 0.7);
  padding: 0 40px;
  border-radius: 6px 6px 6px 6px;
  display: flex;
  align-items: center;
  .icon {
    font-size: 32px;
    color: @withe;
    cursor: pointer;
    z-index: 100;
  }
  .step-line {
    flex: 1;
    overflow: hidden;
    box-sizing: border-box;
    padding-left: 20px;
    :deep(.el-step__head) {
      .el-step__icon {
        height: 10px;
        width: 10px;
        border-radius: 50px;
      }
    }
    :deep(.el-step__head.is-finish) {
      .el-step__icon {
        background-color: var(--el-color-primary);
      }
    }
  }
  :deep(.el-steps) {
    margin: 0 v-bind(width);
    .el-step__head.is-wait {
      top: 0;
    }
    .el-step__line {
      background-color: @withe;
      top: 13px;
    }
    .el-step__title {
      font-size: 12px;
      font-weight: 400;
      color: @withe;
      line-height: 24px;
    }
    .el-step__title.is-finish {
      color: var(--el-color-primary);
    }
  }
}
</style>
