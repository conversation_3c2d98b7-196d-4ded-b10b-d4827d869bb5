import { Marker } from 'mapbox-gl'

// 工具栏
export interface MapTools {
  type:
    | 'mapLocation'
    | 'mapLine'
    | 'mapRange'
    | 'mapArea'
    | 'mapPolygon'
    | 'mapRectangle'
    | 'mapCircle'
    | ''
  drawTools: DrawTools
  handleToolsEvents: (type: MapTools['type']) => void
  handleClearTools: () => void
}

// 标点
export interface MarkerTools {
  markers: Marker | null
  addMarker: (polygon: [number, number], color?: string) => void
  clearedMarker: () => void
}

export interface DrawTools {
  drawingMode: string
  singleClear: (id: string) => void
  addPolygon: () => void
  addCircle: () => void
  addLine: () => void
  addRectangle: () => void
  addPoint: () => void
  clearDraw: () => void
}

// 印象裁剪
export interface ImageCut {
  imageCutData: {
    type: string
    bbox: any[]
    markers: any
    isElement: boolean
  }
  handleImageCut: (type: string) => void
  addMarker: (polygon: any) => void
  clearedImageCut: () => void
}

// 网格码
export interface TrellisCode {
  codeData: {
    selectedSquare: any[]
    isSelectCode: boolean
    isGeoSot: boolean
  }
  setIsSelectCode: (val: boolean) => void
  initTrellisCode: (isGeoSot: boolean) => void
  handleMoveend: () => void
  handleClickEvent: (e: any) => void
  setTrellisCode: () => Promise<void>
  clearTrelisCode: () => void
  clearSelectedSquare: () => void
}
