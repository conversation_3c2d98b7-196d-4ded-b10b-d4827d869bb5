<template>
  <div class="image-upload">
    <el-upload
      class="avatar-uploader"
      :action="action || uploadThumbnailApi()"
      :show-file-list="false"
      :on-success="handleAvatarSuccess"
      :before-upload="beforeAvatarUpload"
      :accept="'.png,.jpg,.jpeg,.svg,.webp'"
      :headers="{
        token: userInfo.token,
      }"
    >
      <img v-if="modelValue" :src="modelValue" class="avatar" />
      <el-icon v-else class="avatar-uploader-icon"><SvgIcon name="add" /></el-icon>
    </el-upload>
    <div class="el-upload__tip">
      <ElIcon class="icon">
        <SvgIcon name="WarningFilled" />
      </ElIcon>
      <div class="text">
        <p>温馨提示：上传文件后可修改</p>
        <p>格式要求：.jpeg、.png</p>
        <p>图片大小：不超过10MB；图片尺寸：300px * 300 px</p>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { uploadThumbnailApi } from '@/api/common'
import useUserInfo from '@/store/useUserInfo'
import type { UploadProps } from 'element-plus'
import { ElIcon, ElMessage, ElUpload } from 'element-plus'

const userInfo = useUserInfo()

defineProps<{ modelValue: any; disabled?: boolean; action?: string }>()

const handleAvatarSuccess: UploadProps['onSuccess'] = (response) => {
  const { data, status, message } = response
  if ([200].includes(status)) {
    emit('update:modelValue', data)
    ElMessage.success(message)
  } else {
    ElMessage.error(message)
  }
}

const beforeAvatarUpload: UploadProps['beforeUpload'] = (rawFile) => {
  if (rawFile.size / 1024 / 1024 > 10) {
    ElMessage.warning('请上传小于10MB的图片')
    return false
  }
  return true
}

const emit = defineEmits<{
  (e: 'update:modelValue', url: any): void
}>()
</script>

<style scoped lang="less">
.avatar-uploader .avatar {
  width: 80px;
  height: 80px;
  display: block;
}
.image-upload {
  width: 100%;
  display: flex;
  .el-upload__tip {
    flex: 1;
    overflow: hidden;
    box-sizing: border-box;
  }
}

.el-icon.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 80px;
  height: 80px;
  text-align: center;
}

.el-upload__tip {
  margin-left: 30px;
  display: flex;
  line-height: 24px;
  .icon {
    margin-top: 5px;
    color: RGBA(255, 172, 41, 1);
    font-size: 16px;
    line-height: 24px;
  }
  .text {
    margin-left: 10px;
    flex: 1;
    overflow: hidden;
    box-sizing: border-box;
    p {
      width: 100%;
      .ellipseLine();
    }
  }
}
</style>

<style>
.avatar-uploader .el-upload {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}
</style>
