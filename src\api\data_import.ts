import request from '@/utils/request'

// 查询-数据导入（分页）
export const queryPageByParamApi = (data: any) => request.post(`/dataImport/queryPageByParam`, data)

// 新增-数据导入
export const addImportApi = (data: any) => request.post(`/dataImport/add`, data)

// 修改-数据导入
export const modifyByIdApi = (data: any) => request.post(`/dataImport/modifyById`, data)

// 查询-PG数据源所有表名，区分已导入，未导入
export const queryPGTableNameByParamApi = (data: any) =>
  request.post(`/dataImport/queryPGTableNameByParam`, data)

// 查询-数据源（集合）
export const queryDBListApi = (data: any) => request.post(`/dataImport/queryDBList`, data)

// 查询-数据导入
export const queryByIdApi = (tid: any) => request.get(`/dataImport/queryById/${tid}`)

// 查询-数据导入详情（分页）
export const queryDetailPageApi = (data: any) =>
  request.post(`/dataImportDetail/queryPageByParam`, data)

// 查询-数据导入文件日志结果
export const queryByDataImportIdApi = (tid: any) =>
  request.get(`/dataImportFileLog/queryByDataImportId/${tid}`)

// 查询-数据源
export const queryListByParamApi = (data: any) => request.post(`/datasource/queryListByParam`, data)

// 新增-数据导入任务
export const addDataImportTaskApi = (data: any) => request.post(`/dataImport/addTask`, data)

// 查询当前模型规则
export const listAllRuleApi = () => request.get(`/qaTableRule/listAllRule`)
