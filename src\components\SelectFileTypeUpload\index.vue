<template>
  <el-dialog
    v-model="dialogFormVisible"
    title="上传"
    width="440px"
    :close-on-click-modal="false"
    @open="handleDialogOpen"
  >
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="80px">
      <el-form-item label="数据类型" prop="fileTypeId">
        <div class="dataFormat">
          <el-cascader
            style="width: 250px"
            @change="handleChange"
            :props="catalogProps"
            :options="catalogOptions"
            v-model="formData.fileTypeId"
            placeholder="请选择数据类型"
          />
          <ElPopover placement="right" :width="400" trigger="click">
            <template #reference>
              <ElIcon style="margin-left: 10px; color: #e6a23c; cursor: pointer"
                ><SvgIcon name="WarningFilled"
              /></ElIcon>
            </template>
            <FormatFilter />
          </ElPopover>
        </div>
      </el-form-item>
      <el-form-item label="数据文件" prop="fileId">
        <div class="fileType-dataFormat">
          <el-button type="primary" @click="handleSelectUploadFile">选择文件</el-button>
          <span :title="dataFormat"> 支持的数据格式 {{ dataFormat }} </span>
        </div>
      </el-form-item>
      <el-form-item label="名称" prop="fileName" :rules="rules.fileName">
        <el-input
          style="width: 250px"
          placeholder="请修改文件名称"
          v-model="formData.fileName"
        ></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="setDialogFormVisible(false)">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import useDialogForm from '@/hooks/useDialogForm'
import useGlobalData from '@/store/useGlobalData'
import { CascaderProps, ElIcon, ElMessage, ElPopover } from 'element-plus'
import { reactive, ref, computed } from 'vue'
import { findValueByIdWithChildren } from '@/utils'
import FormatFilter from './FormatFilter.vue'

// 数据类型
const globalData = useGlobalData()
const catalogOptions = globalData.catalogMenuList.slice(0, 3)
const catalogProps: CascaderProps = {
  expandTrigger: 'hover',
  value: 'tid',
  label: 'fileTypeName',
  children: 'childVOList',
}

// 获取数据格式
const dataFormat = computed<string>(
  () => findValueByIdWithChildren(catalogOptions, formData.value.fileTypeId)?.notes,
)

// 上传类型校验
const dataValiate = computed<string>(() =>
  findValueByIdWithChildren(catalogOptions, formData.value.fileTypeId)
    .suffixs.map((item: string) => `.${item}`)
    .join(','),
)

// 选择数据类型
const handleChange = (value: any) => {
  if (value && value.length > 0) {
    formData.value.fileTypeId = value[value.length - 1]
  } else {
    formData.value.fileTypeId = ''
  }
}

// 选择上传文件
const handleSelectUploadFile = () => {
  if (!formData.value.fileTypeId) {
    ElMessage.warning('请选择数据类型！')
    return
  }
  console.log(dataValiate, 'attrs')
  emit('handleSelectSpaceData', dataValiate.value)
}

// 上传触发
const handleSubmit = async () => {
  try {
    await validateForm()
    emit('handleSelectUpload', formData.value)
    setDialogFormVisible(false)
  } catch (error) {}
}

// 打开弹框
const handleOpen = () => {
  setDialogFormVisible(true)
}

const { dialogFormVisible, handleDialogOpen, setDialogFormVisible, formRef, validateForm } =
  useDialogForm()

const formData = ref<any>({
  fileTypeId: '',
  fileName: '',
})

const rules = reactive<any>({
  fileTypeId: [{ required: true, message: '请选择数据子类型', trigger: 'change' }],
  fileName: [{ required: true, message: '请输入文件名', trigger: 'change' }],
  fileId: [{ required: true, message: '请选择文件', trigger: 'change' }],
})

const emit = defineEmits<{
  (e: 'handleSelectSpaceData', dataFormat: string): void
  (e: 'handleSelectUpload', params: any): void
}>()

// 获取上传的文件信息
const getFileData = ({ name, id }: any) => {
  formData.value.fileId = id
  formData.value.fileName = name
}

defineExpose({ handleOpen, getFileData })
</script>

<style scoped lang="less">
.dataFormat {
  display: flex;
  align-items: center;
}
.fileType-dataFormat {
  height: 100%;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  width: 100%;
  > span {
    line-height: normal;
    box-sizing: border-box;
    margin-left: 15px;
    .ellipseLine();
  }
}
</style>
