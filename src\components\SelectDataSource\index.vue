<template>
  <el-select
    style="width: 100%"
    v-model="value"
    filterable
    remote
    reserve-keyword
    placeholder="请选择数据源"
    remote-show-suffix
    :remote-method="remoteMethod"
    :loading="loading"
    value-key="tid"
    @change="handleChange"
    clearable
    @clear="remoteMethod('', true)"
  >
    <el-option v-for="p in dataSourceOptions" :key="p.tid" :label="p.datasourceName" :value="p" />
  </el-select>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { queryDBListApi } from '@/api/data_import'
import { ElMessage } from 'element-plus'

const props = defineProps<{ value?: any; name?: any; onChange: (p: any) => void }>()

const loading = ref(false)
const value = ref<any>('')
const dataSourceOptions = ref<any[]>([])

const handleChange = (val: any) => {
  props.onChange && props.onChange(val)
}

const remoteMethod = async (query: string = '', isInit: boolean = false) => {
  if (!isInit && !query) return
  try {
    loading.value = true
    const { data, status, message } = await queryDBListApi({ datasourceName: query })
    if ([200].includes(status)) {
      dataSourceOptions.value = data.slice(0, 30)
    } else {
      ElMessage.error(message)
    }
    loading.value = false
  } catch (error) {
    loading.value = false
  }
}

remoteMethod('', true)

watch(
  () => props,
  () => {
    value.value = props.name || props.value
  },
  {
    deep: true,
    immediate: true
  }
)
</script>

<style scoped></style>
