import request, { baseUrl } from '@/utils/request'
// 用户文件列表
export const dataCatalogApplyPageApi = (data: any) =>
  request.post(`/dataCatalogApply/queryPageByParam`, data)

// 查询-我提交的数据目录申请集合（分页）
export const queryApplyPageByParamApi = (data: any) =>
  request.post(`/dataCatalogApply/queryApplyPageByParam`, data)

// 查询-数据目录申请（主键）
export const dataCatalogQueryByIdApi = (tid: string) =>
  request.get(`/dataCatalogApply/queryById/${tid}`)

// 修改-使用数据目录
export const dataCatalogModifyUseApi = (tid: string) =>
  request.get(`/dataCatalogApply/modifyUse/${tid}`)

// 新增-数据目录申请
export const dataCatalogAddApi = (data: any) => request.post(`/dataCatalogApply/add`, data)

// 查询-数据目录申请（当前版本）
export const dataCatalogQueryUseApi = () => request.get(`/dataCatalogApply/queryUse`)

// 导出-数据目录
export const exportDataLakeCatalogApi = () => request.downLoad(`/dataCatalog/exportDataLakeCatalog`)

// 修改-取消数据目录申请
export const modifyCancelApplyApi = (data: any) =>
  request.post(`/dataCatalogApply/modifyCancelApply`, data)

// 删除-数据目录申请
export const dataCatalogRemoveByIdApi = (tid: string) =>
  request.get(`/dataCatalogApply/removeById/${tid}`)

// 修改-审核数据目录申请
export const modifyAuthApplyApi = (data: any) =>
  request.post(`/dataCatalogApply/batchModifyAuthApply`, data)

// 修改-发布数据目录
export const modifyReleaseApi = (tid: string) =>
  request.get(`/dataCatalogApply/modifyRelease/${tid}`)

// 导入-数据湖目录
export const importDataLakeCatalogApi = () => `${baseUrl}/dataCatalog/importDataLakeCatalog`
