<template>
  <MainLayout>
    <template #header>
      <SearchTool @handle-create="handleAddEdit(null, 1)" @handleSearch="handleSearch" />
    </template>
    <el-table
      header-cell-class-name="common-table-header"
      cell-class-name="common-table-cell"
      ref="multipleTableRef"
      :data="tableData"
      height="100%"
      style="width: 100%"
      row-key="tid"
      default-expand-all
    >
      <el-table-column property="ruleName" label="规则名称" show-overflow-tooltip />
      <el-table-column property="dataTypeName" label="数据类型" show-overflow-tooltip />
      <el-table-column property="dimension" label="维度" show-overflow-tooltip />
      <el-table-column property="fileTypeName" label="适用数据" show-overflow-tooltip />
      <el-table-column property="ruleContent" label="规则说明" show-overflow-tooltip />
      <el-table-column property="standardContent" label="参考标准" show-overflow-tooltip />
      <el-table-column
        v-if="
          permission.hasButton(['qualityRules_details', 'qualityRules_edit', 'qualityRules_del'])
        "
        label="操作"
        width="120"
        fixed="right"
      >
        <template #default="scope">
          <el-button
            v-if="!scope.row.isSystem && permission.hasButton('qualityRules_edit')"
            @click="handleAddEdit(scope.row, 2)"
            title="编辑"
            class="common-icon-btn"
            type="primary"
            text
            link
          >
            <template #icon>
              <SvgIcon name="edit" />
            </template>
          </el-button>
          <el-button
            v-if="permission.hasButton('qualityRules_details')"
            @click="handleAddEdit(scope.row, 3)"
            title="详情"
            class="common-icon-btn"
            type="primary"
            text
            link
          >
            <template #icon>
              <SvgIcon name="details" />
            </template>
          </el-button>
          <el-button
            v-if="!scope.row.isSystem && permission.hasButton('qualityRules_del')"
            @click="handleDel(scope.row.tid)"
            title="删除"
            class="common-icon-btn"
            type="danger"
            plain
            link
          >
            <template #icon>
              <SvgIcon name="delete" />
            </template>
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <template #footer>
      <el-pagination
        v-model:currentPage="pageData.page"
        v-model:page-size="pageData.limit"
        :page-sizes="pageData.pageSizes"
        background
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageData.total"
        @size-change="pageData.handleSizeChange"
        @current-change="pageData.handleCurrentChange"
      />
    </template>
    <EditDetailDialog ref="EditDetailDialogRef" />
  </MainLayout>
</template>

<script setup lang="ts">
import MainLayout from '@/components/MainLayout/index.vue'
import SearchTool from './SearchTool.vue'
import { ref } from 'vue'
import { uqaRuleListApi, uqaRuleRemoveByIdApi } from '@/api/data_governance'
import EditDetailDialog from './EditDetailDialog.vue'
import useUserInfo from '@/store/useUserInfo'
import usePageData from '@/hooks/usePageData'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
const permission = useUserInfo()

const router = useRouter()

const { tableData, pageData } = usePageData(uqaRuleListApi)
const handleSearch = (form: any) => {
  pageData.handleSearch(form, 1)
}

const handleAddEdit = (row: any, optype: number) => {
  router.push({ name: 'addEditRules', query: { optype: optype, tid: row?.tid } })
}

const handleDel = (tid: string) => {
  ElMessageBox.confirm('确认要删除该数据嘛？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    try {
      const { status, message } = await uqaRuleRemoveByIdApi({ tid })
      if ([200].includes(status)) {
        ElMessage.success(message)
        pageData.handleSearch(null, 1)
      } else {
        ElMessage.error(message)
      }
    } catch (error) {
      console.error(error)
    }
  })
}

const EditDetailDialogRef = ref<any>()
</script>

<style scoped></style>
