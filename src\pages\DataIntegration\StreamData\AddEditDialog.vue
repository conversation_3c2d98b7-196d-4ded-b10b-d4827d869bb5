<template>
  <ElDialog
    title="新增流数据"
    v-model="dialogFormVisible"
    width="960px"
    :close-on-click-modal="false"
    @close="handleClose"
    @open="handleMap"
  >
    <ElScrollbar height="50vh">
      <div class="form-data">
        <ElSteps :active="active" finish-status="success" simple>
          <ElStep title="数据源"> </ElStep>
          <ElStep title="配置信息"></ElStep>
        </ElSteps>
        <ElForm
          v-show="active == 0"
          :disabled="[3].includes(openType)"
          ref="formDataRef"
          :model="formData"
          :rules="rules"
          label-width="auto"
          scroll-to-error
        >
          <ElRow class="rowData" :gutter="20">
            <ElCol :span="12">
              <ElFormItem prop="serverName">
                <template #label>
                  <div class="lableTile">
                    <ElTooltip
                      class="tips"
                      effect="dark"
                      placement="top"
                      content="名称限制输入'~!@#$%^&*()_+-=[]:;,.?/'等特殊字符"
                    >
                      <ElIcon class="icon"><SvgIcon name="WarningFilled" /></ElIcon>
                    </ElTooltip>
                    <span>服务名称</span>
                  </div>
                </template>
                <ElInput v-model="formData.serverName" placeholder="输入服务名称" />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="数据引接协议" prop="sourceType">
                <ElRadioGroup v-model="formData.sourceType" @change="handleSourceType">
                  <ElRadio
                    v-for="p in dictData.sourceType"
                    :key="p.value"
                    :label="p.key"
                    :value="p.value"
                  />
                </ElRadioGroup>
              </ElFormItem>
            </ElCol>
            <ElCol v-if="sourceTypeOptions.length" :span="12">
              <ElFormItem label="接收端口" prop="sourcePort">
                <ElSelect v-model="formData.sourcePort" placeholder="请选择接收端口">
                  <ElOption
                    v-for="p in sourceTypeOptions"
                    :key="p.value"
                    :label="p.key"
                    :value="p.value"
                  />
                </ElSelect>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem prop="context">
                <template #label>
                  <div class="lableTile">
                    <ElTooltip
                      class="tips"
                      effect="dark"
                      placement="top"
                      content="一条数据流的唯一标识，需要与适配器配置的channelID相同"
                    >
                      <ElIcon class="icon"><SvgIcon name="WarningFilled" /></ElIcon>
                    </ElTooltip>
                    <span>channel ID</span>
                  </div>
                </template>
                <ElInput v-model="formData.context" placeholder="输入channel ID" />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem prop="mapType">
                <template #label>
                  <div class="lableTile">
                    <ElTooltip
                      class="tips"
                      effect="dark"
                      placement="top"
                      content="数据流的封装格式"
                    >
                      <ElIcon class="icon"><SvgIcon name="WarningFilled" /></ElIcon>
                    </ElTooltip>
                    <span>数据封装格式</span>
                  </div>
                </template>
                <ElSelect v-model="formData.mapType" placeholder="数据封装格式">
                  <ElOption
                    v-for="p in dictData.mapType"
                    :key="p.value"
                    :label="p.key"
                    :value="p.value"
                  />
                </ElSelect>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem prop="targetId">
                <template #label>
                  <div class="lableTile">
                    <ElTooltip
                      class="tips"
                      effect="dark"
                      placement="top"
                      content="数据流中一个目标的唯一标识字段名称"
                    >
                      <ElIcon class="icon"><SvgIcon name="WarningFilled" /></ElIcon>
                    </ElTooltip>
                    <span>目标标识</span>
                  </div>
                </template>
                <ElInput v-model="formData.targetId" placeholder="输入目标标识" />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem prop="lon">
                <template #label>
                  <div class="lableTile">
                    <ElTooltip
                      class="tips"
                      effect="dark"
                      placement="top"
                      content="数据流中表示经度的字段名称"
                    >
                      <ElIcon class="icon"><SvgIcon name="WarningFilled" /></ElIcon>
                    </ElTooltip>
                    <span>经度标识</span>
                  </div>
                </template>
                <ElInput v-model="formData.lon" placeholder="输入经度标识" />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem prop="lat">
                <template #label>
                  <div class="lableTile">
                    <ElTooltip
                      class="tips"
                      effect="dark"
                      placement="top"
                      content="数据流中表示纬度的字段名称"
                    >
                      <ElIcon class="icon"><SvgIcon name="WarningFilled" /></ElIcon>
                    </ElTooltip>
                    <span>纬度标识</span>
                  </div>
                </template>
                <ElInput v-model="formData.lat" placeholder="输入纬度标识" />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem prop="alt">
                <template #label>
                  <div class="lableTile">
                    <ElTooltip
                      class="tips"
                      effect="dark"
                      placement="top"
                      content="数据流中表示高度的字段名称，未填写表示二维数据"
                    >
                      <ElIcon class="icon"><SvgIcon name="WarningFilled" /></ElIcon>
                    </ElTooltip>
                    <span>高度标识</span>
                  </div>
                </template>
                <ElInput v-model="formData.alt" placeholder="输入高度标识" />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem prop="time">
                <template #label>
                  <div class="lableTile">
                    <ElTooltip
                      class="tips"
                      effect="dark"
                      placement="top"
                      content="数据流中表示采集时间的字段名称，未填写默认为target-time，并将值设为数据接收的时间"
                    >
                      <ElIcon class="icon"><SvgIcon name="WarningFilled" /></ElIcon>
                    </ElTooltip>
                    <span>时间标识</span>
                  </div>
                </template>
                <ElInput v-model="formData.time" placeholder="输入时间标识" />
              </ElFormItem>
            </ElCol>
            <ElCol :span="24">
              <ElFormItem label="扩展字段" prop="extendAttrs">
                <div class="btn">
                  <ElButton @click="handleAdd" type="primary">添加字段</ElButton>
                  <span>数据流中其他需要存储的字段</span>
                </div>
                <ElTable
                  :data="formData.extendAttrs"
                  header-cell-class-name="common-table-header"
                  cell-class-name="common-table-cell"
                  max-height="200px"
                  border
                >
                  <el-table-column property="extKey" label="字段名称">
                    <template #default="scope">
                      <ElFormItem :prop="`ruleList.${scope.$index}.extKey`" :rules="rules">
                        <ElInput v-model="scope.row.extKey" placeholder="请输入"></ElInput>
                      </ElFormItem>
                    </template>
                  </el-table-column>
                  <el-table-column property="extType" label="字段类型">
                    <template #default="scope">
                      <ElFormItem :prop="`ruleList.${scope.$index}.extType`" :rules="rules">
                        <ElSelect v-model="scope.row.extType" placeholder="请选择表名">
                          <ElOption
                            v-for="p in dictData.fieldType"
                            :key="p.value"
                            :label="p.key"
                            :value="p.value"
                          />
                        </ElSelect>
                      </ElFormItem>
                    </template>
                  </el-table-column>
                  <el-table-column property="extDescribe" label="字段描述">
                    <template #default="scope">
                      <ElFormItem :prop="`ruleList.${scope.$index}.extDescribe`" :rules="rules">
                        <ElInput v-model="scope.row.extDescribe" placeholder="请输入"></ElInput>
                      </ElFormItem>
                    </template>
                  </el-table-column>
                  <el-table-column property="adapterName" label="操作" width="80">
                    <template #default="scope">
                      <ElButton @click="handleDel(scope.$index)" type="primary" link>删除</ElButton>
                    </template>
                  </el-table-column>
                </ElTable>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="定时执行" prop="isSchedule">
                <ElRadioGroup v-model="formData.isSchedule">
                  <ElRadio label="是" :value="1" />
                  <ElRadio label="否" :value="0" />
                </ElRadioGroup>
              </ElFormItem>
            </ElCol>
            <ElCol v-if="formData.isSchedule" :span="12">
              <ElFormItem label="执行时间" prop="execTime">
                <ElDatePicker
                  style="width: 100%"
                  v-model="formData.execTime"
                  type="datetime"
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  placeholder="选择时间"
                />
              </ElFormItem>
            </ElCol>
          </ElRow>
        </ElForm>
        <ElForm
          v-show="active"
          :disabled="[3].includes(openType)"
          ref="formRef"
          class="form-data"
          :model="formData"
          :rules="rules"
          label-width="auto"
        >
          <ElRow class="rowData" :gutter="20">
            <ElCol :span="12">
              <ElFormItem prop="serverName">
                <template #label>
                  <div class="lableTile">
                    <ElTooltip
                      class="tips"
                      effect="dark"
                      placement="top"
                      content="名称限制输入'~!@#$%^&*()_+-=[]:;,.?/'等特殊字符"
                    >
                      <ElIcon class="icon"><SvgIcon name="WarningFilled" /></ElIcon>
                    </ElTooltip>
                    <span>服务名称</span>
                  </div>
                </template>
                <ElInput v-model="formData.serverName" placeholder="输入服务名称" />
              </ElFormItem>
              <ElFormItem label="最小层级" prop="minLevel">
                <ElInput v-model="formData.minLevel" placeholder="输入最小层级" />
              </ElFormItem>
              <ElFormItem label="数据位置" prop="initLonLat">
                <ElInput
                  readonly
                  v-model="formData.initLonLat"
                  placeholder="请在地图上点击选取位置"
                />
              </ElFormItem>
              <ElFormItem label="服务描述" prop="describe">
                <ElInput
                  v-model="formData.describe"
                  :rows="2"
                  type="textarea"
                  placeholder="输入服务描述"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <div class="map" ref="mapRef"></div>
            </ElCol>
          </ElRow>
        </ElForm>
      </div>
    </ElScrollbar>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="setDialogFormVisible(false)">取消</el-button>
        <el-button v-if="active" @click="active = 0">上一步</el-button>
        <el-button v-else @click="handleNext">下一步</el-button>
        <el-button v-if="active && ![3].includes(openType)" @click="handleSubmit" type="primary"
          >确定</el-button
        >
      </span>
    </template>
  </ElDialog>
</template>
<script setup lang="ts">
import { IotCreateApi, IotDictApi, IotServerApi, IotUpdateApi } from '@/api/stream_data'
import useDialogForm from '@/hooks/useDialogForm'
import useMapbox from '@/hooks/useMapbox'
import {
  ElButton,
  ElCol,
  ElDatePicker,
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElMessage,
  ElOption,
  ElRadio,
  ElRadioGroup,
  ElRow,
  ElScrollbar,
  ElSelect,
  ElStep,
  ElSteps,
  ElTooltip,
} from 'element-plus'
import { Marker } from 'mapbox-gl'
import { nextTick, reactive, ref } from 'vue'

const dictData = ref<any>({})

const sourceTypeOptions = ref<any[]>([])
const handleSourceType = (val: any) => {
  formData.value.sourcePort = ''
  sourceTypeOptions.value = dictData.value.sourceType.find((item: any) => item.value === val).child
}

const loading = ref<boolean>(false)
const handleSubmit = async () => {
  try {
    loading.value = true
    await validateForm()
    const { status, message } = [1].includes(openType.value)
      ? await IotCreateApi(formData.value)
      : await IotUpdateApi(formData.value)
    if (status === 200) {
      ElMessage.success('处理成功!')
      emit('handleRefresh')
      setDialogFormVisible(false)
    } else {
      ElMessage.error(message)
    }
    loading.value = false
  } catch (error) {
    loading.value = false
  }
}

const { mapRef, initMap, map } = useMapbox('mercator')

const marker = ref<Marker | null>()

const handleMap = () => {
  initMap(() => {
    if (formData.value.initLonLat) {
      marker.value = new Marker({ color: 'red' })
        .setLngLat(formData.value.initLonLat.split(','))
        .addTo(map.value)
    }
    map.value.on('click', (e: any) => {
      if (marker.value) {
        marker.value.remove()
        marker.value = null
      }

      if (![3].includes(openType.value)) {
        formData.value.initLonLat = [e.lngLat.lng, e.lngLat.lat].join(',')
        marker.value = new Marker({ color: 'red' })
          .setLngLat([e.lngLat.lng, e.lngLat.lat])
          .addTo(map.value)
      }
    })
  })
  nextTick(() => {
    formDataRef.value?.clearValidate()
  })
}

const handleClose = () => {
  handleDialogClose()
  if (map.value) {
    map.value.remove()
  }
}

const { dialogFormVisible, handleDialogClose, formRef, setDialogFormVisible, validateForm } =
  useDialogForm()

const active = ref<number>(0)
const formDataRef = ref<any>()
const handleNext = async () => {
  if (!formDataRef.value) return
  await formDataRef.value.validate((valid: boolean) => {
    if (valid) {
      active.value = 1
    }
  })
}

const formData = ref<any>({ extendAttrs: [] })
const rules = reactive<any>({
  serverName: [{ required: true, message: '请输入服务名称', trigger: 'blur' }],
  sourceType: [{ required: true, message: '请选择数据引接协议', trigger: 'change' }],
  sourcePort: [{ required: true, message: '请选择接收端口', trigger: 'blur' }],
  context: [{ required: true, message: '输入channel ID', trigger: 'blur' }],
  mapType: [{ required: true, message: '请选择数据封装格式', trigger: 'blur' }],
  targetId: [{ required: true, message: '输入目标标识', trigger: 'change' }],
  lon: [{ required: true, message: '输入经度标识', trigger: 'blur' }],
  lat: [{ required: true, message: '输入纬度标识', trigger: 'blur' }],
  // alt: [{ required: true, message: '输入高度标识', trigger: 'blur' }],
  // time: [{ required: true, message: '输入时间标识', trigger: 'blur' }],
  isSchedule: [{ required: true, message: '请选择是否定时执行', trigger: 'change' }],
  execTime: [{ required: true, message: '请选择执行时间', trigger: 'change' }],
  minLevel: [{ required: true, message: '输入最小层级', trigger: 'blur' }],
  initLonLat: [{ required: true, message: '请选择地图位置', trigger: 'change' }],
})

const handleAdd = () => {
  formData.value.extendAttrs.push({})
}
const handleDel = (index: number) => {
  formData.value.extendAttrs.splice(index, 1)
}

const openType = ref<number>(1)
const handleOpen = async (type: number, row?: any) => {
  await getDictData()
  openType.value = type
  active.value = 0
  if (row) {
    getDetails(row.tid)
  } else {
    formData.value = { extendAttrs: [] }
  }
  setDialogFormVisible(true)
}

const getDictData = async () => {
  try {
    const { data, status, message } = await IotDictApi()
    if (status === 200) {
      dictData.value = data
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}

const getDetails = async (id: any) => {
  try {
    const { data, message, status } = await IotServerApi(id)
    if (status === 200) {
      formData.value = data
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}

defineExpose({ handleOpen })
const emit = defineEmits<{
  (e: 'handleRefresh'): void
}>()
</script>
<style lang="less" scoped>
.form-data {
  height: 100%;
  display: flex;
  overflow: hidden;
  box-sizing: border-box;
  flex-direction: column;
  .rowData {
    margin-top: 15px;
    flex: 1;
    box-sizing: border-box;
    overflow: hidden;
    .lableTile {
      display: flex;
      align-items: center;
      .icon {
        margin-right: 5px;
        color: RGBA(255, 172, 41, 1);
      }
    }
    .btn {
      display: flex;
      align-items: center;
      margin-bottom: 15px;
      > span {
        margin-left: 10px;
      }
    }
  }
  .map {
    height: 380px;
    width: 100%;
  }
}
</style>
