<template>
  <div class="AsideMenu">
    <el-input v-model="dataBaseName" class="search">
      <template #suffix>
        <SvgIcon name="search" />
      </template>
    </el-input>
    <div class="title">数据库名称</div>
    <el-scrollbar>
      <ul class="table-list">
        <li
          v-for="p in tableData"
          :key="p"
          @click="handleSelect(p)"
          :class="{ active: [active].includes(p) }"
        >
          <img :src="table" alt="icon" />
          <span>{{ p }}</span>
        </li>
      </ul>
    </el-scrollbar>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import table from '../SourceDataDetail/table.png'
import { useRoute, useRouter } from 'vue-router'

const props = defineProps<{ tableList: any[] }>()

const router = useRouter()
const route = useRoute()

const dataBaseName = ref<string>('')

const active = ref<any>()
const handleSelect = (p: any) => {
  router.push({ query: { ...route.query, dbname: p } })
}

watch(
  () => route.query.dbname,
  () => {
    active.value = route.query.dbname
  },
  {
    immediate: true,
  },
)

const tableData = computed<any[]>(() =>
  props.tableList.filter((item: string) => item.includes(dataBaseName.value)),
)
</script>

<style scoped lang="less">
.AsideMenu {
  width: 200px;
  height: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  background-color: @withe;
  box-shadow: 0px 8px 8px 0px rgba(0, 0, 0, 0.05);
  padding: 20px 0;
  overflow-y: auto;
  .search {
    margin: 0 auto;
    box-sizing: border-box;
    width: 160px;
  }
  .title {
    width: 180px;
    margin: 0 auto;
    margin-top: 20px;
    height: 30px;
    background: #f5f5f5;
    line-height: 30px;
    font-size: 14px;
    font-weight: 400;
    color: #21333f;
    padding: 0 10px;
    box-sizing: border-box;
  }
  .table-list {
    flex: 1;
    box-sizing: border-box;
    margin-top: 20px;
    padding: 0px 10px;
    overflow-y: auto;
    .no-scrollbar(0);
    li {
      display: flex;
      align-items: center;
      cursor: pointer;
      padding: 5px 10px;
      span {
        flex: 1;
        margin-left: 10px;
        font-size: 14px;
        font-weight: 400;
        color: #21333f;
        .ellipseLine();
      }
    }
    li + li {
      margin-top: 8px;
    }
    .active {
      background: #f5f5f5;
    }
  }
}
</style>
