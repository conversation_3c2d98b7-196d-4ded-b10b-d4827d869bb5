<template>
  <el-dialog
    :title="isCreateTask ? '新建任务' : '打开模型'"
    v-model="dialogFormVisible"
    width="640px"
    :close-on-click-modal="false"
    @close="handleDialogClose"
  >
    <el-form ref="formRef" :model="formData" :rules="rules">
      <el-form-item label="选择模型" required>
        <div class="select-model">
          <el-row class="select-row" :gutter="10">
            <el-col :span="12">
              <el-form-item>
                <el-select
                  @change="modelObj.handleChange"
                  style="width: 100%"
                  v-model="formData.modelTypeId"
                  placeholder="请选择模型"
                >
                  <el-option
                    v-for="p in modelObj.modelTypeOptions"
                    :key="p.tid"
                    :label="p.typeName"
                    :value="p.tid"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item>
                <el-select
                  style="width: 100%"
                  v-model="formData.tid"
                  filterable
                  remote
                  reserve-keyword
                  placeholder="请搜索模型模型"
                  remote-show-suffix
                  :remote-method="modelObj.remoteMethod"
                  :loading="modelObj.loading"
                  value-key="tid"
                  clearable
                >
                  <el-option
                    v-for="p in modelObj.modelList"
                    :key="p.tid"
                    :label="p.modelName"
                    :value="p.tid"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-button v-if="isCreateTask" style="margin-left: 10px" plain link type="primary"
            >新建模型</el-button
          >
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="setDialogFormVisible(false)">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import useDialogForm from '@/hooks/useDialogForm'
import { reactive, ref } from 'vue'
import { queryModeltypeTreeListApi } from '@/api/online_model'
import { ElMessage } from 'element-plus'
import { queryPageByParamApi } from '@/api/online_model'

const formData = ref<any>({})
const rules = reactive<any>({})

interface ModelObj {
  modelTypeOptions: any[]
  handleChange: (val: any) => void
  modelList: any[]
  loading: boolean
  getModelList: (likeModelName?: string) => void
  remoteMethod: (query: string) => void
}
const modelObj = reactive<ModelObj>({
  modelTypeOptions: [],
  handleChange: async (val) => {
    modelObj.modelList = []
    if (val) {
      await modelObj.getModelList()
    }
  },
  modelList: [],
  loading: false,
  getModelList: async (likeModelName) => {
    try {
      modelObj.loading = true
      const params = { modelTypeId: formData.value.modelTypeId, likeModelName, page: 1, limit: 30 }
      const { data, status, message } = await queryPageByParamApi(params)
      if ([200].includes(status)) {
        modelObj.modelList = data
      } else {
        ElMessage.error(message)
      }
      modelObj.loading = false
    } catch (error) {
      modelObj.loading = false
    }
  },
  remoteMethod: async (query) => {
    if (!query || !formData.value.modelTypeId) {
      return
    }
    await modelObj.getModelList(query)
  },
})

const { dialogFormVisible, setDialogFormVisible, formRef, handleDialogClose } = useDialogForm()

const handleSubmit = () => {
  if (!formData.value.tid) {
    ElMessage.warning('请选择模型！')
    return
  }
  emit('handleSubmit', setDialogFormVisible, formData.value.tid)
}

const handleOpen = async () => {
  try {
    const { data, status, message } = await queryModeltypeTreeListApi({ ptid: '1' })
    if ([200].includes(status)) {
      modelObj.modelTypeOptions = data
      setDialogFormVisible(true)
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}

const emit = defineEmits<{
  handleSubmit: [callback: any, form: any]
}>()
defineExpose({ handleOpen })
defineProps<{ isCreateTask?: boolean }>()
</script>

<style scoped lang="less">
.select-model {
  width: 100%;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  .select-row {
    flex: 1;
    box-sizing: border-box;
  }
}
</style>
