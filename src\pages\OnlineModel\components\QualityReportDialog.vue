<template>
  <el-dialog
    width="80%"
    v-model="dialogFormVisible"
    title="质检报告"
    top="5vh"
    :close-on-click-modal="false"
    @close="handleDialogClose"
  >
    <div class="qualityReport">
      <div class="header">
        <div class="left">
          <img :src="quality" alt="quality" />
          <span class="title" :title="`${tableData.taskName}一质检任务一分析报告`"
            >{{ tableData.taskName }}一质检任务一分析报告</span
          >
        </div>
        <ul class="center">
          <li class="center-item">
            <div class="img" :style="{ backgroundColor: '#edf2fc', color: '#598bfd' }">
              <SvgIcon name="onlineModelTable" />
            </div>
            <div class="text">
              <span :style="{ color: '#598bfd' }">{{ tableData.qaReportStat?.checkNum }}</span>
              <span class="title">检查项数量</span>
            </div>
          </li>
          <li class="center-item">
            <div class="img" :style="{ backgroundColor: '#edf2fc', color: '#FF7070' }">
              <SvgIcon name="onlineModelTable" />
            </div>
            <div class="text">
              <span :style="{ color: '#FF7070' }">{{ tableData.qaReportStat?.failNum }}</span>
              <span class="title">异常项数量</span>
            </div>
          </li>
        </ul>
        <div class="right">
          <div class="right-item">
            <el-progress :width="70" type="circle" :percentage="passingRate" />
            <p>通过率</p>
          </div>
          <div class="right-item">
            <el-progress :width="70" type="circle" :percentage="failRate" color="#FF7070" />
            <p>异常率</p>
          </div>
          <div class="right-item">
            <div
              style="
                font-size: 16px;
                width: 70px;
                height: 70px;
                line-height: 70px;
                text-align: center;
              "
            >
              {{
                tableData.qaReportStat?.duration >= 1000
                  ? transferSecondsToTime(tableData.qaReportStat?.duration || 0, 'milliseconds')
                  : `${tableData.qaReportStat?.duration}ms`
              }}
            </div>
            <p style="margin-top: 4px">质检时长</p>
          </div>
        </div>
      </div>
      <ElScrollbar class="main">
        <div class="tableList" v-for="p in tableData.qaReportResult" :key="p.tableName">
          <p class="title">检查对象：{{ p.tableName }}</p>
          <el-table
            header-cell-class-name="common-table-header"
            cell-class-name="common-table-cell"
            ref="multipleTableRef"
            :data="p.qaReportDtoList"
            border
            row-key="tid"
          >
            <el-table-column property="nodeName" label="质检项" show-overflow-tooltip />
            <el-table-column property="qaType" label="质检类型" show-overflow-tooltip />
            <el-table-column property="nodeName" label="质检内容" show-overflow-tooltip />
            <el-table-column property="qaStatus" label="质检结果">
              <template #default="scope">
                {{ scope.row.qaStatus ? '质检通过' : '质检异常' }}
              </template>
            </el-table-column>
            <el-table-column property="qaStatus" label="异常信息" show-overflow-tooltip>
              <template #default="scope">
                {{ scope.row.qaStatus ? '--' : scope.row.qaResult }}
              </template>
            </el-table-column>
            <el-table-column property="qaProposal" label="建议" show-overflow-tooltip />
          </el-table>
        </div>
      </ElScrollbar>
      <div class="footer">
        <el-pagination
          v-model:currentPage="pageData.page"
          v-model:page-size="pageData.limit"
          :page-sizes="pageData.pageSizes"
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="pageData.total"
          @size-change="pageData.handleSizeChange"
          @current-change="pageData.handleCurrentChange"
        />
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import useDialogForm from '@/hooks/useDialogForm'
import quality from '@/assets/commonImg/quality.webp'
import { ElScrollbar } from 'element-plus'
import usePageData from '@/hooks/usePageData'
import { getDetailQaReportApi } from '@/api/online_model'
import { computed } from 'vue'
import { transferSecondsToTime } from '@/utils'

const { pageData, tableData } = usePageData(getDetailQaReportApi, false)

const { dialogFormVisible, setDialogFormVisible, handleDialogClose } = useDialogForm()

const passingRate = computed<number>(() => parseInt((100 - failRate.value).toString()))

const failRate = computed<number>(() => {
  const num: number = tableData.value?.qaReportStat?.failNum || 0
  const rate: number = tableData.value?.qaReportStat?.checkNum || 0
  if (!rate) {
    return 0
  }
  return parseInt(((num / rate) * 100).toString())
})

const handleOpen = async (params: any) => {
  await pageData.handleSearch(params, 1)
  setDialogFormVisible(true)
}

defineExpose({ handleOpen })
</script>

<style lang="less" scoped>
.qualityReport {
  height: 80vh;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .title {
    font-size: 14px;
    font-weight: 500;
    color: #333333;
  }
  .header {
    display: flex;
    box-sizing: border-box;
    align-items: center;
    .left {
      width: 20%;
      display: flex;
      align-items: center;
      img {
        max-width: 120px;
        min-width: 60px;
      }
      span {
        margin-left: 10px;
        flex: 1;
        .ellipseLine();
        box-sizing: border-box;
      }
    }
    .right {
      width: 27%;
      display: flex;
      justify-content: space-around;
      align-items: center;
      box-sizing: border-box;
      .right-item {
        text-align: center;
      }
    }
    .center {
      margin-left: 15px;
      flex: 3;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      .center-item {
        height: 76px;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        .img {
          height: 76px;
          width: 76px;
          text-align: center;
          line-height: 76px;
          background-color: #edf2fc;
          border-radius: 10px 10px 10px 10px;
          font-size: 27px;
          color: var(--el-color-primary);
        }
        .text {
          height: 100%;
          margin-left: 10px;
          display: flex;
          flex-direction: column;
          justify-content: space-around;
          :nth-child(1) {
            font-size: 30px;
            font-weight: 500;
          }
        }
      }
      .center-item + .center-item {
        margin-left: 20px;
      }
    }
  }
  .main {
    flex: 1;
    box-sizing: border-box;
    overflow: hidden;
  }
  .tableList {
    margin-top: 20px;
    p {
      margin-bottom: 15px;
    }
  }
  .footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 15px;
  }
}
</style>
