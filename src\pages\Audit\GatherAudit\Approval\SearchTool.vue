<template>
  <el-form ref="ruleFormRef" :model="ruleForm">
    <div class="search-content">
      <div class="search-input">
        <div class="search-item">
          <el-form-item prop="taskName">
            <el-input
              class="form-item"
              v-model="ruleForm.taskName"
              placeholder="请输入任务名称/申请人"
              @keydown.enter="handleSearch"
            >
              <template #suffix>
                <SvgIcon @click="handleSearch" style="cursor: pointer" name="search" />
              </template>
            </el-input>
          </el-form-item>
        </div>
        <div class="search-item">
          <el-form-item prop="dataType">
            <el-select
              style="width: 100%"
              @change="handleSearch"
              v-model="ruleForm.dataType"
              placeholder="请选择入库方式"
              clearable
            >
              <el-option
                v-for="(p, i) in dataTypeOptions"
                :key="i"
                :label="p.dictName"
                :value="p.dictValue"
              />
            </el-select>
          </el-form-item>
        </div>
        <div class="search-item">
          <el-form-item prop="status">
            <el-select
              style="width: 100%"
              @change="handleSearch"
              v-model="ruleForm.status"
              placeholder="请选择审核状态"
              clearable
            >
              <el-option
                v-for="(p, i) in statusOptions"
                :key="i"
                :label="p.dictName"
                :value="p.dictValue"
              />
            </el-select>
          </el-form-item>
        </div>
      </div>
    </div>
  </el-form>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'
import type { FormInstance } from 'element-plus'
import useDictData from '@/hooks/useDictData'

const ruleFormRef = ref<FormInstance>()
const ruleForm = reactive<any>({})

const dataTypeOptions = useDictData('IMPORT_DATA_TYPE')

const statusOptions = useDictData('IMPORT_AUDIT_STATUS')

const handleSearch = () => {
  emit('handleSearch', ruleForm)
}

const emit = defineEmits<{
  (e: 'handleSearch', form: any): void
}>()
</script>
