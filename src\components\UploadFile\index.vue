<template>
  <div class="upload-file-wrapper">
    <Uploader
      ref="uploaderRef"
      class="uploader-app"
      :options="options"
      :fileStatusText="fileStatusText"
      :autoStart="false"
      @filesAdded="handleFilesAdded"
      @fileError="handleFileError"
      @fileSuccess="handleFileSuccess"
      @complete="handleComplete"
    >
      <UploaderUnsupport></UploaderUnsupport>

      <UploaderBtn v-if="isBtnShow" class="uploader-file-icon" :attrs="attrs" ref="uploadSpaceBtn">
      </UploaderBtn>
      <UploaderBtn class="uploader-file-icon" :attrs="attrs" ref="uploadBtnRef">
        选择文件
      </UploaderBtn>
      <uploader-btn
        class="uploader-file-icon"
        :attrs="attrs"
        :directory="true"
        ref="uploadDirBtnRef"
      >
        选择目录
      </uploader-btn>
      <UploaderList v-show="panelShow">
        <template #default="props">
          <div class="file-panel">
            <div class="file-title">
              <span class="title-span">
                上传列表<span class="count">（{{ props.fileList.length }}）</span>
              </span>
              <div class="operate">
                <el-button
                  plain
                  link
                  :title="collapse ? '展开' : '折叠'"
                  @click="collapse ? (collapse = false) : (collapse = true)"
                >
                  <template #icon>
                    <SvgIcon :name="collapse ? 'fullScreen' : 'semiSelect'" />
                  </template>
                </el-button>
                <el-button @click="handleClosePanel" plain link title="关闭">
                  <template #icon>
                    <SvgIcon name="closeBold" />
                  </template>
                </el-button>
              </div>
            </div>
            <!-- 正在上传的文件列表 -->
            <el-collapse-transition>
              <ul class="file-list" v-show="!collapse">
                <li
                  class="file-item"
                  :class="{ 'custom-status-item': !!file.statusStr }"
                  v-for="file in props.fileList"
                  :key="file.id"
                >
                  <uploader-file ref="fileItem" :file="file" :list="true"></uploader-file>
                  <!-- 自定义状态 -->
                  <span class="custom-status">{{ file.statusStr }}</span>
                </li>

                <div class="no-file" v-if="!props.fileList.length">暂无待上传文件!</div>
              </ul>
            </el-collapse-transition>
          </div>
        </template>
      </UploaderList>
    </Uploader>
  </div>
</template>

<script setup lang="ts">
import { computed, reactive, ref } from 'vue'
import Uploader from '@/components/SimpleUploader/components/Uploader.vue'
import UploaderUnsupport from '@/components/SimpleUploader/components/UploaderUnsupport.vue'
import UploaderBtn from '@/components/SimpleUploader/components/UploaderBtn.vue'
import UploaderList from '@/components/SimpleUploader/components/UploaderList.vue'
import SparkMD5 from 'spark-md5'
import useUserInfo from '@/store/useUserInfo'
import { ElMessage } from 'element-plus'
import { uploadFileApi } from '@/api/common'
import { useRoute } from 'vue-router'

const route = useRoute()

const userInfo = useUserInfo()
const filePath = computed<any>(() => route.query?.filePath || '/')
const dataCatalogId = computed<any>(() => route.query?.dataCatalogId)

// 上传分片大小
const CHUNK_SIZE: number = 5 * 1024 * 1024

// 上传参数的配置
const options = reactive<any>({
  // 上传地址
  target: uploadFileApi(),
  // 单文件上传
  singleFile: true,
  // 是否开启服务器分片校验。默认为 true
  testChunks: true,
  // 分片大小
  chunkSize: CHUNK_SIZE,
  // 一个文件以及上传过了是否还允许再次上传
  allowDuplicateUploads: true,
  //  上传文件时文件的参数名，默认 file
  fileParameterName: 'file',
  // 并发上传数，默认为 3
  simultaneousUploads: 3,
  /**
   * 判断分片是否上传，秒传和断点续传基于此方法
   * 这里根据实际业务来 用来判断哪些片已经上传过了 不用再重复上传了 [这里可以用来写断点续传！！！]
   */
  checkChunkUploadedByResponse: function (chunk: any, message: string) {
    let objMessage = JSON.parse(message)
    if ([200].includes(objMessage.status)) {
      let data = objMessage.data
      if (data.skipUpload) {
        // 分片已存在于服务器中
        return true
      }
      return (data.uploaded || []).indexOf(chunk.offset + 1) >= 0
    } else {
      return true
    }
  },
  //格式化时间
  parseTimeRemaining: function (_: any, parsedTimeRemaining: string) {
    return parsedTimeRemaining
      .replace(/\syears?/, '年')
      .replace(/\days?/, '天')
      .replace(/\shours?/, '小时')
      .replace(/\sminutes?/, '分钟')
      .replace(/\sseconds?/, '秒')
  },
  // 设置header头
  headers: {
    token: userInfo.token,
  },
  // 额外添加请求参数
  query: {},
})

// 上传文件类型校验
const attrs = reactive<any>({
  accept: '*',
})

// 修改上传状态
const fileStatusText = reactive<any>({
  success: '上传成功',
  error: '上传错误',
  uploading: '正在上传',
  paused: '停止上传',
  waiting: '等待中',
})

// 上传列表弹框显示
const panelShow = ref<boolean>(false)
// 上传列表弹框是否隐藏
const collapse = ref<boolean>(false)

// 上传组件实例
const uploaderRef = ref<any>(null)
const uploaderInstance = computed<any>(() => uploaderRef.value?.uploader)

// 手动上传按钮是否显示
const uploadSpaceBtn = ref<any>()
const isBtnShow = ref<boolean>(true)

// 上传组件预处理
const uploadBtnRef = ref<any>(null)
const uploadDirBtnRef = ref<any>(null)

// 上传时间触发 type：1 文件上传  2文件夹上传  3选择文件类型上传
const uploadWay = ref<number>(1)
const handlePrepareUpload = (type: number, format: string = '*') => {
  uploadWay.value = type
  switch (type) {
    case 1:
      uploadBtnRef.value.handleClickUpload()
      break
    case 2:
      uploadDirBtnRef.value.handleClickUpload()
      break
    case 3:
      attrs.accept = format
      uploaderInstance.value.fileList = uploaderInstance.value.fileList.filter(
        (file: any) => !file.isSelectUpload,
      )
      uploaderInstance.value.files = uploaderInstance.value.files.filter(
        (file: any) => !file.isSelectUpload,
      )
      uploadSpaceBtn.value.handleClickUpload()
      break
    default:
      break
  }
}

//  手动上传
const handleUpload = (data: any) => {
  uploaderInstance.value.fileList[uploaderInstance.value.fileList.length - 1].name = data.fileName
  uploaderInstance.value.fileList[uploaderInstance.value.fileList.length - 1].relativePath =
    data.fileName
  fileComputeMD5(
    reactive<any>(uploaderInstance.value.fileList[uploaderInstance.value.fileList.length - 1]),
    data,
  )
  panelShow.value = true
  collapse.value = false
}

// 上传前的 校验
const handleFilesAdded = (files: any[]) => {
  if ([3].includes(uploadWay.value)) {
    files[0].isSelectUpload = true
    // files[0].statusStr = '读取中'
    // files[0].pause()
    emit('getFileData', files[0])
    return
  }
  panelShow.value = true
  collapse.value = false
  files.forEach((file) => {
    fileComputeMD5(reactive<any>(file))
  })
}

// 生成文件md5
const fileComputeMD5 = (file: any, params: any = {}) => {
  const fileReader = new FileReader()
  const FilePrototype = File.prototype as any
  const blobSlice: any = FilePrototype.slice || FilePrototype.mozSlice || FilePrototype.webkitSlice
  let currentChunk: number = 0
  let chunks = Math.ceil(file.size / CHUNK_SIZE)
  let spark = new SparkMD5.ArrayBuffer()
  file.isSelectUpload = undefined
  // 文件状态设为"计算MD5"
  file.cmd5 = true //文件状态为“计算md5...”
  file.statusStr = '读取中'
  file.pause()
  loadNext()
  fileReader.onload = (e: any) => {
    spark.append(e.target.result)
    if (currentChunk < chunks) {
      currentChunk++
      loadNext()
    } else {
      let md5 = spark.end()
      spark.destroy() //释放缓存
      file.uniqueIdentifier = md5 //将文件md5赋值给文件唯一标识
      file.cmd5 = false //取消计算md5状态
      file.statusStr = undefined
      if ([3].includes(uploadWay.value)) {
        file.uploader.opts.query = {
          filePath: filePath.value,
          dataCatalogId: dataCatalogId.value,
          fileTypeId: params.fileTypeId,
        }
      } else {
        file.uploader.opts.query = { filePath: filePath.value, dataCatalogId: dataCatalogId.value }
      }
      file.resume() //开始上传
    }
  }
  fileReader.onerror = function () {
    ElMessage.error(`文件${file.name}读取出错，请检查该文件`)
    file.cancel()
  }
  function loadNext() {
    let start = currentChunk * CHUNK_SIZE
    let end = start + CHUNK_SIZE >= file.size ? file.size : start + CHUNK_SIZE
    fileReader.readAsArrayBuffer(blobSlice.call(file.file, start, end))
  }
}

// 文件成功上传
const handleComplete = () => {
  ElMessage.success('上传成功')
  emit('handleRefresh')
}

// 上传成功校验
const handleFileSuccess = (_rootFile: any, file: any, response: any) => {
  if (!response) {
    file.statusStr = '上传失败'
    return
  }
  const result: any = JSON.parse(response)
  if (![200].includes(result.status)) {
    file.statusStr = '上传失败'
    ElMessage.error('上传失败')
  }
}

// 上传错误
const handleFileError = (_rootFile: any, _file: any, response: any) => {
  ElMessage.error(response || '网络异常，请稍后刷新重试')
}

// 关闭上传列表弹框
const handleClosePanel = () => {
  uploaderInstance.value.cancel()
  panelShow.value = false
}

defineExpose({ handlePrepareUpload, handleUpload })
const emit = defineEmits<{
  (e: 'handleRefresh'): void
  (e: 'getFileData', file: any): void
}>()
</script>

<style lang="less" scoped>
.upload-file-wrapper {
  position: fixed;
  z-index: 20;
  right: 16px;
  bottom: 16px;
  .uploader-app {
    width: 560px;
  }
  .file-panel {
    background-color: @withe;
    border: 1px solid #e2e2e2;
    border-radius: 7px 7px 0 0;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
    .file-title {
      display: flex;
      height: 40px;
      line-height: 40px;
      padding: 0 15px;
      border-bottom: 1px solid #ddd;
      .title-span {
        padding-left: 0;
        margin-bottom: 0;
        font-size: 16px;
        color: #1d2129;
        .count {
          color: #909399;
        }
      }
      .operate {
        flex: 1;
        text-align: right;
        :deep(.el-button--text) {
          color: #303133;
          i[class^='el-icon-'] {
            font-weight: 600;
          }
          &:hover {
            .el-icon-full-screen,
            .el-icon-minus {
              color: #67c23a;
            }
            .el-icon-close {
              color: #f56c6c;
            }
          }
        }
      }
    }

    .file-list {
      position: relative;
      height: 260px;
      overflow-x: hidden;
      overflow-y: auto;
      background-color: @withe;
      font-size: 12px;
      list-style: none;
      .no-scrollbar(2px);
      color: #1d2129;
      .file-item {
        position: relative;
        background-color: @withe;

        :deep(.uploader-file) {
          height: 40px;
          line-height: 40px;
          .uploader-file-progress {
            border: 1px solid #67c23a;
            border-right: none;
            border-left: none;
            background: #e1f3d8;
          }
          .uploader-file-name {
            width: 44%;
          }
          .uploader-file-size {
            width: 16%;
          }
          .uploader-file-meta {
            display: none;
          }
          .uploader-file-status {
            width: 30%;
            text-indent: 0;
          }
          .uploader-file-actions > span {
            margin-top: 12px;
          }
        }
        :deep(.uploader-file[status='success']) {
          .uploader-file-progress {
            border: none;
          }
        }
      }

      .file-item.custom-status-item {
        :deep(.uploader-file-status) {
          visibility: hidden;
        }
        .custom-status {
          position: absolute;
          top: 0;
          right: 10%;
          width: 30%;
          height: 40px;
          line-height: 40px;
        }
      }
    }

    &.collapse {
      .file-title {
        background-color: #e7ecf2;
      }
    }
  }

  .no-file {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 16px;
  }

  :deep(.uploader-file-icon) {
    display: none;
  }

  :v-deep(.uploader-file-actions > span) {
    margin-right: 6px;
  }
}
</style>
