<template>
  <el-form ref="ruleFormRef" :model="ruleForm">
    <div class="search-content">
      <div class="search-input">
        <div style="width: 150px; margin-right: 10px">
          <el-form-item prop="fieldName">
            <el-select
              v-if="props.isSelect"
              class="form-item"
              v-model="ruleForm.fieldName"
              placeholder="请选择查询字段"
              clearable
            >
              <el-option
                v-for="p in props.fieldOptions"
                :key="p.fieldName"
                :label="p.fieldName"
                :value="p.fieldName"
              />
            </el-select>
            <el-input
              v-else
              class="form-item"
              v-model="ruleForm.fieldName"
              placeholder="请输入查询字段"
              clearable
            />
          </el-form-item>
        </div>
        <div class="search-item" style="margin-right: 15px">
          <el-form-item label="等于" prop="fieldValue">
            <el-input
              class="form-item"
              v-model="ruleForm.fieldValue"
              placeholder="请输入查询内容"
              clearable
            />
          </el-form-item>
        </div>
        <el-button type="primary" @click="handleSearch">
          <template #icon>
            <SvgIcon name="search" />
          </template>
          查询
        </el-button>
      </div>
    </div>
  </el-form>
</template>

<script setup lang="ts">
import { reactive, ref, watch } from 'vue'
import type { FormInstance } from 'element-plus'
import { useRoute } from 'vue-router'

const props = withDefaults(defineProps<{ fieldOptions?: any; isSelect?: boolean }>(), {
  fieldOptions: [],
  isSelect: true,
})

const ruleFormRef = ref<FormInstance>()
const ruleForm = reactive<any>({})

const route = useRoute()

const handleSearch = () => {
  emit('handleSearch', ruleForm)
}

watch(
  () => route.query.dbname,
  () => {
    ruleFormRef.value?.resetFields()
  },
)

const emit = defineEmits<{
  (e: 'handleSearch', form: any): void
}>()
</script>

<style lang="less" scoped>
.search-input {
  :deep(.el-form-item) {
    margin-bottom: 0;
  }
}
</style>
