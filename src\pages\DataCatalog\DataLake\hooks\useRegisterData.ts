import { getAdmincodeTreeListApi, productLabelPageApi } from '@/api/data_catalog'
import useDictType from '@/hooks/useDictType'
import { ElMessage } from 'element-plus'
import { ref } from 'vue'

const useRegisterData = () => {
  // 获取行政树
  const treeAsdProps = {
    label: 'title',
    children: 'children'
  }
  const asdData = ref<any>([])
  const getAsdTree = async () => {
    try {
      const { data, message, status } = await getAdmincodeTreeListApi()
      if ([200].includes(status)) {
        asdData.value = data
      } else {
        ElMessage.error(message)
      }
    } catch (error) {}
  }
  getAsdTree()

  // 数据标签
  const productLabelData = ref<any>([])
  const getProductLabelData = async () => {
    try {
      const { data, message, status } = await productLabelPageApi({ pageNo: 1, pageSize: 100 })
      if ([200].includes(status)) {
        productLabelData.value = data.rows || []
      } else {
        ElMessage.error(message)
      }
    } catch (error) {}
  }
  getProductLabelData()

  //公开范围
  const sharedScopeOptions = useDictType('product_shared_scope')

  return { treeAsdProps, asdData, productLabelData, sharedScopeOptions }
}

export default useRegisterData
