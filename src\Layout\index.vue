<template>
  <div :class="['layout']">
    <Header />
    <div class="layout-main">
      <RouterView />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import Header from './Header.vue'
import { RouterView } from 'vue-router'

const route: any = useRoute()
const margin = computed<string>(() =>
  ['dataSearch', 'catalogConfig', 'catalogVersion', 'editCatalog', 'dm_home'].includes(route.name)
    ? '0px'
    : '1.0417vw',
)
</script>

<style scoped lang="less">
.layout {
  width: 100%;
  height: 100vh;
  box-sizing: border-box;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  background-color: @bgColor;
  .layout-main {
    flex: 1;
    box-sizing: border-box;
    overflow: hidden;
    margin: v-bind(margin);
  }
}
</style>
