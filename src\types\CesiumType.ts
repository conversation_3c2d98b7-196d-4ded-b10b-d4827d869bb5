export interface mapOperate {
  zoomIn: () => void
  zoomOut: () => void
  zoomToCenter: (center?: [number, number], height?: number) => void
  getCurrentViewInfo: () => void
}

export interface MapTools {
  geoMaterial: any
  outlineColor: any
  drawHandler: any
  entity: any
  registerEvents: (type: string) => void
  removeAllEvent: () => void
  clearEntity: () => void
  addPolygon: (positionData: any) => any
  addRectangle: (circlePosition: any) => any
  addCircle: (callback?: any) => any
  addPoint: (pointInfo: any) => any
  cartesian3ToCartographic: (cartesian3: any) => any
  getDistance: (start: any, end: any) => any
  getPositionPoints: (rectangle: any, type: string) => any
}
