<template>
  <el-form ref="formRef" :model="{ formData }" size="small" :disabled="disabled" class="form-demo">
    <el-row :gutter="10">
      <el-col :span="span" v-for="(p, i) in formData" :key="i">
        <el-form-item
          :rules="p.formRequired ? rules.formValue : null"
          :label="p.formLabel"
          :prop="'formData.' + i + '.formValue'"
          :key="i"
        >
          <template v-if="['input'].includes(p.formType)">
            <el-input
              :minlength="p.formMinLength"
              :maxlength="p.formMaxLength"
              v-model="p.formValue"
              placeholder="请输入"
            />
          </template>
          <template v-if="['switch'].includes(p.formType)">
            <el-switch v-model="p.formValue" />
          </template>
          <template v-if="['checkbox-group'].includes(p.formType)">
            <el-checkbox-group v-model="p.formValue">
              <el-checkbox
                v-for="(item, i) in p.children"
                :key="i"
                :value="item.formValue"
                :label="item.formLabel"
              />
            </el-checkbox-group>
          </template>
          <template v-if="['select'].includes(p.formType)">
            <el-select v-model="p.formValue" placeholder="请选择">
              <el-option
                v-for="(item, i) in p.children"
                :key="i"
                :label="item.formLabel"
                :value="item.formValue"
              />
            </el-select>
          </template>
          <template v-if="['radio-group'].includes(p.formType)">
            <el-radio-group v-model="p.formValue">
              <el-radio
                v-for="(item, i) in p.children"
                :key="i"
                :value="item.formValue"
                :label="item.formValue"
              ></el-radio>
            </el-radio-group>
          </template>
          <template v-if="['textarea'].includes(p.formType)">
            <el-input
              v-model="p.formValue"
              :minlength="p.formMinLength"
              :maxlength="p.formMaxLength"
              show-word-limit
              type="textarea"
            />
          </template>
          <template v-if="['cascader'].includes(p.formType)">
            <el-cascader v-model="p.formValue" :options="p.children" :prop="cascaderProp" />
          </template>
          <template v-if="['number'].includes(p.formType)">
            <el-input-number
              v-model="p.formValue"
              :precision="p.formPrecision"
              :min="p.formMinLength"
              :max="p.formMaxLength"
              controls-position="right"
            />
          </template>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import { reactive } from 'vue'
import { type FormRules } from 'element-plus'
import useDialogForm from '@/hooks/useDialogForm'

const { formRef, validateForm } = useDialogForm()

const rules = reactive<FormRules<any>>({
  formValue: [{ required: true, message: '该数据是必填！', trigger: 'blur' }],
})

const cascaderProp: any = {
  value: 'formValue',
  label: 'formLabel',
}

defineExpose({ validateForm })

withDefaults(defineProps<{ formData?: any; span?: number; disabled?: boolean }>(), {
  formData: [],
  span: 24,
  disabled: false,
})
</script>

<style lang="less" scoped>
.form-demo {
  width: 100%;
  overflow-x: hidden;
  :deep(.el-checkbox-group) {
    width: 100%;
    .el-checkbox {
      margin-right: 0;
      display: flex !important;
      overflow: hidden;
      .el-checkbox__label {
        flex: 1;
        box-sizing: border-box;
        width: 100%;
        margin-right: 15px;
        .ellipseLine();
      }
    }
  }
}
</style>
