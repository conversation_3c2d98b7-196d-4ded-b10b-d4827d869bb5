<template>
  <MainLayout>
    <template #header>
      <SearchTool
        @handle-create="EditDetailDialogRef.handleOpen(null, 1)"
        @handle-search="
          (form: any) => {
            pageData.handleSearch(form, 1)
          }
        "
      />
    </template>
    <el-table
      v-loading="pageData.loading"
      header-cell-class-name="common-table-header"
      cell-class-name="common-table-cell"
      ref="multipleTableRef"
      :data="tableData"
      height="100%"
      style="width: 100%"
      row-key="tid"
    >
      <el-table-column property="modelName" label="模型名称" show-overflow-tooltip />
      <el-table-column property="dataTypeName" label="数据类型" show-overflow-tooltip />
      <el-table-column property="dataCatalogName" label="适用数据目录" show-overflow-tooltip>
        <template #default="scope">
          {{ scope.row.dataCatalogName || '--' }}
        </template>
      </el-table-column>
      <el-table-column property="fileTypeName" label="适用数据" show-overflow-tooltip />
      <el-table-column property="createTime" label="模型组成" show-overflow-tooltip>
        <template #default="scope">
          {{ ruleNames(scope.row.modelRuleList) }}
        </template>
      </el-table-column>
      <el-table-column property="notes" label="模型描述" show-overflow-tooltip />
      <el-table-column property="scoreName" label="健康度评分标准" show-overflow-tooltip />
      <el-table-column
        v-if="
          permission.hasButton(['qualityModel_edit', 'qualityModel_details', 'qualityModel_del'])
        "
        label="操作"
        width="180"
        fixed="right"
      >
        <template #default="scope">
          <el-button
            v-if="[1].includes(scope.row.dataType) && permission.hasButton('qualityModel_checked')"
            @click="QualityRuleTableRef.handleQualityOpen(scope.row)"
            title="质检模板"
            class="common-icon-btn"
            type="primary"
            text
            link
          >
            <template #icon>
              <SvgIcon name="CheckIcon" />
            </template>
          </el-button>
          <el-button
            v-if="permission.hasButton('qualityModel_edit')"
            @click="EditDetailDialogRef.handleOpen(scope.row, 2)"
            class="common-icon-btn"
            type="primary"
            text
            link
            title="编辑"
          >
            <template #icon>
              <SvgIcon name="edit" />
            </template>
          </el-button>
          <el-button
            v-if="permission.hasButton('qualityModel_details')"
            @click="EditDetailDialogRef.handleOpen(scope.row, 3)"
            title="详情"
            class="common-icon-btn"
            type="primary"
            text
            link
          >
            <template #icon>
              <SvgIcon name="details" />
            </template>
          </el-button>
          <el-button
            v-if="!scope.row.isSystem && permission.hasButton('qualityModel_del')"
            @click="handleDel(scope.row.tid)"
            title="删除"
            class="common-icon-btn"
            type="danger"
            plain
            link
          >
            <template #icon>
              <SvgIcon name="delete" />
            </template>
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <template #footer>
      <el-pagination
        v-model:currentPage="pageData.page"
        v-model:page-size="pageData.limit"
        :page-sizes="pageData.pageSizes"
        background
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageData.total"
        @size-change="pageData.handleSizeChange"
        @current-change="pageData.handleCurrentChange"
      />
    </template>
    <EditDetailDialog ref="EditDetailDialogRef" @handle-refresh="pageData.handleSearch(null, 1)" />
    <QualityRuleTable
      ref="QualityRuleTableRef"
      @handle-refresh="pageData.handleSearch(null, 1)"
      :open-type="3"
    />
  </MainLayout>
</template>

<script setup lang="ts">
import MainLayout from '@/components/MainLayout/index.vue'
import SearchTool from './SearchTool.vue'
import usePageData from '@/hooks/usePageData'
import { qaModelListApi, qaModelRemoveByIdApi } from '@/api/data_governance'
import EditDetailDialog from './EditDetailDialog.vue'
import { ref } from 'vue'
import useUserInfo from '@/store/useUserInfo'
import { ElMessage, ElMessageBox } from 'element-plus'
import QualityRuleTable from '@/pages/DataIntegration/DataGather/AddGather/QualityRuleTable.vue'

const permission = useUserInfo()

const QualityRuleTableRef = ref<any>()

const { tableData, pageData } = usePageData(qaModelListApi)

const EditDetailDialogRef = ref<any>()

const handleDel = (tid: string) => {
  ElMessageBox.confirm('确认要删除该数据嘛？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    try {
      const { status, message } = await qaModelRemoveByIdApi({ tid })
      if ([200].includes(status)) {
        ElMessage.success(message)
        pageData.handleSearch(null, 1)
      } else {
        ElMessage.error(message)
      }
    } catch (error) {
      console.error(error)
    }
  })
}

const ruleNames = (row: any[]) => {
  return row.map((item: any) => item.ruleName).join(',')
}
</script>

<style scoped></style>
