<template>
  <div class="data-import-detail">
    <div class="form-data">
      <ElScrollbar>
        <ElForm ref="formRef" class="form-main" :model="formData" :rules="rules" label-width="auto">
          <GatherFormData v-if="formData" :formData>
            <ElCol :span="24">
              <ElFormItem label="审核状态" prop="statusName">
                {{ formData.statusName || '--' }}
              </ElFormItem>
            </ElCol>
            <template v-if="[0].includes(formData.status)">
              <ElCol :span="24">
                <ElFormItem label="取消申请" prop="auditStatus">
                  <ElRadioGroup v-model="formData.auditStatus">
                    <ElRadio :value="3" label="是" />
                  </ElRadioGroup>
                </ElFormItem>
              </ElCol>
              <ElCol :span="24">
                <el-form-item label="取消原因" prop="auditRemark">
                  <el-input
                    :disabled="![0].includes(formData.status)"
                    :rows="6"
                    v-model="formData.auditRemark"
                    placeholder="请输入取消原因"
                    type="textarea"
                  />
                </el-form-item>
              </ElCol>
            </template>
            <ElCol v-if="![0].includes(formData.status)" :span="24">
              <el-form-item label="拒绝原因">
                <el-input
                  disabled
                  :rows="6"
                  v-model="formData.auditRemark"
                  placeholder="请输入拒绝原因"
                  type="textarea"
                />
              </el-form-item>
            </ElCol>
          </GatherFormData>
        </ElForm>
      </ElScrollbar>
    </div>
    <div class="footer-btn">
      <el-button @click="router.back">取消</el-button>
      <el-button @click="handleSubmit" :loading="loading" type="primary">确定</el-button>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { auditDetailApi, updateAuditApi } from '@/api/gather_audit'
import { ElMessage, ElForm } from 'element-plus'
import { ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import GatherFormData from './GatherFormData.vue'

const router = useRouter()
const route = useRoute()

const formRef = ref<any>()
const formData = ref<any>()
const rules = ref<any>({
  auditRemark: [{ required: true, message: '请输入取消原因！', trigger: 'blur' }],
  auditStatus: [{ required: true, message: '请选择取消申请！', trigger: 'blur' }],
})
const getDetailData = async () => {
  try {
    const { data, status, message } = await auditDetailApi({ taskId: route.query.tid })
    if ([200].includes(status)) {
      formData.value = {
        ...JSON.parse(data.taskVO.reqParam),
        status: data.status,
        statusName: data.statusName,
      }
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}

getDetailData()

const loading = ref<boolean>(false)
const handleSubmit = async () => {
  if (![0].includes(formData.value.status)) {
    router.back()
    return
  }
  await formRef.value?.validate(async (valid: boolean) => {
    if (!valid) return
    try {
      loading.value = true
      const { message, status } = await updateAuditApi({
        taskId: route.query.tid,
        auditRemark: formData.value.auditRemark,
        status: formData.value.auditStatus,
      })
      if ([200].includes(status)) {
        ElMessage.success(message)
        router.back()
      } else {
        ElMessage.error(message)
      }
      loading.value = false
    } catch (error) {
      loading.value = false
    }
  })
}
</script>
<style scoped lang="less">
.data-import-detail {
  height: 100%;
  background-color: @withe;
  overflow: hidden;
  box-sizing: border-box;
  padding: 30px;
  display: flex;
  flex-direction: column;
  .form-data {
    flex: 1;
    box-sizing: border-box;
    overflow: hidden;
    .form-main {
      width: 900px;
    }
  }
  .footer-btn {
    width: 900px;
    text-align: right;
  }
}
</style>
