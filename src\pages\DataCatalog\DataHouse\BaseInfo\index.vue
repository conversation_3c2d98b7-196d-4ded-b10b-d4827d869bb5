<template>
  <div class="baseInfo">
    <ElForm :model="formData" label-width="auto">
      <ElFormItem label="目录名称">
        <span>{{ formData.catalogAlias }}</span>
      </ElFormItem>
      <ElFormItem label="目录分类">
        <span>{{ [2].includes(formData.bsType) ? '数据库' : '菜单' }}</span>
      </ElFormItem>
      <ElFormItem label="描述">
        <span>{{ formData.catalogDesc }}</span>
      </ElFormItem>
      <ElFormItem label="创建时间">
        <span>{{ formData.createTime }}</span>
      </ElFormItem>
    </ElForm>
  </div>
</template>
<script setup lang="ts">
import { catalogQueryByIdApi } from '@/api/data_house'
import { ElForm, ElFormItem, ElMessage } from 'element-plus'
import { computed, ref, watch } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()

const formData = ref<any>({})
const getData = async () => {
  try {
    const { dataCatalogId } = route.query
    const { data, message, status } = await catalogQueryByIdApi(dataCatalogId as string)
    if ([200].includes(status)) {
      formData.value = data
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}
getData()

const dataCatalogId = computed(() => route.query.dataCatalogId as string)
watch(dataCatalogId, (val: string) => {
  if (val) {
    getData()
  }
})
</script>
<style lang="less" scoped>
.baseInfo {
  height: 100%;
  box-sizing: border-box;
  box-shadow: 0px 0px 7px 1px rgba(0, 0, 0, 0.05);
  background-color: @withe;
  padding: 1.0417vw;
}
</style>
