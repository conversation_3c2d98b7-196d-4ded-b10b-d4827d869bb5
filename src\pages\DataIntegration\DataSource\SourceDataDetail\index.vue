<template>
  <div class="SourceDataDetail">
    <Breadcrumb />
    <div class="SourceDataDetail-main">
      <AsideMenu :tableList="tableList" />
      <div class="main-content">
        <TableList v-if="route.query.dbname" />
        <DataBaseDetail v-else :rowData="rowData" :tableList="tableList" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import Breadcrumb from '@/pages/DataIntegration/DataSource/components/Breadcrumb.vue'
import AsideMenu from '@/pages/DataIntegration/DataSource/components/AsideMenu.vue'
import TableList from './TableList.vue'
import DataBaseDetail from '@/pages/DataIntegration/DataSource/components/DataBaseDetail.vue'
import { queryByIdApi, queryPgListTableApi } from '@/api/data_source'
import { useRoute } from 'vue-router'
import { ref } from 'vue'
import { ElMessage } from 'element-plus'

const route = useRoute()

// 获取详情
const rowData = ref<any>({})
const getDataDetail = async () => {
  try {
    const { data, status, message } = await queryByIdApi(route.query.id)
    if ([200].includes(status)) {
      rowData.value = data
    } else {
      ElMessage.error(message)
    }
  } catch (error) {
    console.error(error)
  }
}
getDataDetail()

// 获取数据库列表
const tableList = ref<any[]>([])
const getPgListTable = async () => {
  try {
    const { data, message, status } = await queryPgListTableApi({ dataSourceId: route.query.id })
    if ([200].includes(status)) {
      tableList.value = data
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}
getPgListTable()
</script>

<style lang="less" scoped>
.SourceDataDetail {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;

  overflow: hidden;
  .SourceDataDetail-main {
    margin-top: 15px;
    flex: 1;
    display: flex;
    box-sizing: border-box;
    overflow: hidden;
    .main-content {
      width: calc(100% - 215px);
      height: 100%;
      box-sizing: border-box;
      margin-left: 15px;
      background-color: @withe;
    }
  }
}
</style>
