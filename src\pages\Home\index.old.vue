<template>
  <div class="home">
    <div class="home-layout">
      <div class="home-main">
        <div class="main-aside">
          <div class="aside-item">
            <CommonTile name="数据概览" @handleChange="handleDataOverview" />
            <LeftTop :data="allFilterData" />
          </div>
          <div class="aside-item">
            <CommonTile :name="`${activeObj?.desc}概览`" @handleChange="handleSelectRasterUnit" />
            <LeftCenter v-if="rasterData.length" :rasterData="rasterData" />
          </div>
          <div class="aside-item">
            <CommonTile name="数据占比分析" @handleChange="handleDataPercent" />
            <LeftBottom v-if="dataPiePercent.length" :dataPercent="dataPiePercent" />
          </div>
        </div>
        <div class="main-content">
          <CenterTop />
        </div>
        <div class="main-aside">
          <div class="aside-item">
            <CommonTile name="服务统计" :isActiveShow="false" />
            <RightTop />
          </div>
          <div class="aside-item" style="flex: 2">
            <CommonTile name="数据来源分析" :isActiveShow="false" />
            <RightBottom />
          </div>
        </div>
      </div>
    </div>
    <video
      class="video-background"
      preload="auto"
      loop
      playsinline
      autoplay
      :src="bg"
      tabindex="-1"
      muted
      :playbackRate="1.0"
    ></video>
  </div>
</template>

<script setup lang="ts">
import bg from '@/assets/homeImg/bg.mp4'
import CommonTile from './CommonTile.vue'
import LeftTop from './LeftTop.vue'
import LeftCenter from './LeftCenter.vue'
import LeftBottom from './LeftBottom.vue'
import RightTop from './RightTop.vue'
import RightBottom from './RightBottom.vue'
import CenterTop from './CenterTop.vue'
import { dataPercentApi, dataWithTypeApi } from '@/api/home'
import { ElMessage } from 'element-plus'
import { ref, reactive, computed, onUnmounted } from 'vue'

// 数据概览
const allData = ref<any[]>([])
const allDataType = ref<number>(1)
const allFilterData = computed(() =>
  allData.value.map((item: any) => ({
    desc: item.desc,
    code: item.code,
    data: [1].includes(allDataType.value) ? item.numberValue : item.data,
    unit: [1].includes(allDataType.value) ? '个' : item.unit,
  })),
)
const handleDataOverview = (value: number) => {
  allDataType.value = value
}
const getDataOverview = async () => {
  try {
    const { status, data, message } = await dataWithTypeApi()
    if ([200].includes(status)) {
      allData.value = data
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}

// 栅格数据概览
const activeObj = computed<any>(() => allData.value[timeData.activeIndex] || undefined)
const rasterDataType = ref<number>(1)
const rasterData = computed<any[]>(() =>
  activeObj.value
    ? activeObj.value?.children.map((item: any) => ({
        realValue: [1].includes(rasterDataType.value) ? item.numberValue : item.data,
        data: [1].includes(rasterDataType.value) ? item.numberValue : item.data,
        desc: item.desc,
        unit: [1].includes(rasterDataType.value) ? '个' : item.unit,
      }))
    : [],
)

const timeData = reactive<any>({
  timer: null,
  activeIndex: 0,
})

const handleSelectRasterUnit = (value: number) => {
  rasterDataType.value = value
}
// 每五秒渲染一次
const handleSetInterval = () => {
  timeData.timer = setInterval(() => {
    timeData.activeIndex++
    if (timeData.activeIndex > 3) {
      timeData.activeIndex = 0
    }
  }, 5000)
}

const initData = async () => {
  try {
    await getDataOverview()
    handleSetInterval()
  } catch (error) {}
}
initData()

// 数据占比分析
const dataPercent = ref<any[]>([])
const dataPercentType = ref<number>(1)
const dataPiePercent = computed(() =>
  dataPercent.value.map((item: any) => ({
    desc: item.desc,
    data: [1].includes(dataPercentType.value) ? item.numberPercent : item.storagePercent,
  })),
)
const handleDataPercent = (value: number) => {
  dataPercentType.value = value
}
const getDataPercent = async () => {
  try {
    const { data, status, message } = await dataPercentApi()
    if ([200].includes(status)) {
      dataPercent.value = data
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}
getDataPercent()

onUnmounted(() => {
  if (timeData.timer) {
    clearInterval(timeData.timer)
    timeData.timer = null
  }
})
</script>

<style scoped lang="less">
.home {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
  font-size: 0;
  background-color: none;
  .home-layout {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    left: 0;
    height: 100%;
    z-index: 2;
    display: flex;
    flex-direction: column;
  }
  .home-main {
    box-sizing: border-box;
    flex: 1;
    display: flex;
    .main-aside {
      width: 20.8333vw;
      height: 100%;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      .aside-item {
        padding: 1.0417vw;
        box-sizing: border-box;
        flex: 1;
        background-color: rgba(255, 255, 255, 0.5);
        border-radius: 0.3125vw 0.3125vw 0.3125vw 0.3125vw;
        box-shadow: 0px 0.4167vw 0.4167vw 0.4167vw rgba(0, 0, 0, 0.025);
        display: flex;
        flex-direction: column;
      }
      .aside-item + .aside-item {
        margin-top: 0.5208vw;
      }
    }
    .main-content {
      flex: 1;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
    }
  }
  .video-background {
    position: absolute;
    width: 100%;
    height: 100%;
    /*保证视频充满屏幕*/
    object-fit: cover;
    z-index: 0;
    outline: none !important;
    mix-blend-mode: darken;
    &:focus {
      outline: none !important;
    }
    // left: 1.8229vw;
    // top: 2.0833vw;
    // top: 50%;
    // left: 50%;
    // transform: translate(-50%, -50%);
  }
}
</style>
