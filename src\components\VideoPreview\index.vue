<template>
  <transition name="fade">
    <div v-if="showViewer" class="common-preview-wrapper">
      <div class="tip-wrapper">
        <div class="video-name">
          <span>{{ getFileNameComplete(activeVideo) }}</span>
          <span>{{ calculateFileSize(Number(activeVideo.fileSize)) }}</span>
        </div>
        <div class="tip-right">
          <SvgIcon name="closeBold" @click="handleClose" class="close-icon" />
        </div>
      </div>
      <div class="map-wrapper">
        <div class="wrapper-left">
          <video ref="videoPlayer" class="video-js"></video>
        </div>
        <div class="wrapper-right">
          <div class="title">播放列表</div>
          <ul class="right-main">
            <li
              v-for="(p, i) in videoList"
              :key="p.id"
              :class="{ active: [i].includes(activeIndex) }"
              @click="handleChange(i)"
            >
              <span>{{ getFileNameComplete(p) }}</span>
              <span>{{ calculateFileSize(Number(p.fileSize)) }}</span>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </transition>
</template>

<script setup lang="ts">
import { computed, nextTick, reactive, ref } from 'vue'
import { calculateFileSize, getFileNameComplete } from '@/utils/fileUtils'
import videojs from 'video.js'
import 'video.js/dist/video-js.css'

// 视频对象
const player = ref<any>(null)

// 视频实例
const videoPlayer = ref<any>(null)

// 当前播放视频
const activeIndex = ref<number>(0)
const activeVideo = computed<any>(() => videoList.value[activeIndex.value] || {})

// 视频列表
const videoList = ref<any[]>([])

// 视频源
const source = computed<any>(() => ({
  type: `video/mp4`,
  src: videoList.value[activeIndex.value]?.url || '',
  extendName: 'mp4',
}))

// 视频配置
const options = reactive<any>({
  playbackRates: [0.5, 1.0, 1.5, 2.0], // 播放速度
  autoplay: false, //  如果true,浏览器准备好时开始播放。
  muted: false, // 默认情况下将会消除任何音频。
  loop: false, // 导致视频一结束就重新开始。
  preload: 'auto', // 建议浏览器在<video>加载元素后是否应该开始下载视频数据。auto浏览器选择最佳行为,立即开始加载视频（如果浏览器支持）
  language: 'zh-CN',
  aspectRatio: '16:9', // 将播放器置于流畅模式，并在计算播放器的动态大小时使用该值。值应该代表一个比例 - 用冒号分隔的两个数字（例如"16:9"或"4:3"）
  fluid: true, // 当true时，Video.js player将拥有流体大小。换句话说，它将按比例缩放以适应其容器。
  sources: [], //  视频源信息
  notSupportedMessage: '此视频暂无法播放，请稍后再试',
  controls: true,
  controlBar: {
    timeDivider: true,
    durationDisplay: true,
    remainingTimeDisplay: false,
    fullscreenToggle: true, // 全屏按钮
  },
})

// 视频播放
const handlePlay = () => {
  player.value?.pause()
  player.value?.currentTime(0) //  跳到 0 秒
  player.value?.src(source.value) //  动态设置视频源
  player.value?.play() //  加载完毕后自动播放
}

const initPlayer = () => {
  player.value = videojs(videoPlayer.value, options, () => {
    player.value?.on('ratechange', () => {})
    player.value?.reloadSourceOnError({
      getSource: (reload: any) => {
        console.log('reloading because of an error')
        reload({
          src: `${source.value.src}&rnd=${new Date()}`,
        })
      },
      errorInterval: 5,
    })
  })
}

const handleChange = (index: number) => {
  activeIndex.value = index
  nextTick(() => {
    handlePlay()
  })
}

const showViewer = ref<boolean>(false)
const handleOpenPreview = (urlList: any, initialIndex: number) => {
  console.log(urlList, 'ssss')
  showViewer.value = true
  nextTick(() => {
    videoList.value = urlList
    activeIndex.value = initialIndex
    options.sources = [source.value]
    initPlayer()
  })
}

const handleClose = () => {
  player.value?.dispose()
  player.value = null
  nextTick(() => {
    showViewer.value = false
  })
}

defineExpose({ handleOpenPreview })
</script>

<style scoped lang="less">
.common-preview-wrapper {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  color: @withe;
  .tip-wrapper {
    background: rgba(0, 0, 0, 1);
    padding: 0 48px;
    width: 100%;
    height: 48px;
    line-height: 48px;
    font-size: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-sizing: border-box;
    .video-name {
      :nth-child(2) {
        margin-left: 16px;
        font-size: 12px;
      }
    }
    .tip-right {
      display: flex;
      height: 100%;
      align-items: center;
      .close-icon {
        cursor: pointer;
      }
    }
  }
  .map-wrapper {
    margin: 10px 0;
    box-sizing: border-box;
    flex: 1;
    display: flex;
    .wrapper-left {
      box-sizing: border-box;
      flex: 1;
      background-color: #000000;
      margin-left: 10px;
      object-fit: fill;
      .video-js {
        width: 100%;
        height: 100%;
        padding: 0;
        color: var(--el-color-primary);
        ::v-ddep(.vjs-big-play-button) {
          border-radius: 50%;
          border: 6px solid var(--el-color-primary);
          left: calc(50% - 1em);
          top: calc(50% - 1em);
          width: 2.5em;
          height: 2.5em;
          line-height: 2.5em;
          background: transparent;
          box-sizing: content-box;
          .vjs-icon-placeholder:before {
            font-size: 48px;
          }
          &:hover {
            opacity: 0.6;
          }
        }
        ::v-ddep(.vjs-volume-level, .vjs-play-progress, .vjs-slider-bar) {
          background-color: var(--el-color-primary) !important;
        }
        :deep(.vjs-control-bar) {
          font-size: 14px;
        }
      }
    }

    .wrapper-right {
      width: 280px;
      height: 100%;
      margin: 0 10px;
      display: flex;
      flex-direction: column;
      .title {
        box-sizing: border-box;
        padding: 0 15px;
        height: 40px;
        line-height: 40px;
        font-size: 18px;
        font-weight: bold;
        background-color: #000000;
      }
      .right-main {
        flex: 1;
        margin-top: 5px;
        box-sizing: border-box;
        background-color: #000000;
        padding: 8px 0;
        li {
          padding: 8px 15px;
          box-sizing: border-box;
          display: flex;
          cursor: pointer;
          span {
            font-size: 12px;
          }
          :nth-child(1) {
            flex: 1;
            .ellipseLine();
            &:hover {
              color: var(--el-color-primary);
            }
          }
          :nth-child(2) {
            margin-left: 15px;
            color: #909399;
          }
        }
        li.active {
          color: var(--el-color-primary);
        }
      }
    }
  }
}
:global(.video-js .vjs-volume-level, ) {
  background-color: var(--el-color-primary) !important;
}
:global(.video-js .vjs-play-progress) {
  background-color: var(--el-color-primary) !important;
}
:global(.video-js .vjs-slider-bar) {
  background-color: var(--el-color-primary) !important;
}
</style>
