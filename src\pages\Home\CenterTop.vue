<template>
  <ul class="CenterTop">
    <li v-for="(p, i) in list" :key="i">
      <SvgIcon class="img" :name="p.code" />
      <div class="title">
        <p>{{ p.desc }}</p>
        <p>
          <span>{{ p.data }}</span>
          <span>{{ p.unit }}</span>
        </p>
      </div>
    </li>
  </ul>
</template>

<script setup lang="ts">
import { todayFileApi } from '@/api/home'
import { ElMessage } from 'element-plus'
import { ref } from 'vue'

const list = ref<any[]>([])

const initData = async () => {
  try {
    const { data, message, status } = await todayFileApi()
    if ([200].includes(status)) {
      list.value = data
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}

initData()
</script>

<style scoped lang="less">
.CenterTop {
  height: 3.75vw;
  width: 100%;
  box-sizing: border-box;
  display: flex;
  padding: 0 1.5625vw;
  li {
    flex: 1;
    box-sizing: border-box;
    display: flex;
    .img {
      width: 3.75vw;
      height: 3.75vw;
    }
    .title {
      margin-left: 0.7813vw;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      text-align: center;
      :nth-child(1) {
        font-weight: 500;
        color: #656565;
        font-size: 0.8333vw;
      }
      :nth-child(2) {
        :nth-child(1) {
          font-weight: 500;
          color: #333333;
          font-size: 1.875vw;
        }
        :nth-child(2) {
          font-size: 0.625vw;
          color: #656565;
          margin-left: 0.2604vw;
        }
      }
    }
  }
}
</style>
