import { defineStore } from 'pinia'
import socket from '@/utils/webSocket'

interface State {
  socketData: any
}

const useSocket = defineStore({
  id: 'socket',
  state: (): State => {
    return {
      socketData: undefined,
    }
  },
  actions: {
    setSocketData(data: State['socketData']) {
      this.socketData = data
    },
    handleCreateSocket(params?: any) {
      socket.initWebSocket(
        {
          onMessage: (data: any) => {
            if (data && Object.keys(JSON.parse(data)).length) {
              this.setSocketData(JSON.parse(data))
              params.callback && params.callback(JSON.parse(data))
            }
          },
        },
        false,
        params?.url,
      )
    },
    handleCloseSocket() {
      socket.close()
    },
  },
})

export default useSocket
