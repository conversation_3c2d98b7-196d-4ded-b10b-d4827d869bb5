<template>
  <ElDialog
    v-model="dialogFormVisible"
    top="10vh"
    title="修改密码"
    width="400px"
    :close-on-click-modal="false"
    @close="handleDialogClose"
  >
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="auto">
      <el-form-item label="输入密码" prop="password">
        <ElInput type="password" autocomplete="off" v-model="formData.password" />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="setDialogFormVisible(false)">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </span>
    </template>
  </ElDialog>
</template>
<script setup lang="ts">
import { addBatchApi } from '@/api/data_security'
import useDialogForm from '@/hooks/useDialogForm'
import { ElDialog, ElInput, ElMessage } from 'element-plus'
import { ref } from 'vue'

const { dialogFormVisible, setDialogFormVisible, formRef, handleDialogClose, validateForm } =
  useDialogForm()
const selectData = ref<any[]>([])
const handleSubmit = async () => {
  const userFileIds = selectData.value.map((item: any) => item.tid)
  if (!userFileIds.length) return ElMessage.error('请选择要修改密码的文件！')
  try {
    await validateForm()
    const { message, status } = await addBatchApi({ userFileIds, ...formData.value })
    if ([200].includes(status)) {
      ElMessage.success(message)
      setDialogFormVisible(false)
      emit('handleRefresh')
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}

const handleOpen = (list: any) => {
  selectData.value = list
  setDialogFormVisible(true)
}

const formData = ref<any>({})
const rules = ref({
  password: [{ required: true, message: '加密密码必填!', trigger: 'blur' }],
})

defineExpose({ handleOpen })

const emit = defineEmits<{
  (e: 'handleRefresh'): void
}>()
</script>
<style lang="less" scoped></style>
