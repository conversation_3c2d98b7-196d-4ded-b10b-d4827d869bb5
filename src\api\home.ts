import request from '@/utils/request'

// 今日文件统计
export const todayFileApi = () => request.get(`/stat/todayFile`)

// 服务发布概览
export const serverPublishApi = () => request.get(`/stat/serverPublish`)

// 文件来源概览
export const fileSourceApi = () => request.get(`/stat/fileSource`)

// 数据百分比概览
export const dataPercentApi = () => request.get(`/stat/dataPercent`)

// 今日数据及分类概览
export const dataWithTypeApi = () => request.get(`/stat/dataWithType`)

// 湖仓数据大小统计
export const queryDataLakeWarehouseApi = () => request.get(`/homeBean/queryDataLakeWarehouse`)

// 数据目录数据排行统计
export const queryCatalogDataApi = () => request.get(`/homeBean/queryCatalogData`)

// 适配器数据流排行统计
export const queryAdapterDataApi = () => request.get(`/homeBean/queryAdapterData`)

// 任务统计
export const queryHomeTaskApi = () => request.get(`/homeBean/queryTask`)
