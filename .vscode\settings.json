{"editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "eslint.enable": true, "eslint.format.enable": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[css]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[svg]": {"editor.defaultFormatter": "jock.svg"}, "[typescript]": {"editor.defaultFormatter": "dbaeumer.vscode-eslint"}, "[vue]": {"editor.defaultFormatter": "dbaeumer.vscode-eslint"}}