<template>
  <div class="tabLayout">
    <ul class="tab" v-if="menuList.length && routeLength.length !== 4">
      <li v-for="p in menuList" :key="p.name">
        <RouterLink :to="{ name: p.name }">{{ p.meta?.title }}</RouterLink>
      </li>
    </ul>
    <div class="main">
      <RouterView />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { RouterLink, RouterView, useRoute } from 'vue-router'

const route = useRoute()

const routeLength = computed<any[]>(() => route.path.split('/').filter((item: any) => !!item))

const menuList = computed<any[]>(() => {
  let list: any[] = []
  if (route.matched && route.matched.length === 4) {
    list = route.matched[route.matched.length - 2].children.filter((item: any) => {
      const arr: any[] = item.path.split('/').filter((p: any) => !!p)
      return arr.length === 3
    })
  }
  return list
})
</script>

<style scoped lang="less">
.tabLayout {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  display: flex;
  overflow: hidden;
  flex-direction: column;
  .tab {
    width: 100%;
    height: 40px;
    box-shadow: 0px 0px 7px 1px rgba(0, 0, 0, 0.05);
    box-sizing: border-box;
    background-color: @withe;
    display: flex;
    padding: 0 20px;
    margin-bottom: 15px;
    li {
      a {
        font-size: 14px;
        color: #333333;
        line-height: 40px;
        position: relative;
        display: inline-block;
      }
      .router-link-active {
        color: var(--el-color-primary);
        &::after {
          content: '';
          width: 40px;
          height: 2px;
          background-color: var(--el-color-primary);
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
        }
      }
    }
    li + li {
      margin-left: 30px;
    }
  }
  .main {
    flex: 1;
    box-sizing: border-box;
    overflow: hidden;
  }
}
</style>
