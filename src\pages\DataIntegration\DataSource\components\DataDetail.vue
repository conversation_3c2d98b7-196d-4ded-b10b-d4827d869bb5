<template>
  <div class="DataDetail">
    <div class="header">
      <span>{{ rowData.datasourceName }}</span>
    </div>
    <div class="main">
      <el-form ref="formRef" class="form-item" :model="rowData" label-width="120px">
        <el-form-item label="数据源类型" prop="taskName">
          <el-input disabled v-model="rowData.adapterName" placeholder="请输入域名" />
        </el-form-item>
        <el-form-item label="域名" prop="hosts">
          <el-input disabled v-model="rowData.hosts" placeholder="请输入访问秘钥" />
        </el-form-item>
        <el-form-item label="桶名" prop="dbName">
          <el-input disabled v-model="rowData.dbName" placeholder="请输入安全秘钥" />
        </el-form-item>
        <el-form-item label="描述" prop="notes">
          <el-input
            disabled
            :rows="6"
            type="textarea"
            v-model="rowData.notes"
            placeholder="请输入桶名"
          />
        </el-form-item>
        <el-form-item prop="notes">
          <el-button type="primary" style="width: 100%" @click="handleSee">查看数据</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()
const handleSee = () => {
  router.push({ query: { ...route.query, list: 'true' } })
}

defineProps<{ rowData: any }>()
</script>

<style scoped lang="less">
.DataDetail {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  .header {
    height: 60px;
    box-sizing: border-box;
    border-bottom: 1px solid #eeeeee;
    line-height: 60px;
    padding-left: 30px;
    span {
      font-size: 16px;
      font-weight: 500;
      color: #333333;
    }
    :nth-child(2) {
      color: var(--el-color-primary);
    }
    span + span {
      margin-left: 20px;
    }
  }
  .main {
    flex: 1;
    overflow-y: auto;
    .no-scrollbar(0);
    box-sizing: border-box;
    padding: 30px 15px;

    .form-item {
      width: 440px;
    }
  }
}
</style>
