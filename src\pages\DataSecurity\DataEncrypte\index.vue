<template>
  <MainLayout>
    <template #header>
      <SearchTool
        @handle-search="handleSearch"
        @handle-create="AddDialogRef.handleOpen()"
        @handle-update="handleUpdatePass(selectData)"
        @handle-del="handleDel(selectData)"
        :is-btn="true"
      />
    </template>
    <el-table
      v-loading="pageData.loading"
      header-cell-class-name="common-table-header"
      cell-class-name="common-table-cell"
      ref="multipleTableRef"
      :data="tableData"
      height="100%"
      style="width: 100%"
      row-key="tid"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" reserve-selection width="30" />
      <el-table-column property="fileName" label="文件名称" show-overflow-tooltip>
        <template #default="scope">
          <span class="fileNmae">{{ getFileNameComplete(scope.row) }}</span>
        </template>
      </el-table-column>
      <el-table-column property="suffix" label="数据格式" :width="80" show-overflow-tooltip />
      <el-table-column property="filePath" label="资源路径" :width="100" show-overflow-tooltip />
      <el-table-column property="encryptTime" label="加密时间" :width="180" show-overflow-tooltip />
      <el-table-column
        v-if="
          permission.hasButton(['dataEncrypte_edit', 'dataEncrypte_details', 'dataEncrypte_del'])
        "
        label="操作"
        width="130"
        fixed="right"
      >
        <template #default="scope">
          <el-button
            v-if="permission.hasButton('dataEncrypte_edit')"
            @click="handleUpdatePass([scope.row])"
            title="修改密码"
            class="common-icon-btn"
            type="primary"
            plain
            link
          >
            <template #icon>
              <SvgIcon name="edit" />
            </template>
          </el-button>
          <el-button
            v-if="permission.hasButton('dataEncrypte_details')"
            @click="DetailsDialogRef.handleOpen(scope.row)"
            title="查看密码"
            class="common-icon-btn"
            type="primary"
            plain
            link
          >
            <template #icon>
              <SvgIcon name="details" />
            </template>
          </el-button>
          <el-button
            v-if="permission.hasButton('dataEncrypte_del')"
            @click="handleDel([scope.row])"
            title="数据脱敏"
            class="common-icon-btn"
            type="danger"
            plain
            link
          >
            <template #icon>
              <SvgIcon name="Unlock" />
            </template>
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <template #footer>
      <el-pagination
        v-model:currentPage="pageData.page"
        v-model:page-size="pageData.limit"
        :page-sizes="pageData.pageSizes"
        background
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageData.total"
        @size-change="pageData.handleSizeChange"
        @current-change="pageData.handleCurrentChange"
      />
    </template>
    <AddDialog ref="AddDialogRef" @handle-refresh="pageData.handleSearch(null, 1)" />
    <UpdatePassDialog ref="UpdatePassDialogRef" @handle-refresh="handleRefresh" />
    <DetailsDialog ref="DetailsDialogRef" />
  </MainLayout>
</template>

<script setup lang="ts">
import MainLayout from '@/components/MainLayout/index.vue'
import SearchTool from './SearchTool.vue'
import usePageData from '@/hooks/usePageData'
import { userFileListApi } from '@/api/data_catalog'
import { ref } from 'vue'
import { getFileNameComplete } from '@/utils/fileUtils'
import AddDialog from './AddDialog.vue'
import UpdatePassDialog from './UpdatePassDialog.vue'
import DetailsDialog from './DetailsDialog.vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { removeByUserFileIdsApi } from '@/api/data_security'
import useUserInfo from '@/store/useUserInfo'

const permission = useUserInfo()

const AddDialogRef = ref<any>()

const params = { encryptIsNotNull: true, isDir: 0 }
const { tableData, pageData } = usePageData(userFileListApi, false)
pageData.handleSearch(params, 1)

const handleSearch = (data: any) => {
  pageData.handleSearch({ ...params, ...(data || {}) }, 1)
}

const DetailsDialogRef = ref<any>()

const selectData = ref<any[]>([])
const handleSelectionChange = (list: any[]) => {
  selectData.value = list
}

const UpdatePassDialogRef = ref<any>()
const handleUpdatePass = (list: any) => {
  if (!list.length) return ElMessage.warning('请选择需要更新密码的文件!')
  UpdatePassDialogRef.value?.handleOpen(list)
}

const multipleTableRef = ref<any>()
const handleRefresh = () => {
  multipleTableRef.value!.clearSelection()
}

const handleDel = (list: any) => {
  if (!list.length) return ElMessage.warning('请选择需要脱敏的文件!')
  const userFileIds = list.map((item: any) => item.tid)
  ElMessageBox.confirm('是否确定进行数据脱敏？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    try {
      const { message, status } = await removeByUserFileIdsApi(userFileIds)
      if ([200].includes(status)) {
        ElMessage.success(message)
        handleRefresh()
        pageData.handleSearch(null, 1)
      } else {
        ElMessage.error(message)
      }
    } catch (error) {}
  })
}
</script>

<style lang="less" scoped></style>
