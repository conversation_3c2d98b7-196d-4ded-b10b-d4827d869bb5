<template>
  <ElDialog
    v-model="dialogFormVisible"
    top="10vh"
    width="500px"
    title="审核"
    :close-on-click-modal="false"
  >
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="auto">
      <el-form-item label="审核">
        <ElRadioGroup v-model="formData.authStatus">
          <ElRadio :value="1" label="审核通过" />
          <ElRadio :value="2" label="审核拒绝" />
        </ElRadioGroup>
      </el-form-item>
      <el-form-item label="审核建议" prop="authDesc">
        <el-input
          v-model="formData.authDesc"
          :rows="3"
          type="textarea"
          placeholder="请填写审核建议"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="setDialogFormVisible(false)">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </span>
    </template>
  </ElDialog>
</template>
<script setup lang="ts">
import { modifyAuthApplyApi } from '@/api/lake_catalog'
import useDialogForm from '@/hooks/useDialogForm'

import { ElMessage } from 'element-plus'
import { ref } from 'vue'

const { dialogFormVisible, setDialogFormVisible, formRef, validateForm } = useDialogForm()
const formData = ref<any>({})

const rules = ref<any>({
  authDesc: [{ required: true, message: '审核建议必填！', trigger: 'blur' }],
})

const handleSubmit = async () => {
  try {
    await validateForm()
    const { status, message } = await modifyAuthApplyApi({
      ...formData.value,
    })
    if ([200].includes(status)) {
      ElMessage.success(message)
      setDialogFormVisible(false)
      emit('handleRefresh')
    } else {
      ElMessage.error(message)
    }
  } catch (error) {
    console.error(error)
  }
}

const handleOpen = (tidList: string[]) => {
  formData.value = { tidList }
  setDialogFormVisible(true)
}

defineExpose({ handleOpen })

const emit = defineEmits<{
  (e: 'handleRefresh'): void
}>()
</script>
