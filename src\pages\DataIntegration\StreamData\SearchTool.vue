<template>
  <el-form ref="ruleFormRef" :model="ruleForm">
    <div class="search-content">
      <div class="search-input">
        <div class="search-item">
          <el-form-item prop="serverName">
            <el-input
              class="form-item"
              v-model="ruleForm.serverName"
              placeholder="请输入服务名称"
              @keydown.enter="handleSearch"
            >
              <template #suffix>
                <SvgIcon @click="handleSearch" style="cursor: pointer" name="search" />
              </template>
            </el-input>
          </el-form-item>
        </div>
        <div class="search-item">
          <el-form-item prop="isRurning">
            <el-select
              class="form-item"
              v-model="ruleForm.isRurning"
              placeholder="请选择状态"
              clearable
              @change="handleSearch"
            >
              <el-option
                v-for="p in dictData.statusType"
                :key="p.value"
                :label="p.key"
                :value="p.value"
              />
            </el-select>
          </el-form-item>
        </div>
        <div class="search-item">
          <el-form-item prop="dateRange">
            <el-date-picker
              @change="handleSearch"
              style="width: 100%"
              value-format="YYYY-MM-DD HH:mm:ss"
              v-model="ruleForm.dateRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="上传开始日期"
              end-placeholder="上传结束日期"
              clearable
            />
          </el-form-item>
        </div>
      </div>
      <div v-if="permission.hasButton('streamData_add')" class="search-btn">
        <el-button type="primary" @click="emit('handleCreate')">
          <template #icon>
            <SvgIcon name="add" />
          </template>
          新增
        </el-button>
      </div>
    </div>
  </el-form>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import type { FormInstance } from 'element-plus'
import useUserInfo from '@/store/useUserInfo'

const permission = useUserInfo()

const { dictData } = defineProps<{ dictData: any }>()

const ruleFormRef = ref<FormInstance>()
const ruleForm = ref<any>({})

const handleSearch = () => {
  const { dateRange, serverName, isRurning } = ruleForm.value
  emit('handleSearch', {
    serverName,
    isRurning,
    startTime: dateRange && dateRange.length ? dateRange[0] : undefined,
    endTime: dateRange && dateRange.length ? dateRange[1] : undefined,
  })
}

const emit = defineEmits<{
  (e: 'handleSearch', form: any): void
  (e: 'handleCreate'): void
}>()
</script>
