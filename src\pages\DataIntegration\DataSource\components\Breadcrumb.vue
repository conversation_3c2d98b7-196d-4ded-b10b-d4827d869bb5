<template>
  <div class="breadcrumb">
    <el-breadcrumb separator="/">
      <el-breadcrumb-item
        v-for="(item, index) in breadCrumbList"
        :key="index"
        :to="getRouteQuery(item)"
      >
        {{ item.name }}
      </el-breadcrumb-item>
    </el-breadcrumb>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'

const route: any = useRoute()

const breadCrumbList = computed<any[]>(() => {
  if (['sourceDataDetail', 'sourceEsDBDetail'].includes(route.name)) {
    return [
      {
        path: 'dataSource',
        query: {},
        name: '数据源',
      },
      {
        path: ['sourceDataDetail'].includes(route.name) ? 'sourceDataDetail' : 'sourceEsDBDetail',
        query: null,
        name: '数据源数据详情',
      },
    ]
  }
  const filePathList: any[] = route.query.filePath ? route.query.filePath.split('/') : []
  const resList: any[] = [] //  返回结果数组
  const pathList: any[] = [] //  存放祖先路径
  filePathList.forEach((item: any) => {
    if (item) {
      pathList.push(item)
      resList.push({
        path: 'sourceFileDetail',
        query: { filePath: pathList.join('/') + '/' },
        name: item,
      })
    }
  })
  let tabs: any[] = []
  if (resList.length > 4) {
    tabs = [...resList.splice(-3)]
    tabs.unshift({
      path: 'sourceFileDetail',
      query: null,
      name: '...',
    })
    tabs.unshift(resList[0])
  } else {
    tabs = [...resList]
  }
  const menuList: any[] = [
    {
      path: 'dataSource',
      query: {},
      name: '数据源',
    },
    {
      path: 'sourceFileDetail',
      query: tabs.length ? { filePath: undefined } : null,
      name: '数据源数据详情',
    },
  ]

  return [...menuList, ...tabs]
})

const getRouteQuery = (item: any) => {
  if (!item.query) {
    return
  }
  return {
    name: item.path,
    query: ['dataSource'].includes(item.path) ? undefined : { ...route.query, ...item.query },
  }
}
</script>

<style scoped lang="less">
.breadcrumb {
  height: 40px;
  padding-left: 20px;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  box-shadow: 0px 8px 8px 0px rgba(0, 0, 0, 0.05);
  background-color: @withe;
}
</style>
