<template>
  <div class="taskManageDetail">
    <LeftMenu @update-task-id="taskModel.updateTaskId" />
    <div class="main">
      <div class="header">
        <div class="title">
          <div class="back" @click="router.back">
            <SvgIcon name="arrowLeft" />
            <span>返回</span>
          </div>
          <span>{{ route.query.taskName }}{{ taskModel.taskData.createTimeStr }}</span>
          <div class="icon">
            <SvgIcon
              @click="taskModel.menuActive = 0"
              :class="['item', [0].includes(taskModel.menuActive) ? 'active' : '']"
              name="onlineModelArchitecture"
            />

            <SvgIcon
              @click="taskModel.menuActive = 1"
              :class="['item', [1].includes(taskModel.menuActive) ? 'active' : '']"
              name="onlineModelList"
            />
          </div>
        </div>
        <div class="text">
          <p>
            开始：<span>{{ taskModel.taskData.startTime }}</span>
          </p>
          <p>
            结束：<span>{{
              [1].includes(taskModel.taskData.runStatus)
                ? taskModel.taskData.endTime
                : Status[taskModel.taskData.runStatus]
            }}</span>
          </p>
          <p>
            时长：<span>{{
              taskModel.taskData.runTimes && taskModel.taskData.runTimes >= 1000
                ? transferSecondsToTime(taskModel.taskData.runTimes || 0, 'milliseconds')
                : `${taskModel.taskData.runTimes || 0}ms`
            }}</span>
          </p>
        </div>
      </div>
      <div class="content">
        <div v-show="[0].includes(taskModel.menuActive)" class="graph">
          <div class="graphContent">
            <RelationGraph
              :options="options"
              :on-canvas-click="graphOperate.handleCanvasClick"
              :on-node-click="graphOperate.handleNodeClick"
              ref="RelationGraphRef"
            >
              <template #node="{ node }">
                <div class="node">
                  <div class="nodeIcon">
                    <img :src="(node as any).data.icon" alt="" />
                  </div>
                  <span
                    @click.stop.self="graphOperate.handleReport(node)"
                    :class="getNodeStyle(node)"
                  >
                    {{ (node as any).text }}
                  </span>
                </div>
              </template>
            </RelationGraph>
            <ModelLog :logList="logData.logList" />
          </div>
          <RightMenu is-running :selectNode="graphOperate.selectNode" />
        </div>
        <el-table
          v-show="[1].includes(taskModel.menuActive)"
          stripe
          header-cell-class-name="common-table-header"
          cell-class-name="common-table-cell"
          ref="multipleTableRef"
          :data="tableData"
          height="100%"
          style="width: 100%"
          row-key="tid"
        >
          <el-table-column type="index" width="50" />
          <el-table-column property="text" label="节点名称" show-overflow-tooltip />
          <el-table-column property="nodeTypeName" label="类型" show-overflow-tooltip>
            <template #default="scope">
              {{ scope.row.data.nodeTypeName || '--' }}
            </template>
          </el-table-column>
          <el-table-column property="startTimeValue" label="开始时间" show-overflow-tooltip>
            <template #default="scope">
              {{ scope.row.data.startTimeValue || '--' }}
            </template>
          </el-table-column>
          <el-table-column property="runTimesValue" label="执行时长" show-overflow-tooltip>
            <template #default="scope">
              {{ scope.row.data.runTimesValue || '--' }}
            </template>
          </el-table-column>
          <el-table-column property="nodeStatus" label="执行状态" show-overflow-tooltip>
            <template #default="scope">
              {{ Status[scope.row.data.nodeStatus] || '--' }}
            </template>
          </el-table-column>
          <el-table-column property="nodeStatus" label="操作" show-overflow-tooltip>
            <template #default="scope">
              <el-button
                v-if="isOutputNode(scope.row)"
                @click="handleOutputData(scope.row, taskModel.taskData?.tid)"
                type="primary"
                plain
                link
              >
                查看
              </el-button>
              <span v-else>--</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <QualityReportDialog ref="QualityReportDialogRef" />
      <MapBoxPreview ref="MapBoxPreviewRef" :is-image-cut-show="false" />
      <CesiumPreview ref="CesiumPreviewRef" />
      <ServerAddress ref="ServerAddressRef" />
    </div>
  </div>
</template>

<script setup lang="ts">
import LeftMenu from './LeftMenu.vue'
import RightMenu from '@/pages/OnlineModel/components/RightMenu.vue'
import RelationGraph, { RGNode, RGUserEvent } from 'relation-graph-vue3'
import ModelLog from '@/pages/OnlineModel/components/ModelLog.vue'
import { onUnmounted, reactive, computed } from 'vue'
import useRelationGraph from '@/hooks/useRelationGraph.ts'
import { useRouter, useRoute } from 'vue-router'
import { taskModelDetailApi } from '@/api/online_model'
import { ElMessage } from 'element-plus'
import { transferSecondsToTime, getFilterLineData, getFilterNodeData } from '@/utils'
import useOutputData from '@/pages/OnlineModel/hooks/useOutputData'
// 运行状态
enum Status {
  '失败',
  '成功',
  '等待',
  '执行',
  '暂停',
}

const {
  QualityReportDialog,
  QualityReportDialogRef,
  MapBoxPreview,
  MapBoxPreviewRef,
  CesiumPreview,
  CesiumPreviewRef,
  handleOutputData,
  getNodeStyle,
  isOutputNode,
  ServerAddress,
  ServerAddressRef,
} = useOutputData()

const {
  setCheckedNode,
  options,
  RelationGraphRef,
  setJsonData,
  clearChecked,
  taskModelStatus,
  logData,
} = useRelationGraph()
const router = useRouter()
const route = useRoute()
const tableData = computed(() => RelationGraphRef.value?.getInstance().graphData.nodes)

interface TaskModel {
  taskData: any
  menuActive: number
  updateTaskId: (id: string) => Promise<void>
  filterModelParams: (params: any) => any
}
const taskModel = reactive<TaskModel>({
  taskData: {},
  menuActive: 0,
  updateTaskId: async (id: string) => {
    if (taskModel.taskData?.tid === id) return
    try {
      taskModelStatus.stopTask()
      logData.clearLogData()
      const { data, message, status } = await taskModelDetailApi(id)
      if ([200].includes(status)) {
        taskModel.taskData = data
        const json = taskModel.filterModelParams(data)
        await setJsonData(json)
        clearChecked()
        if (![0, 1, 4].includes(data.runStatus)) {
          logData.setTaskModelId(data.tid)
          await logData.getLogData()
          taskModelStatus.initStatus(data.tid)
        } else {
          await logData.getAllLogData(data.tid)
        }
      } else {
        ElMessage.error(message)
      }
    } catch (error) {}
  },
  filterModelParams: (params: any) => {
    const { taskModelNodeLineVOList, taskModelNodeVOList, tid } = params
    const nodes = getFilterNodeData(taskModelNodeVOList)
    const lines = getFilterLineData(taskModelNodeLineVOList)
    return { rootId: tid, nodes, lines }
  },
})

// 关系图相关操作
interface GraphOperate {
  selectNode: RGNode | any
  handleNodeClick: (node: RGNode, _e?: RGUserEvent) => boolean
  handleCanvasClick: () => void
  handleReport: (node: any) => void
}

const graphOperate = reactive<GraphOperate>({
  selectNode: {},
  handleNodeClick: (node: RGNode, _e?: RGUserEvent | any) => {
    const className: string = _e.target.className
    if (className.indexOf('nodeText') !== -1) return false
    graphOperate.selectNode = node
    setCheckedNode(node.id)
    return true
  },
  handleCanvasClick: () => {
    graphOperate.selectNode = null
    clearChecked()
  },
  // 点击报告
  handleReport: async (node: any) => {
    const nodeIsReport: boolean = await handleOutputData(node, taskModel.taskData?.tid)
    console.log(nodeIsReport, 'ssss')
    if (nodeIsReport) return
    graphOperate.selectNode = node
    setCheckedNode(node.id)
  },
})

onUnmounted(() => {
  taskModelStatus.stopTask()
  logData.clearLogData()
})
</script>

<style lang="less" scoped>
.taskManageDetail {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  display: flex;
  overflow: hidden;
  .main {
    flex: 1;
    box-sizing: border-box;
    overflow: hidden;
    margin-left: 20px;
    background-color: @withe;
    box-shadow: 0 8px 8px 8px rgba(0, 0, 0, 0.05);
    display: flex;
    flex-direction: column;
    padding: 20px;
    .header {
      margin-bottom: 20px;
      .title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        box-sizing: border-box;
        .back {
          display: flex;
          align-items: center;
          cursor: pointer;
          :nth-child(1) {
            font-size: 28px;
          }
          span {
            font-size: 14px;
            color: #333333;
          }
        }
        > span {
          font-size: 18px;
          color: #333333;
        }
        .icon {
          font-size: 20px;
          color: #a7b7cb;
          .item {
            cursor: pointer;
          }
          .item + .item {
            margin-left: 10px;
          }
          .active {
            color: var(--el-color-primary);
          }
        }
      }
      .text {
        margin-top: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        p {
          font-size: 14px;
          color: #333333;
          span {
            color: #666666;
          }
        }
        p + p {
          margin-left: 20px;
        }
      }
    }
    .content {
      flex: 1;
      box-sizing: border-box;
      overflow: hidden;
      .graph {
        width: 100%;
        height: 100%;
        box-sizing: border-box;
        display: flex;
        .graphContent {
          flex: 1;
          box-sizing: border-box;
          overflow: hidden;
          display: flex;
          flex-direction: column;
        }
      }

      .node {
        font-size: 12px;
        cursor: pointer;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        .nodeIcon {
          width: 36px;
          height: 36px;
          pointer-events: none;
          img {
            width: 100%;
            height: 100%;
          }
        }
        .nodeText {
          text-align: center;
          position: absolute;
          bottom: -30px;
          width: 80px;
          .ellipseLine();
        }
        .nodeTextActive {
          color: var(--el-color-primary);
          text-decoration: underline;
        }
      }
    }
    .page {
      margin-top: 15px;
      display: flex;
      justify-content: flex-end;
    }
  }
}
</style>
