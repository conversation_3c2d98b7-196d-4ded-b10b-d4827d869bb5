<template>
  <ElImageViewer
    v-if="showViewer"
    :z-index="1000"
    infinite
    :url-list="urlList"
    :hide-on-click-modal="false"
    :teleported="false"
    :initial-index="initialIndex"
    :close-on-press-escape="true"
    @close="handleCloseViewer"
  />
</template>

<script setup lang="ts">
import { ElImageViewer } from 'element-plus'
import { ref } from 'vue'

const showViewer = ref<boolean>(false)
const urlList = ref<any[]>([])
const initialIndex = ref<number>(0)

const handleOpenPreview = (urls: any[], index: number = 0) => {
  initialIndex.value = index
  urlList.value = urls
  showViewer.value = true
}

const handleCloseViewer = () => {
  showViewer.value = false
}

defineExpose({ handleOpenPreview })
</script>
