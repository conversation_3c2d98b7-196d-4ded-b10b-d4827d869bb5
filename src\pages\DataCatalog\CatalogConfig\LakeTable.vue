<template>
  <MainLayout>
    <template #header>
      <div class="btn">
        <ElButton @click="AddEditDialogRef.handleOpen(null, 0)" type="primary"
          >新建一级目录</ElButton
        >
      </div>
    </template>
    <el-table
      v-loading="loading"
      header-cell-class-name="common-table-header"
      cell-class-name="common-table-cell"
      ref="multipleTableRef"
      :data="tableData"
      height="100%"
      style="width: 100%"
      row-key="tid"
      :tree-props="treeProps"
    >
      <el-table-column property="catalogAlias" label="目录名称" show-overflow-tooltip />
      <el-table-column property="level" label="目录级别" show-overflow-tooltip />
      <el-table-column property="level" label="目录级别">
        <template #default="scope">{{ scope.row.bsType === 1 ? '菜单' : '数据库' }}</template>
      </el-table-column>
      <el-table-column property="catalogDesc" label="描述" show-overflow-tooltip />
      <el-table-column property="createTime" label="创建时间" show-overflow-tooltip />
      <el-table-column label="操作" width="130" fixed="right">
        <template #default="scope">
          <el-button
            @click="AddEditDialogRef.handleOpen(scope.row, 2)"
            title="新建子目录"
            class="common-icon-btn"
            type="primary"
            plain
            link
          >
            <template #icon>
              <SvgIcon name="CirclePlus" />
            </template>
          </el-button>
          <el-button
            @click="AddEditDialogRef.handleOpen(scope.row, 1)"
            title="编辑"
            class="common-icon-btn"
            type="primary"
            plain
            link
          >
            <template #icon>
              <SvgIcon name="edit" />
            </template>
          </el-button>
          <el-button
            @click="handleDel(scope.row.tid)"
            title="删除"
            class="common-icon-btn"
            type="danger"
            plain
            link
          >
            <template #icon>
              <SvgIcon name="delete" />
            </template>
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <AddEditDialog
      :tree-data="tableData"
      ref="AddEditDialogRef"
      :dataType="2"
      @handle-refresh="getTableData"
    />
  </MainLayout>
</template>

<script lang="ts" setup>
import MainLayout from '@/components/MainLayout/index.vue'
import usePageData from './usePageData'
import AddEditDialog from './AddEditDialog.vue'
import { ref } from 'vue'

const { treeProps, tableData, loading, getTableData, handleDel } = usePageData(2)
const AddEditDialogRef = ref<any>()
</script>

<style lang="less" scoped>
.btn {
  text-align: right;
}
</style>
