<template>
  <div class="CatalogConfig">
    <div class="breadcrumb">
      <ElBreadcrumb separator="/">
        <ElBreadcrumbItem @click="router.go(-2)" class="link">数据目录</ElBreadcrumbItem>
        <ElBreadcrumbItem @click="router.go(-1)" class="link">目录配置</ElBreadcrumbItem>
        <ElBreadcrumbItem>版本管理</ElBreadcrumbItem>
      </ElBreadcrumb>
    </div>
    <div class="btn">
      <ElButton @click="handleContrast" type="primary">对比分析</ElButton>
    </div>
    <div class="tableData">
      <el-table
        header-cell-class-name="common-table-header"
        cell-class-name="common-table-cell"
        ref="multipleTableRef"
        :data="tableData"
        height="100%"
        style="width: 100%"
        row-key="tid"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" reserve-selection width="30" />
        <el-table-column :width="80">
          <template #default="scope">
            <ElTag effect="dark" :type="scope.row.useStatus ? 'primary' : 'danger'">{{
              scope.row.useStatus ? '使用中' : '停用'
            }}</ElTag>
          </template>
        </el-table-column>
        <el-table-column property="version" label="版本号" show-overflow-tooltip>
          <template #default="scope"> V{{ scope.row.version }} </template>
        </el-table-column>
        <el-table-column property="tid" label="ID" show-overflow-tooltip />
        <el-table-column property="useStatus" label="状态">
          <template #default="scope">
            {{ scope.row.useStatus ? '活跃' : '归档' }}
          </template>
        </el-table-column>
        <el-table-column property="authDesc" label="版本描述" show-overflow-tooltip />
        <el-table-column property="applyUname" label="创建人" show-overflow-tooltip />
        <el-table-column property="releaseTime" label="创建时间" show-overflow-tooltip />
        <el-table-column
          v-if="permission.hasButton(['catalogVersion_details', 'catalogVersion_use'])"
          label="操作"
          width="130"
          fixed="right"
        >
          <template #default="scope">
            <el-button
              v-if="permission.hasButton('catalogVersion_details')"
              @click="VersionInfoDialogRef?.handleOpen(scope.row)"
              title="详情"
              class="common-icon-btn"
              type="primary"
              plain
              link
            >
              <template #icon>
                <SvgIcon name="details" />
              </template>
            </el-button>
            <el-button
              v-if="!scope.row.useStatus && permission.hasButton('catalogVersion_use')"
              title="使用"
              class="common-icon-btn"
              type="primary"
              plain
              link
              @click="handleClick(scope.row)"
            >
              <template #icon>
                <SvgIcon name="UseStatus" />
              </template>
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="page">
      <el-pagination
        v-model:currentPage="pageData.page"
        v-model:page-size="pageData.limit"
        :page-sizes="pageData.pageSizes"
        background
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageData.total"
        @size-change="pageData.handleSizeChange"
        @current-change="pageData.handleCurrentChange"
      />
    </div>
    <VersionInfoDialog ref="VersionInfoDialogRef" />
    <ContrastDialog ref="ContrastDialogRef" />
  </div>
</template>
<script setup lang="ts">
import { dataCatalogApplyPageApi, dataCatalogModifyUseApi } from '@/api/lake_catalog'
import usePageData from '@/hooks/usePageData'
import {
  ElBreadcrumb,
  ElBreadcrumbItem,
  ElButton,
  ElMessage,
  ElMessageBox,
  ElTag,
} from 'element-plus'
import { useRouter } from 'vue-router'
import VersionInfoDialog from './VersionInfoDialog.vue'
import { ref } from 'vue'
import useUserInfo from '@/store/useUserInfo'
import ContrastDialog from './ContrastDialog.vue'

const permission = useUserInfo()

const selectData = ref<any[]>([])
const handleSelectionChange = (list: any[]) => {
  selectData.value = list.map((item) => item.tid)
}

const ContrastDialogRef = ref<InstanceType<typeof ContrastDialog>>()
const handleContrast = () => {
  if (!selectData.value.length) return ElMessage.warning('请选择需要对比的版本！')
  if (selectData.value.length !== 2) return ElMessage.warning('只能对比2个版本')
  ContrastDialogRef.value?.handleOpen(selectData.value)
}

const VersionInfoDialogRef = ref<InstanceType<typeof VersionInfoDialog>>()

const router = useRouter()

const { tableData, pageData } = usePageData(dataCatalogApplyPageApi, false)

pageData.handleSearch({ release: 1 }, 1)

const handleClick = ({ tid }: any) => {
  ElMessageBox.confirm('确认要使用该版本作为数据目录嘛？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    try {
      const { status, message } = await dataCatalogModifyUseApi(tid)
      if ([200].includes(status)) {
        ElMessage.success(message)
        pageData.handleSearch(null, 1)
      } else {
        ElMessage.error(message)
      }
    } catch (error) {
      console.error(error)
    }
  })
}
</script>
<style lang="less" scoped>
.CatalogConfig {
  height: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 0 20px 20px 20px;
  .breadcrumb {
    height: 40px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    line-height: 40px;
    .link {
      :deep(.el-breadcrumb__inner) {
        color: #303133;
        font-weight: bold;
        cursor: pointer;
        &:hover {
          color: var(--el-color-primary);
        }
      }
    }
  }
  .btn {
    background-color: @withe;
    padding: 15px 0 0 15px;
  }
  .tableData {
    background-color: @withe;
    padding: 15px;
    flex: 1;
    overflow: auto;
    box-sizing: border-box;
  }
  .page {
    background-color: @withe;
    display: flex;
    justify-content: flex-end;
    padding: 0px 15px 15px 0;
  }
}
</style>
