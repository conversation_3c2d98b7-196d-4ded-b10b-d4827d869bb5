<template>
  <ElScrollbar style="width: 375px; margin-right: 40px">
    <div class="left-title">场景名称：{{ formData.name }}</div>
    <div class="btns">
      <ElButton @click="emit('handleSelectAudio')" class="btn" type="primary" plain>
        选择音频
      </ElButton>
      <ElButton @click="emit('handleGenerateVideo')" class="btn" type="primary">
        {{ isGenerateVideo ? '结束' : '开始' }}生成
      </ElButton>
    </div>
    <el-alert
      v-if="audioData"
      @close="emit('handleClearAudio')"
      :title="audioData.fileName"
      type="info"
    />
    <div class="time">{{ formatTimeStr }}</div>
    <p class="text">视频生成时，请勿离开此页....</p>
    <div class="btns">
      <ElButton @click="cancelGenerate" class="btn" type="danger" plain> 取消 </ElButton>
      <ElButton @click="emit('handleVideoPreview')" class="btn" type="primary" plain>
        视频预览
      </ElButton>
      <ElButton @click="emit('handleSave')" class="btn" type="primary"> 保存 </ElButton>
    </div>
  </ElScrollbar>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()

// 取消生成
const cancelGenerate = () => {
  router.back()
}

defineProps<{
  isGenerateVideo: boolean
  formData: any
  formatTimeStr: string
  audioData: any
}>()

const emit = defineEmits<{
  handleGenerateVideo: []
  handleSave: []
  handleVideoPreview: []
  handleSelectAudio: []
  handleClearAudio: []
}>()
</script>

<style lang="less" scoped>
.text {
  text-align: center;
  font-size: 12px;
  font-weight: 400;
  color: #999999;
}
.time {
  height: 46px;
  background: #f7f7f7;
  border-radius: 3px 3px 3px 3px;
  border: 1px solid #dcdfe6;
  text-align: center;
  line-height: 46px;
  margin: 20px 0;
  font-size: 24px;
  font-weight: 500;
  color: #333333;
}
.left-title {
  font-size: 16px;
  font-weight: 400;
  color: #333333;
  .ellipseLine();
}
.btns {
  margin-top: 20px;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  .btn {
    flex: 1;
  }
}
</style>
