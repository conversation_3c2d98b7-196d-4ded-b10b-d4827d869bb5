import NProgress from 'nprogress'
import useGlobalData from '@/store/useGlobalData'
import useUserInfo from '@/store/useUserInfo'
import Layout from '@/Layout/index.vue'

export default function handleRouterPermission(router: any) {
  router.beforeEach(async (to: any) => {
    NProgress.start()
    const permission = useUserInfo()
    // 获取token
    await permission.getToken()
    // 获取用户信息
    if (!permission.userInfo) {
      const menuList: any = await permission.getUserInfo()
      console.log(menuList, 'sssss')
      router.addRoute({
        path: '/',
        name: 'layout',
        component: Layout,
        redirect: menuList[0].path,
        children: menuList
      })
      return to.fullPath
    }
    // 获取字典值
    const globalData = useGlobalData()
    if (!globalData.dicTypeData && !['dataRecovery'].includes(to.name)) {
      await globalData.getDicTypeData()
    }
    if (!globalData.catalogMenuList.length && !['dataRecovery'].includes(to.name)) {
      await globalData.getCatalogMenuList()
    }
    return true
  })
  router.afterEach(() => {
    NProgress.done()
  })
}
