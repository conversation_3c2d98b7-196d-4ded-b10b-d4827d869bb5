<template>
  <div class="GenerateVideo">
    <LeftCreate
      :isGenerateVideo="generateVideoObj.isGenerateVideo"
      :formData="formData"
      :formatTimeStr="formatTimeStr"
      :audioData="audioObj.audioData"
      @handle-clear-audio="audioObj.handleClearAudio"
      @handle-save="generateVideoObj.handleSave"
      @handle-generate-video="generateVideoObj.handleGenerateVideo"
      @handle-video-preview="handleSceneVideoPreview"
      @handle-select-audio="audioObj.handleSelectAudio"
    />
    <div class="mapbox-view" ref="mapRef">
      <MapboxTimeLine
        v-show="sceneData.rowList.length"
        @hanlde-is-paly="generateVideoObj.handleRecordVideo"
        :palyList="sceneData.rowList"
        :currentIndex="sceneData.currentIndex"
        :isPlay="sceneData.isPlay"
      />
    </div>
    <audio
      ref="audioRef"
      loop
      :src="audioObj.audioData?.url"
      controls
      style="display: none"
    ></audio>
    <VideoPreview ref="VideoPreviewRef" />
    <AudioDialog ref="AudioDialogRef" @get-audio-data="audioObj.getAudioData" />
  </div>
</template>

<script setup lang="ts">
import AudioDialog from './AudioDialog.vue'
import MapboxTimeLine from '@/components/MapboxTimeLine/index.vue'
import LeftCreate from './LeftCreate.vue'
import useMapbox from '@/hooks/useMapbox'
import useScenePreview from '@/hooks/useScenePreview'
import { querySceneDetailApi, uploadSceneVideoApi } from '@/api/sequential_scene'
import { computed, onMounted, onUnmounted, reactive, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElLoading, ElMessage } from 'element-plus'
import RecordRTC from 'recordrtc'
import screenfull from 'screenfull'
import SparkMD5 from 'spark-md5'
import { useVideoPreview } from '@/hooks/useFilePreview'

const route = useRoute()
const router = useRouter()

const { mapRef, setPreviewLayers, initMap } = useMapbox()
const { sceneData, hanldeIsPaly, handleScenePreview, clearData } = useScenePreview(setPreviewLayers)

// 选择音频弹框
const AudioDialogRef = ref<any>()
const audioRef = ref<any>()
const audioObj = reactive<any>({
  audioData: null,
  isPlay: false,
  handleAudioPause: () => {
    if (!audioObj.audioData) {
      return
    }
    audioObj.isPlay = false
    audioRef.value.pause()
  },
  handleAudioPlay: () => {
    if (!audioObj.audioData) {
      return
    }
    audioObj.isPlay = true
    audioRef.value.play()
  },
  handleClearAudio: () => {
    audioObj.audioData = null
  },
  getAudioData: (row: any) => {
    audioObj.audioData = row
  },
  handleSelectAudio: () => {
    if (AudioDialogRef.value) {
      AudioDialogRef.value.handleOpen()
    }
  },
})

// 视频预览
const { VideoPreview, VideoPreviewRef, handleVideoPreview } = useVideoPreview()
const handleSceneVideoPreview = () => {
  if (!generateVideoObj.videoBlob) {
    ElMessage.error('请先生成视频！')
    return
  }
  const url = URL.createObjectURL(generateVideoObj.videoBlob)
  const videoObj: any = {
    id: 1,
    fileName: formData.value.name,
    isDir: 0,
    suffix: 'mp4',
    fileSize: generateVideoObj.videoBlob.size,
    url,
  }
  handleVideoPreview([videoObj], 0)
}

// 详情数据
const formData = ref<any>({})
// 获取场景详情
const getDataDetail = async () => {
  try {
    const { data, status, message } = await querySceneDetailApi({ tid: route.query.tid })
    if ([200].includes(status)) {
      formData.value = data
      formData.value.fileList = formData.value.fileList.map((item: any) => ({
        fileName: item.fileName,
        sceneTimeFormat: item.sceneTimeFormat,
        tid: item.userFileId,
        gisPreviewVO: item.detail.gisPreviewVO,
        metadataVO: item.detail.metadataVO,
      }))
      console.log(formData.value)
    } else {
      ElMessage.error(message || '获取场景信息失败!')
    }
  } catch (error) {}
}
if (route.query.tid) {
  getDataDetail()
}

// 录制时间
const recorderObj = reactive<any>({
  hours: 0,
  minutes: 0,
  seconds: 0,
  intervalId: null,
  isRunning: '',
  startTimer() {
    if (!recorderObj.isRunning) {
      recorderObj.intervalId = setInterval(() => {
        recorderObj.seconds++
        if (recorderObj.seconds >= 60) {
          recorderObj.seconds = 0
          recorderObj.minutes++
          if (recorderObj.minutes >= 60) {
            recorderObj.minutes = 0
            recorderObj.hours++
          }
        }
      }, 1000)
      recorderObj.isRunning = true
    }
  },
  stopTimer() {
    if (recorderObj.isRunning) {
      clearInterval(recorderObj.intervalId)
      recorderObj.isRunning = false
    }
  },
  resetTimer() {
    recorderObj.hours = 0
    recorderObj.minutes = 0
    recorderObj.seconds = 0
    recorderObj.stopTimer()
  },
  formatTime(number: number) {
    return number.toString().padStart(2, '0')
  },
})
const formatTimeStr = computed<string>(
  () =>
    `${recorderObj.formatTime(recorderObj.hours)}:${recorderObj.formatTime(
      recorderObj.minutes,
    )}:${recorderObj.formatTime(recorderObj.seconds)}`,
)

// 视频生成相关操作
const generateVideoObj = reactive<any>({
  // 是否生成视频
  isGenerateVideo: false,
  // 视频录制对象
  recorder: null,
  // 视频流
  videoBlob: null,
  // 获取MD5值
  getFileMd5: (blob: any, fileName: any) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onloadend = () => {
        const arrayBuffer: any = reader.result
        // 生成MD5值
        const spark = new SparkMD5.ArrayBuffer()
        spark.append(arrayBuffer)
        const md5Hash = spark.end()
        console.log(new File([arrayBuffer], `${fileName}.mp4`, { type: blob.type }), '文件流')
        resolve({
          identifier: md5Hash,
          file: new File([arrayBuffer], `${fileName}.mp4`, { type: blob.type }),
        })
      }
      reader.onerror = () => {
        reject(new Error('无法读取 Blob 对象'))
      }
      reader.readAsArrayBuffer(blob)
    })
  },
  // 获取保存视频参数
  getParams: async () => {
    const fileData = new FormData()
    const { identifier, file }: any = await generateVideoObj.getFileMd5(
      generateVideoObj.videoBlob,
      formData.value.name,
    )
    const params: any = {
      tid: formData.value.tid,
      identifier,
      file,
      fileName: `${formData.value.name}.mp4`,
      totalSize: generateVideoObj.videoBlob.size,
      currentChunkSize: generateVideoObj.videoBlob.size,
      totalChunks: 1,
      chunkSize: generateVideoObj.videoBlob.size,
      chunkNumber: 1,
    }
    Object.keys(params).forEach((key) => {
      fileData.append(key, params[key])
    })
    return fileData
  },
  // 保存视频
  handleSave: async () => {
    if (!generateVideoObj.videoBlob) {
      ElMessage.error('请先生成视频！')
      return
    }
    const loading = ElLoading.service({
      lock: true,
      text: '正在保存,请勿离开...',
      spinner: 'el-icon-loading',
      background: 'rgba(0, 0, 0, 0.7)',
    })
    try {
      const params = await generateVideoObj.getParams()
      const { data, message, status } = await uploadSceneVideoApi(params)
      if ([200].includes(status) && [1].includes(data.uploadCode)) {
        ElMessage.success('保存成功！')
        router.replace({ name: 'sceneList' })
      } else {
        ElMessage.error(message || '保存失败！')
      }
      loading.close()
    } catch (error) {
      loading.close()
    }
  },
  // 设置是否全屏
  setFullScreen: () => {
    if (screenfull.isEnabled) {
      screenfull.request(mapRef.value)
    }
  },
  // 录制视频
  handleRecordVideo: () => {
    generateVideoObj.isGenerateVideo = !generateVideoObj.isGenerateVideo
    if (generateVideoObj.isGenerateVideo) {
      generateVideoObj.setFullScreen()
      setTimeout(() => {
        audioObj.handleAudioPlay()
        hanldeIsPaly()
        recorderObj.startTimer()
        generateVideoObj.recorder.startRecording()
      }, 100)
    } else {
      generateVideoObj.stopGenerateVideo()
    }
  },
  // 获取视频
  handleGenerateVideo: async () => {
    recorderObj.resetTimer()
    generateVideoObj.startGenerateVideo()
    handleScenePreview(formData.value?.fileList, formData.value?.interval)
  },
  // 开始生成视频
  startGenerateVideo: async () => {
    const stream = await navigator.mediaDevices.getDisplayMedia({
      video: true,
      audio: true,
    })
    generateVideoObj.recorder = new RecordRTC(stream, {
      type: 'video',
    })
  },
  // 停止生成视频
  stopGenerateVideo: () => {
    generateVideoObj.recorder?.stopRecording(() => {
      const blob = generateVideoObj.recorder.getBlob()
      generateVideoObj.videoBlob = blob
      ElMessage.success('视频生成成功!')
      audioObj.handleAudioPause()
      screenfull.exit()
      recorderObj.stopTimer()
      clearData()
      generateVideoObj.recorder?.destroy()
      generateVideoObj.recorder = null
    })
  },
})

// 监听是否全屏时间
const handleFullscreenChange = () => {
  if (!screenfull.isFullscreen && generateVideoObj.isGenerateVideo) {
    generateVideoObj.isGenerateVideo = false
    if (generateVideoObj.recorder) {
      generateVideoObj.stopGenerateVideo()
    }
    console.log('object')
  }
}
onMounted(() => {
  initMap()
  document.addEventListener('fullscreenchange', handleFullscreenChange)
})
onUnmounted(() => {
  document.removeEventListener('fullscreenchange', handleFullscreenChange)
})
</script>

<style lang="less" scoped>
.GenerateVideo {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  background-color: @withe;
  overflow: hidden;
  box-shadow: 0px 8px 8px 8px rgba(0, 0, 0, 0.05);
  padding: 20px;
  display: flex;
  .mapbox-view {
    flex: 1;
    box-sizing: border-box;
    overflow: hidden;
    position: relative;
    :global(.mapboxgl-ctrl-bottom-left) {
      display: none;
    }
  }
}
</style>
