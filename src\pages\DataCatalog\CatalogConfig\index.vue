<template>
  <div class="CatalogConfig">
    <div class="breadcrumb">
      <ElBreadcrumb separator="/">
        <ElBreadcrumbItem @click="router.back" class="link">数据目录</ElBreadcrumbItem>
        <ElBreadcrumbItem>目录配置</ElBreadcrumbItem>
      </ElBreadcrumb>
    </div>

    <ul class="tabList">
      <li
        v-for="(item, index) in tabList"
        @click="handleClick(item.id)"
        :key="index"
        :class="{ active: item.id === active }"
      >
        {{ item.name }}
      </li>
    </ul>

    <div class="tableData">
      <HouseTable v-if="active === '1'" />
      <LakeTable v-else />
    </div>
  </div>
</template>
<script setup lang="ts">
import { ElBreadcrumb, ElBreadcrumbItem } from 'element-plus'
import { ref } from 'vue'
import HouseTable from './HouseTable.vue'
import LakeTable from './components/LakeTable.vue'
import { useRouter } from 'vue-router'

const router = useRouter()

const tabList = ref<any[]>([
  { name: '数据湖', id: '2' },
  { name: '数据仓', id: '1' },
])
const active = ref<string>('2')
const handleClick = (value: string) => {
  active.value = value
}
</script>
<style lang="less" scoped>
.CatalogConfig {
  height: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  margin: 0 20px;
  .breadcrumb {
    height: 40px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    line-height: 40px;
    .link {
      :deep(.el-breadcrumb__inner) {
        color: #303133;
        font-weight: bold;
        cursor: pointer;
        &:hover {
          color: var(--el-color-primary);
        }
      }
    }
  }
  .tabList {
    height: 50px;
    box-shadow: 0px 0px 7px 1px rgba(0, 0, 0, 0.05);
    background-color: @withe;
    display: flex;
    align-items: center;
    padding: 0 30px;
    li {
      line-height: 50px;
      padding: 0 5px;
      cursor: pointer;
      font-size: 14px;
      color: #333333;
    }
    li + li {
      margin-left: 40px;
    }
    .active {
      color: var(--el-color-primary);
      border-bottom: 1px solid var(--el-color-primary);
    }
  }
  .tableData {
    margin: 20px 0;
    flex: 1;
    overflow: auto;
    box-sizing: border-box;
  }
}
</style>
