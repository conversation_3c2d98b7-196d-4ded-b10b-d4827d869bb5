<template>
  <div class="sceneList">
    <div class="header">
      <SearchTool @handle-search="handleSearch" @handle-create="handleCreateEdit" />
    </div>
    <div class="main">
      <ElScrollbar>
        <ul class="cardList">
          <li class="cardItem" v-for="p in tableData" :key="p.tid">
            <div class="img" :style="{ 'background-image': `url(${p.thumbnailUrl})` }">
              <div class="type">{{ p.fileTypeName }}</div>
              <div class="see" @click="handlePreview(p)">
                <SvgIcon name="view" />
              </div>
            </div>
            <div class="title">
              <p>{{ p.name }}</p>
              <span>{{ p.updateTime }}</span>
            </div>
            <div class="icons">
              <div>
                <el-button
                  v-if="permission.hasButton('sceneList_edit')"
                  @click="handleCreateEdit(p.tid)"
                  class="common-icon-btn"
                  type="primary"
                  plain
                  link
                  title="编辑"
                >
                  <template #icon>
                    <SvgIcon name="edit" />
                  </template>
                </el-button>
                <template v-if="p.videoId">
                  <el-button
                    v-if="permission.hasButton('sceneList_play')"
                    @click="handleVideo(p)"
                    class="common-icon-btn video"
                    type="primary"
                    plain
                    link
                    title="视频播放"
                  >
                    <template #icon>
                      <SvgIcon name="sequentialSceneVideo" />
                    </template>
                  </el-button>
                  <el-button
                    v-if="permission.hasButton('sceneList_download')"
                    @click="handleDownload(p)"
                    class="common-icon-btn"
                    type="primary"
                    plain
                    link
                    title="下载"
                  >
                    <template #icon>
                      <SvgIcon name="download" />
                    </template>
                  </el-button>
                </template>
                <el-button
                  v-if="permission.hasButton('sceneList_del')"
                  @click="handleDel(p.tid)"
                  class="common-icon-btn"
                  type="danger"
                  plain
                  link
                  title="删除"
                >
                  <template #icon>
                    <SvgIcon name="delete" />
                  </template>
                </el-button>
              </div>
              <div
                v-if="permission.hasButton('sceneList_generateVideo')"
                @click="handleCreateVideo(p.tid)"
                class="createVideo"
              >
                编辑视频
              </div>
            </div>
          </li>
        </ul>
      </ElScrollbar>
    </div>
    <div class="footer">
      <el-pagination
        v-model:currentPage="pageData.page"
        v-model:page-size="pageData.limit"
        :page-sizes="pageData.pageSizes"
        background
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageData.total"
        @size-change="pageData.handleSizeChange"
        @current-change="pageData.handleCurrentChange"
      />
    </div>
    <MapboxScenePreview ref="MapboxScenePreviewRef" />
    <VideoPreview ref="VideoPreviewRef" />
  </div>
</template>

<script setup lang="ts">
import { ElScrollbar, ElMessage, ElMessageBox } from 'element-plus'
import SearchTool from './SearchTool.vue'
import MapboxScenePreview from '@/components/MapboxScenePreview/index.vue'
import usePageData from '@/hooks/usePageData'
import { sceneListApi, removeSceneApi, querySceneDetailApi } from '@/api/sequential_scene'
import { useRouter } from 'vue-router'
import { ref } from 'vue'
import { useVideoPreview } from '@/hooks/useFilePreview'
import { userFileListDetailApi, linkPreviewApi } from '@/api/data_catalog'
import { downloadFileApi } from '@/api/common'
import { downloadFile } from '@/utils/fileUtils'
import useUserInfo from '@/store/useUserInfo'

const permission = useUserInfo()
const router = useRouter()

const { pageData, tableData } = usePageData(sceneListApi)

const handleSearch = (form: any) => {
  pageData.handleSearch(form, 1)
}

// 下载
const handleDownload = ({ videoId }: any) => {
  downloadFile(`${downloadFileApi()}?tid=${videoId}&token=${permission.token}`)
}

// 视频预览
const { VideoPreview, VideoPreviewRef, handleVideoPreview } = useVideoPreview()
const handleVideo = async (p: any) => {
  try {
    const { data, message, status } = await userFileListDetailApi({ tid: p.videoId })
    if ([200].includes(status)) {
      const videoObj: any = { ...data, url: linkPreviewApi(data.tid) }
      handleVideoPreview([videoObj], 0)
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}

// 创建视频
const handleCreateVideo = async (tid: string) => {
  router.push({ name: 'generateVideo', query: { tid } })
}

// 预览场景
const MapboxScenePreviewRef = ref<any>()
const handlePreview = async ({ tid }: any) => {
  try {
    const { data, status, message } = await querySceneDetailApi({ tid })
    if ([200].includes(status)) {
      MapboxScenePreviewRef.value?.handleOpenPreview(data)
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}

// 创建编辑场景
const handleCreateEdit = (tid?: string) => {
  router.push({ name: 'sceneStructure', query: { tid } })
}

// 删除
const handleDel = (tid: string) => {
  ElMessageBox.confirm('是否确定删除当前数据？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    try {
      const { status, message } = await removeSceneApi({ tid })
      if ([200].includes(status)) {
        ElMessage.success(message)
        pageData.handleSearch(null, 1)
      } else {
        ElMessage.error(message)
      }
    } catch (error) {
      console.error(error)
    }
  })
}
</script>

<style lang="less" scoped>
.sceneList {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  overflow: hidden;
  .header {
    padding: 15px;
    background-color: @withe;
  }
  .main {
    flex: 1;
    overflow: auto;
    margin-top: 15px;
    padding: 15px;
    background-color: @withe;
    .cardList {
      display: grid;
      justify-content: space-evenly;
      grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
      grid-gap: 15px 10px;

      .cardItem {
        // width: 260px;
        padding: 5px;
        box-sizing: border-box;
        background-color: @withe;
        border-radius: 4px 4px 4px 4px;
        box-shadow: 0px 8px 8px 8px rgba(0, 0, 0, 0.05);
        border: 1px solid rgba(0, 0, 0, 0.05);
        .img {
          height: 135px;
          background-size: cover;
          background-position: center;
          background-repeat: no-repeat;
          display: flex;
          justify-content: flex-end;
          position: relative;
          cursor: pointer;
          .type {
            text-align: center;
            padding: 0 5px;
            height: 26px;
            background-color: rgba(0, 0, 0, 0.5);
            font-size: 12px;
            font-weight: 400;
            color: @withe;
            line-height: 26px;
          }
          .see {
            transition: all 0.5s ease;
            position: absolute;
            left: 0;
            top: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            font-size: 32px;
            color: @withe;
            text-align: center;
            line-height: 135px;
            opacity: 0;
          }
          &:hover {
            .see {
              opacity: 1;
            }
          }
        }
        .title {
          margin: 10px;
          border-bottom: 1px solid #dbdbdb;
          padding-bottom: 10px;
          p {
            font-size: 14px;
            font-weight: 400;
            color: #333333;
            line-height: 24px;
          }
          span {
            font-size: 12px;
            font-weight: 400;
            color: #999999;
          }
        }
        .icons {
          margin: 0 10px 0 5px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          .common-icon-btn {
            font-size: 24px !important;
          }
          .common-icon-btn + .common-icon-btn {
            margin-left: 0px !important;
          }
          .video {
            font-size: 18px !important;
          }
          .createVideo {
            width: 66px;
            height: 16px;
            border-radius: 8px 8px 8px 8px;
            border: 1px solid var(--el-color-primary);
            font-size: 12px;
            font-weight: 400;
            color: var(--el-color-primary);
            cursor: pointer;
            text-align: center;
            list-style: 16px;
          }
        }
      }
    }
  }
  .footer {
    background-color: @withe;
    box-sizing: border-box;
    padding: 0 15px 15px 15px;
    display: flex;
    justify-content: flex-end;
  }
}
</style>
