import { ref } from 'vue'
import UploadFile from '@/components/UploadFile/index.vue'
import SelectFileTypeUpload from '@/components/SelectFileTypeUpload/index.vue'

// 文件上传处理
export default function useUpload() {
  // 上传组件
  const UploadFileRef = ref<any>()

  // 选择上传类型组件
  const SelectFileTypeUploadRef = ref<any>()

  // 上传调用
  const handleUpload = (type: number, attrs?: string) => {
    UploadFileRef.value?.handlePrepareUpload(type, attrs)
  }

  const handleSelectUpload = (data: any) => {
    UploadFileRef.value?.handleUpload(data)
  }

  // 打开选择类型弹框
  const handleSelectFileTypeUpload = () => {
    SelectFileTypeUploadRef.value?.handleOpen()
  }

  // 选择文件
  const handleSelectSpaceData = (attrs: string) => {
    handleUpload(3, attrs)
  }

  // 获取上传的文件信息
  const getFileData = (file: any) => {
    SelectFileTypeUploadRef.value?.getFileData(file)
  }

  return {
    UploadFileRef,
    handleUpload,
    handleSelectUpload,
    UploadFile,
    SelectFileTypeUploadRef,
    SelectFileTypeUpload,
    handleSelectFileTypeUpload,
    handleSelectSpaceData,
    getFileData
  }
}
