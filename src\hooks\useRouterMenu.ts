import { getMenuList } from '@/utils'
import { ref, watchEffect } from 'vue'
import { useRoute } from 'vue-router'

const useWatchRoute = () => {
  const active = ref<string[]>([])
  const route = useRoute()
  const getSiderMenu = () => {
    let siderMenu: any[] = []
    const currentRoute = route.matched.length > 1 ? route.matched[1] : undefined
    if (currentRoute) {
      siderMenu = getMenuList(currentRoute.children || [])
    }
    return siderMenu
  }
  const siderMenu: any[] = getSiderMenu()

  watchEffect(() => {
    active.value = route.path.split('/').filter((item: string) => !!item)
  })

  return { siderMenu, active }
}

export default useWatchRoute
