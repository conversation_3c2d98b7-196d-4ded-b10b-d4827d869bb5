<template>
  <div class="breadcrumb">
    <div>当前位置：</div>
    <el-breadcrumb separator="/">
      <el-breadcrumb-item
        v-for="(item, index) in breadCrumbList"
        :key="index"
        :to="getRouteQuery(item)"
      >
        {{ item.name }}
      </el-breadcrumb-item>
    </el-breadcrumb>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
const route = useRoute()

const filePath = computed<any>(() => route.query?.filePath || '/')

const props = defineProps<{ formData: any }>()

const breadCrumbList = computed<any[]>(() => {
  if (props.formData?.fileName) {
    return [{ path: '/', name: '全部' }]
  }
  const filePathList: any[] = filePath.value ? filePath.value.split('/') : []
  const resList: any[] = [] //  返回结果数组
  const pathList: any[] = [] //  存放祖先路径
  filePathList.forEach((item: any, i: number) => {
    if (item) {
      pathList.push(item)
      resList.push({
        path: pathList.join('/'),
        name: item,
      })
    } else if ([0].includes(i)) {
      pathList.push(item)
      resList.push({
        path: '/',
        name: '全部',
      })
    }
  })
  let tabs: any[] = []
  if (resList.length > 4) {
    tabs = [...resList.splice(-3)]
    tabs.unshift({
      path: '',
      name: '...',
    })
    tabs.unshift(resList[0])
  } else {
    tabs = [...resList]
  }
  return tabs
})

const getRouteQuery = (item: any) => {
  if (!item.path) {
    return
  }
  return { query: { filePath: item.path } }
}
</script>

<style scoped lang="less">
.breadcrumb {
  height: 40px;
  padding: 0 15px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  line-height: 40px;
}
</style>
