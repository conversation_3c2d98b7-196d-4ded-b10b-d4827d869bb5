import MapboxDraw from '@mapbox/mapbox-gl-draw'
import { createSupplementaryPointsForCircle } from './common'

const createSupplementaryPoints = MapboxDraw.lib.createSupplementaryPoints

const moveFeatures = MapboxDraw.lib.moveFeatures

const Constants = MapboxDraw.constants

const SimpleSelectModeOverride: any = MapboxDraw.modes.simple_select

SimpleSelectModeOverride.dragMove = function (state: any, e: any) {
  // Dragging when drag move is enabled
  state.dragMoving = true
  e.originalEvent.stopPropagation()

  const delta = {
    lng: e.lngLat.lng - state.dragMoveLocation.lng,
    lat: e.lngLat.lat - state.dragMoveLocation.lat
  }

  moveFeatures(this.getSelected(), delta)

  this.getSelected()
    .filter((feature: any) => feature.properties.isCircle)
    .map((circle: any) => circle.properties.center)
    .forEach((center: any) => {
      center[0] += delta.lng
      center[1] += delta.lat
    })

  state.dragMoveLocation = e.lngLat
}

SimpleSelectModeOverride.toDisplayFeatures = function (_state: any, geojson: any, display: any) {
  geojson.properties.active = this.isSelected(geojson.properties.id)
    ? Constants.activeStates.ACTIVE
    : Constants.activeStates.INACTIVE
  display(geojson)
  this.fireActionable()
  if (
    geojson.properties.active !== Constants.activeStates.ACTIVE ||
    geojson.geometry.type === Constants.geojsonTypes.POINT
  )
    return
  const supplementaryPoints: any = geojson.properties.user_isCircle
    ? createSupplementaryPointsForCircle(geojson)
    : createSupplementaryPoints(geojson)
  supplementaryPoints.forEach(display)
}

export default SimpleSelectModeOverride
