<template>
  <div class="data-import-detail">
    <div class="breadcrumb">
      <ElBreadcrumb separator="/">
        <ElBreadcrumbItem @click="router.go(-1)" class="link">审批管理</ElBreadcrumbItem>
        <ElBreadcrumbItem>详情</ElBreadcrumbItem>
      </ElBreadcrumb>
    </div>
    <div class="form-data">
      <ElScrollbar>
        <ElForm ref="formRef" class="form-main" :model="formData" :rules="rules" label-width="auto">
          <ElCol class="title" :span="24">采集信息</ElCol>
          <GatherFormData v-if="formData" :formData>
            <ElCol class="title" :span="24">审核状态</ElCol>
            <ElCol :span="24">
              <ElFormItem label="申请提交时间" prop="statusName">
                {{ formData.applyTime || '--' }}
              </ElFormItem>
            </ElCol>
            <ElCol :span="24">
              <ElFormItem label="审核状态" prop="statusName">
                {{ formData.statusName || '--' }}
              </ElFormItem>
            </ElCol>
            <template v-if="route.query.isApproval">
              <ElCol class="title" :span="24">审核操作</ElCol>
              <ElCol :span="24">
                <ElFormItem label="审核结果" prop="auditStatus">
                  <ElRadioGroup v-model="formData.auditStatus">
                    <ElRadio :value="1" label="审核通过" />
                    <ElRadio :value="2" label="审核拒绝" />
                  </ElRadioGroup>
                </ElFormItem>
              </ElCol>
              <ElCol :span="24">
                <el-form-item label="审核说明" prop="auditRemark">
                  <el-input
                    :rows="6"
                    v-model="formData.auditRemark"
                    placeholder="请输入审核说明"
                    type="textarea"
                  />
                </el-form-item>
              </ElCol>
            </template>
          </GatherFormData>
        </ElForm>
      </ElScrollbar>
    </div>
    <div class="footer-btn">
      <div style="width: 900px; padding: 0 30px; margin-bottom: 20px">
        <el-button @click="router.back">取消</el-button>
        <el-button @click="handleSubmit" :loading="loading" type="primary">确定</el-button>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { auditDetailApi, updateAuditApi } from '@/api/gather_audit'
import { ElMessage, ElForm } from 'element-plus'
import { ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import GatherFormData from '@/pages/DataIntegration/DataGather/GatherDetail/GatherFormData.vue'

const router = useRouter()
const route = useRoute()

const formRef = ref<any>()
const formData = ref<any>()
const rules = ref<any>({
  auditRemark: [{ required: true, message: '请输入审核说明！', trigger: 'blur' }],
  auditStatus: [{ required: true, message: '请选择取消申请！', trigger: 'blur' }],
})
const getDetailData = async () => {
  try {
    const { data, status, message } = await auditDetailApi({ taskId: route.query.tid })
    if ([200].includes(status)) {
      formData.value = {
        ...JSON.parse(data.taskVO.reqParam),
        status: data.status,
        statusName: data.statusName,
        applyTime: data.applyTime,
      }
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}

getDetailData()

const loading = ref<boolean>(false)
const handleSubmit = async () => {
  if (!route.query.isApproval) {
    router.back()
    return
  }
  await formRef.value?.validate(async (valid: boolean) => {
    if (!valid) return
    try {
      loading.value = true
      const { message, status } = await updateAuditApi({
        taskId: route.query.tid,
        auditRemark: formData.value.auditRemark,
        status: formData.value.auditStatus,
      })
      if ([200].includes(status)) {
        ElMessage.success(message)
        router.back()
      } else {
        ElMessage.error(message)
      }
      loading.value = false
    } catch (error) {
      loading.value = false
    }
  })
}
</script>
<style scoped lang="less">
.data-import-detail {
  height: 100%;
  overflow: hidden;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  .breadcrumb {
    height: 40px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    line-height: 40px;
    .link {
      :deep(.el-breadcrumb__inner) {
        color: #303133;
        font-weight: bold;
        cursor: pointer;
        &:hover {
          color: var(--el-color-primary);
        }
      }
    }
  }
  .form-data {
    flex: 1;
    box-sizing: border-box;
    overflow: hidden;
    background-color: @withe;
    padding: 20px 30px;
    .form-main {
      width: 900px;
      .title {
        font-weight: bold;
        font-size: 14px;
        color: #333333;
        margin-bottom: 20px;
        > span {
          font-size: 12px;
          color: #999999;
          margin-left: 15px;
          font-weight: 400;
        }
      }
    }
  }
  .footer-btn {
    background-color: @withe;
    text-align: right;
  }
}
</style>
