<template>
  <MainLayout>
    <template #header>
      <SearchTool @handle-search="handleSearch" />
    </template>
    <el-table
      v-loading="pageData.loading"
      header-cell-class-name="common-table-header"
      cell-class-name="common-table-cell"
      ref="multipleTableRef"
      :data="tableData"
      height="100%"
      style="width: 100%"
      row-key="tid"
    >
      <el-table-column property="taskName" label="任务名称" show-overflow-tooltip />
      <el-table-column property="dataTypeName" label="入库方式" show-overflow-tooltip />
      <el-table-column property="applyUserName" label="申请人" show-overflow-tooltip />
      <el-table-column property="applyTime" label="申请时间" show-overflow-tooltip />
      <el-table-column property="statusName" label="状态" show-overflow-tooltip />
      <el-table-column property="auditUserName" label="审核人" show-overflow-tooltip>
        <template #default="scope">
          {{ scope.row.auditUserName || '--' }}
        </template>
      </el-table-column>
      <el-table-column property="auditTime" label="审核时间" :width="180" show-overflow-tooltip>
        <template #default="scope">
          {{ scope.row.auditTime || '--' }}
        </template>
      </el-table-column>
      <el-table-column
        v-if="permission.hasButton(['approval_aduit', 'approval_details'])"
        label="操作"
        width="130"
        fixed="right"
      >
        <template #default="scope">
          <el-button
            v-if="permission.hasButton('approval_details')"
            @click="handleOpt(scope, undefined)"
            title="详情"
            class="common-icon-btn"
            type="primary"
            plain
            link
          >
            <template #icon>
              <SvgIcon name="details" />
            </template>
          </el-button>
          <el-button
            v-if="[0].includes(scope.row.status) && permission.hasButton('approval_aduit')"
            @click="handleOpt(scope, true)"
            title="审核"
            class="common-icon-btn"
            type="primary"
            plain
            link
          >
            <template #icon>
              <SvgIcon name="audit" />
            </template>
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <template #footer>
      <el-pagination
        v-model:currentPage="pageData.page"
        v-model:page-size="pageData.limit"
        :page-sizes="pageData.pageSizes"
        background
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageData.total"
        @size-change="pageData.handleSizeChange"
        @current-change="pageData.handleCurrentChange"
      />
    </template>
  </MainLayout>
</template>

<script setup lang="ts">
import MainLayout from '@/components/MainLayout/index.vue'
import SearchTool from './SearchTool.vue'
import usePageData from '@/hooks/usePageData'
import { pageAuditApi } from '@/api/gather_audit'
import { useRouter } from 'vue-router'
import useUserInfo from '@/store/useUserInfo'

const permission = useUserInfo()

const { tableData, pageData } = usePageData(pageAuditApi)

const handleSearch = (data: any) => {
  pageData.handleSearch({ ...data }, 1)
}

const router = useRouter()
const handleOpt = (row: any, isApproval?: any) => {
  router.push({
    name: 'approvalInfo',
    query: {
      tid: row.row.taskId,
      isApproval: isApproval,
    },
  })
}
</script>

<style lang="less" scoped></style>
