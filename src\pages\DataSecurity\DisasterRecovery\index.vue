<template>
  <MainLayout>
    <template #header>
      <SearchTool @handle-search="handleSearch" @handle-create="handleCreate" />
    </template>

    <el-table
      v-loading="pageData.loading"
      header-cell-class-name="common-table-header"
      cell-class-name="common-table-cell"
      ref="multipleTableRef"
      :data="tableData"
      height="100%"
      style="width: 100%"
      row-key="taskId"
    >
      <el-table-column property="taskName" label="任务名称" show-overflow-tooltip />
      <el-table-column property="hosts" label="服务器地址" show-overflow-tooltip />
      <el-table-column property="taskStatusDesc" label="任务状态" show-overflow-tooltip />
      <el-table-column property="startTime" label="任务开始时间" show-overflow-tooltip />
      <el-table-column property="endTime" label="任务结束时间" show-overflow-tooltip />
      <el-table-column property="costTime" label="耗时" show-overflow-tooltip />
      <el-table-column
        v-if="
          permission.hasButton([
            'dataSecurity_backup',
            'dataSecurity_details',
            'dataSecurity_recovery',
            'dataSecurity_del'
          ])
        "
        label="操作"
        width="160"
        fixed="right"
      >
        <template #default="scope">
          <el-button
            v-if="[2].includes(scope.row.taskStatus) && permission.hasButton('dataSecurity_backup')"
            title="开始备份"
            @click="handleBackups(scope.row.taskId)"
            class="common-icon-btn"
            type="primary"
            plain
            link
          >
            <template #icon>
              <SvgIcon name="videoPlay" />
            </template>
          </el-button>
          <el-button
            v-if="permission.hasButton('dataSecurity_details')"
            title="详情"
            @click="CreateDialogRef?.handleOpen(scope.row, false)"
            class="common-icon-btn"
            type="primary"
            plain
            link
          >
            <template #icon>
              <SvgIcon name="details" />
            </template>
          </el-button>
          <el-button
            v-if="
              [1].includes(scope.row.taskStatus) && permission.hasButton('dataSecurity_recovery')
            "
            title="数据恢复"
            @click="handleDataRecovery(scope.row.taskId)"
            class="common-icon-btn"
            type="primary"
            plain
            link
          >
            <template #icon>
              <SvgIcon name="messageBox" />
            </template>
          </el-button>
          <el-button
            v-if="![3].includes(scope.row.taskStatus) && permission.hasButton('dataSecurity_del')"
            title="删除"
            @click="handleDel(scope.row.taskId)"
            class="common-icon-btn"
            type="danger"
            plain
            link
          >
            <template #icon>
              <SvgIcon name="delete" />
            </template>
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <template #footer>
      <el-pagination
        v-model:currentPage="pageData.page"
        v-model:page-size="pageData.limit"
        :page-sizes="pageData.pageSizes"
        background
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageData.total"
        @size-change="pageData.handleSizeChange"
        @current-change="pageData.handleCurrentChange"
      />
    </template>
    <CreateDialog ref="CreateDialogRef" @handle-refresh="pageData.handleSearch(null, 1)" />
  </MainLayout>
</template>

<script setup lang="ts">
import MainLayout from '@/components/MainLayout/index.vue'
import SearchTool from './SearchTool.vue'
import usePageData from '@/hooks/usePageData'
import CreateDialog from './CreateDialog.vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import { ref } from 'vue'
import { listApi, removeByIdApi, startTaskApi, startRecoveryTaskApi } from '@/api/data_security'

import useUserInfo from '@/store/useUserInfo'

const permission = useUserInfo()

const CreateDialogRef = ref<any>(null)
const handleCreate = () => {
  CreateDialogRef.value?.handleOpen(null, true)
}

const { tableData, pageData } = usePageData(listApi)
const handleSearch = (form: any) => {
  pageData.handleSearch(form, 1)
}

// 开始备份
const handleBackups = async (taskId: string) => {
  try {
    const { message, status } = await startTaskApi(taskId)
    if ([200].includes(status)) {
      ElMessage.success(message)
      pageData.handleSearch(null, 1)
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}

// 数据恢复
const handleDataRecovery = (taskId: string) => {
  ElMessageBox.confirm('是否确定恢复该数据？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const { status, message } = await startRecoveryTaskApi(taskId)
      if ([200].includes(status)) {
        ElMessage.success(message)
        pageData.handleSearch(null, 1)
      } else {
        ElMessage.error(message)
      }
    } catch (error) {
      console.error(error)
    }
  })
}

// 删除
const handleDel = (taskId: string) => {
  ElMessageBox.confirm('是否确定删除该任务？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const { status, message } = await removeByIdApi(taskId)
      if ([200].includes(status)) {
        ElMessage.success(message)
        pageData.handleSearch(null, 1)
      } else {
        ElMessage.error(message)
      }
    } catch (error) {
      console.error(error)
    }
  })
}
</script>

<style scoped></style>
