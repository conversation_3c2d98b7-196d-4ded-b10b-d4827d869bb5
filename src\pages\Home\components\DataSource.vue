<template>
  <ul class="data-house">
    <li v-for="(p, i) in list" :key="i">
      <div class="left">
        <SvgIcon class="img" :name="'OTHER'" />
        <span>{{ p.adapterName }}</span>
      </div>
      <div class="right">
        <span>{{ p.size }}</span>
        <span>{{ p.unit }}</span>
      </div>
    </li>
  </ul>
</template>
<script lang="ts" setup>
import { queryAdapterDataApi } from '@/api/home'
import { ElMessage } from 'element-plus'
import { ref } from 'vue'

const list = ref<any[]>([])

const getData = async () => {
  try {
    const { data, status, message } = await queryAdapterDataApi()
    if ([200].includes(status)) {
      list.value = data.adapterList
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}

getData()
</script>
<style scoped lang="less">
.data-house {
  flex: 1;
  box-sizing: border-box;
  padding: 1.5625vw 0;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  li {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .left {
      display: flex;
      align-items: center;
      .img {
        width: 2.2917vw;
        height: 2.2917vw;
      }
      span {
        font-size: 0.7292vw;
        color: #656565;
        margin-left: 0.7813vw;
      }
    }
    .right {
      :nth-child(1) {
        font-size: 1.0417vw;
        color: var(--el-color-primary);
      }
      :nth-child(2) {
        font-size: 0.625vw;
        color: #333333;
        margin-left: 0.2604vw;
      }
    }
  }
}
</style>
