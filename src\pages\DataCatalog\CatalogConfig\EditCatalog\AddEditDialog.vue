<template>
  <ElDialog
    v-model="dialogFormVisible"
    top="10vh"
    width="500px"
    :title="title"
    :close-on-click-modal="false"
  >
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="auto">
      <el-form-item label="目录名称" prop="catalogAlias">
        <el-input v-model="formData.catalogAlias" placeholder="请输入目录名称" />
      </el-form-item>
      <el-form-item label="描述信息" prop="catalogDesc">
        <el-input
          v-model="formData.catalogDesc"
          :rows="3"
          type="textarea"
          placeholder="请填写分类的描述信息"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="setDialogFormVisible(false)">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </span>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
import useDialogForm from '@/hooks/useDialogForm'
import { dayjs, ElDialog, ElForm, ElFormItem, ElInput } from 'element-plus'
import { computed, ref } from 'vue'

const { dialogFormVisible, setDialogFormVisible, formRef, validateForm } = useDialogForm()

const formData = ref<any>({ bsType: 1, catalogAlias: '', catalogDesc: '' })

const optType = ref<number>(0)

const rowData = ref<any>()

function checkSpecialKey(str) {
  let specialKey = "[`~!#$^&*()=|{}':;'\\[\\].<>/?~！#￥……&*（）——|{}【】‘；：”“'。，、？]‘'"
  for (let i = 0; i < str.length; i++) {
    if (specialKey.indexOf(str.substr(i, 1)) != -1) {
      return false
    }
  }
  return true
}

const catalogAliasRules = (rule, value, callback) => {
  if (!checkSpecialKey(value)) {
    callback(new Error('不能含有特殊字符！'))
  } else {
    callback()
  }
}

const rules = ref<any>({
  catalogAlias: [
    { required: true, message: '目录名称必填！', trigger: 'blur' },
    { validator: catalogAliasRules, trigger: 'blur' },
  ],
})

const title = computed<string>(() =>
  [0].includes(optType.value)
    ? '新建一级目录'
    : [1].includes(optType.value)
      ? '修改目录'
      : '添加子目录',
)

// type: 0 新增 1 修改 2添加子节点
const handleOpen = (row: any, type: number) => {
  optType.value = type
  if (optType.value === 0) {
    formData.value.catalogAlias = ''
    formData.value.catalogDesc = ''
    rowData.value = null
  }
  if (type === 1) {
    formData.value.catalogAlias = row.catalogAlias
    formData.value.catalogDesc = row.catalogDesc
    rowData.value = row
  }
  if (type === 2) {
    formData.value.catalogAlias = ''
    formData.value.catalogDesc = ''
    rowData.value = row
  }
  setDialogFormVisible(true)
}

const handleSubmit = async () => {
  try {
    await validateForm()
    if (optType.value === 0) {
      emit('handleAdd', {
        ...formData.value,
        editFlag: 2,
        dataType: 2,
        tid: new Date().getTime().toString(),
        createTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      })
    }
    if (optType.value === 1) {
      if (
        rowData.value.catalogAlias !== formData.value.catalogAlias ||
        rowData.value.catalogDesc !== formData.value.catalogDesc
      ) {
        rowData.value.catalogAlias = formData.value.catalogAlias
        rowData.value.catalogDesc = formData.value.catalogDesc
        rowData.value.editFlag = 3
      }
    }
    if (optType.value === 2) {
      const params = {
        ...formData.value,
        editFlag: 2,
        dataType: 2,
        tid: new Date().getTime().toString(),
        createTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      }
      if (rowData.value.childVOList) {
        rowData.value.childVOList.push(params)
      } else {
        rowData.value.childVOList = [params]
      }
    }
    setDialogFormVisible(false)
  } catch (error) {
    console.error(error)
  }
}

defineExpose({ handleOpen })

const emit = defineEmits<{
  (e: 'handleAdd', form: any): void
}>()
</script>

<style lang="less" scoped></style>
