<template>
  <div class="addGather">
    <div class="form-data">
      <ElScrollbar>
        <ElForm ref="formRef" class="form-main" :model="formData" :rules="rules" label-width="auto">
          <ElRow :gutter="20" style="box-sizing: border-box">
            <ElCol :span="12">
              <ElFormItem label="任务名称" prop="taskDTO.taskName">
                <ElInput v-model="formData.taskDTO.taskName" placeholder="请输入任务名称" />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12"></ElCol>
            <ElCol :span="12">
              <ElFormItem label="入库方式" prop="dataType">
                <ElRadioGroup @change="handleDataType" v-model="formData.dataType">
                  <ElRadio :value="2" label="数据入湖" />
                  <ElRadio :value="1" label="数据入仓" />
                </ElRadioGroup>
              </ElFormItem>
            </ElCol>
            <!-- 数据入湖 -->
            <template v-if="[2].includes(formData.dataType)">
              <ElCol :span="12">
                <ElFormItem label="原始数据源" prop="datasourceId">
                  <ElSelect v-model="formData.datasourceId" placeholder="请选择原始数据源">
                    <ElOption
                      v-for="item in dataSourceList"
                      :key="item.tid"
                      :label="item.datasourceName"
                      :value="item.tid"
                    />
                  </ElSelect>
                </ElFormItem>
              </ElCol>
              <ElCol :span="24">
                <ElFormItem class="fromTable" label="选择文件" prop="dataImportFileDTOList">
                  <div class="tableData">
                    <ObjectFileList
                      v-model="formData.dataImportFileDTOList"
                      fileShow
                      :data-source-id="formData.datasourceId"
                    />
                  </div>
                </ElFormItem>
              </ElCol>
              <ElCol :span="12">
                <ElFormItem label="数据目录" prop="dataCatalogId">
                  <ElTreeSelect
                    v-model="formData.dataCatalogId"
                    :data="dataCataLogSource"
                    check-strictly
                    :props="{
                      label: 'catalogAlias',
                      value: 'tid',
                      children: 'childVOList',
                      disabled: 'disabled',
                    }"
                    value-key="tid"
                    style="width: 100%"
                  />
                </ElFormItem>
              </ElCol>
            </template>
            <!-- 数据入仓 -->
            <template v-if="[1].includes(formData.dataType)">
              <ElCol :span="12"></ElCol>
              <ElCol :span="12">
                <ElFormItem label="原数据库" prop="datasourceId">
                  <ElSelect
                    @change="handleDataBaseChange"
                    v-model="formData.datasourceId"
                    placeholder="请选择原始数据源"
                  >
                    <ElOption
                      v-for="item in dataSourceList"
                      :key="item.tid"
                      :label="item.datasourceName"
                      :value="item.tid"
                    />
                  </ElSelect>
                </ElFormItem>
              </ElCol>
              <ElCol :span="12">
                <ElFormItem label="目标数据库" prop="dataCatalogId">
                  <ElTreeSelect
                    v-model="formData.dataCatalogId"
                    @change="handleTableDataChange"
                    :data="dataCataLogSource"
                    check-strictly
                    :props="{
                      label: 'catalogAlias',
                      value: 'tid',
                      children: 'childVOList',
                      disabled: (_data: any, node: any) => {
                        if (node) {
                          return node?.data?.bsType === 1
                        }
                        return false
                      },
                    }"
                    value-key="tid"
                    style="width: 100%"
                  />
                </ElFormItem>
              </ElCol>
              <ElCol :span="24">
                <ElFormItem class="fromTable" label="入库方式" prop="dataImportFileDTOList">
                  <div class="dataBase">
                    <div class="tips">
                      <ElIcon class="icon"><SvgIcon name="WarningFilled" /></ElIcon>
                      <span>如果目标数据目录中存在表名相同，文件内容不同时，系统默认替换。</span>
                    </div>
                    <div class="dataBaseList">
                      <div class="table">
                        <ElTable
                          ref="TableRef"
                          @selection-change="handleSelectionChange"
                          :data="tableData"
                          header-cell-class-name="common-table-header"
                          cell-class-name="common-table-cell"
                          :height="300"
                          border
                          row-key="fileName"
                        >
                          <el-table-column type="selection" reserve-selection width="50" />
                          <el-table-column
                            property="fileName"
                            label="源数据库表名"
                            show-overflow-tooltip
                          />
                        </ElTable>
                      </div>
                      <div class="btn">
                        <ElIcon><SvgIcon name="DArrowRight" /></ElIcon>
                      </div>
                      <div class="table">
                        <ElTable
                          :data="formData.dataImportFileDTOList"
                          header-cell-class-name="common-table-header"
                          cell-class-name="common-table-cell"
                          :height="300"
                          border
                          row-key="fileName"
                        >
                          <el-table-column property="targetFileName" label="目标数据库表名">
                            <template #default="scope">
                              <ElFormItem
                                :prop="`dataImportFileDTOList.${scope.$index}.targetFileName`"
                                :rules="{ required: true, message: '必填！', trigger: 'blur' }"
                              >
                                <ElInput
                                  v-if="scope.row.edit"
                                  v-model="scope.row.targetFileName"
                                  placeholder="请输入目标数据库表名"
                                />
                                <span class="tableText" v-else>
                                  <ElTooltip
                                    v-if="catalogDataTable.includes(scope.row.targetFileName)"
                                    placement="top"
                                    content="目标数据库中存在相同数据库表，请修改表名"
                                    effect="light"
                                  >
                                    <ElIcon class="icon">
                                      <SvgIcon name="WarningFilled" />
                                    </ElIcon>
                                  </ElTooltip>
                                  {{ scope.row.targetFileName }}
                                </span>
                              </ElFormItem>
                            </template>
                          </el-table-column>
                          <el-table-column width="130" label="操作">
                            <template #default="scope">
                              <ElButton
                                v-if="scope.row.edit"
                                @click="handleEdit(scope.row, false)"
                                size="small"
                                type="primary"
                                link
                                >保存</ElButton
                              >
                              <ElButton
                                v-else
                                @click="handleEdit(scope.row, true)"
                                size="small"
                                type="primary"
                                link
                                >编辑</ElButton
                              >
                              <ElButton
                                @click="
                                  QualityRuleTableRef.handleOpen(scope.row, formData.datasourceId)
                                "
                                size="small"
                                type="primary"
                                link
                                >规则设置</ElButton
                              >
                            </template>
                          </el-table-column>
                        </ElTable>
                      </div>
                    </div>
                  </div>
                </ElFormItem>
              </ElCol>
              <ElCol :span="24">
                <ElFormItem label="健康度评分标准" prop="qaScoreId">
                  <ElSelect v-model="formData.qaScoreId" placeholder="请选择健康度评分标准">
                    <ElOption
                      v-for="p in scoreList"
                      :key="p.tid"
                      :label="p.scoreName"
                      :value="p.tid"
                    />
                  </ElSelect>
                </ElFormItem>
              </ElCol>
            </template>
            <ElCol :span="12">
              <ElFormItem label="任务调度" prop="taskDTO.timerType">
                <ElRadioGroup v-model="formData.taskDTO.timerType">
                  <el-radio :value="1" label="否" />
                  <el-radio :value="2" label="是" />
                </ElRadioGroup>
              </ElFormItem>
            </ElCol>
            <template v-if="[2].includes(formData.taskDTO.timerType)">
              <ElCol :span="24">
                <el-form-item label="执行周期" required>
                  <div class="select-model">
                    <el-form-item style="width: 150px" prop="taskDTO.timerCycle">
                      <el-select v-model="formData.taskDTO.timerCycle" placeholder="请选择">
                        <el-option label="小时" :value="5" />
                        <el-option label="天" :value="4" />
                        <el-option label="周" :value="3" />
                        <el-option label="月" :value="2" />
                      </el-select>
                    </el-form-item>
                    <div class="select-row">
                      <el-form-item
                        v-if="[3].includes(formData.taskDTO.timerCycle)"
                        class="row-item"
                        prop="taskDTO.timerCycleDay"
                      >
                        <el-select v-model="formData.taskDTO.timerCycleDay" placeholder="选择时间">
                          <el-option label="星期一" :value="1" />
                          <el-option label="星期二" :value="2" />
                          <el-option label="星期三" :value="3" />
                          <el-option label="星期四" :value="4" />
                          <el-option label="星期五" :value="5" />
                          <el-option label="星期六" :value="6" />
                          <el-option label="星期日" :value="7" />
                        </el-select>
                      </el-form-item>
                      <el-form-item
                        v-if="[2].includes(formData.taskDTO.timerCycle)"
                        class="row-item"
                        prop="taskDTO.timerCycleDay"
                      >
                        <el-select v-model="formData.taskDTO.timerCycleDay" placeholder="选择时间">
                          <el-option v-for="p in 30" :key="p" :label="p" :value="p" />
                        </el-select>
                      </el-form-item>
                      <el-form-item class="row-item" prop="taskDTO.timerCycleHour">
                        <el-time-select
                          v-show="[5].includes(formData.taskDTO.timerCycle)"
                          v-model="formData.taskDTO.timerCycleHour"
                          start="00:00"
                          step="00:01"
                          end="0:59"
                          placeholder="选择时间"
                        />
                        <el-time-select
                          v-show="![5].includes(formData.taskDTO.timerCycle)"
                          v-model="formData.taskDTO.timerCycleHour"
                          start="00:00"
                          step="00:01"
                          end="23:59"
                          placeholder="选择时间"
                        />
                      </el-form-item>
                    </div>
                  </div>
                </el-form-item>
              </ElCol>
              <ElCol :span="12">
                <ElFormItem label="立即执行" prop="taskDTO.isRun">
                  <ElRadioGroup v-model="formData.taskDTO.isRun">
                    <ElRadio :value="1" label="是" />
                    <ElRadio :value="2" label="否" />
                  </ElRadioGroup>
                </ElFormItem>
              </ElCol>
            </template>
            <ElCol :span="24">
              <el-form-item label="描述信息" prop="note">
                <el-input
                  :rows="6"
                  v-model="formData.note"
                  placeholder="请输入描述信息"
                  type="textarea"
                />
              </el-form-item>
            </ElCol>
            <ElCol :span="24">
              <el-form-item label="申请信息" prop="applyRemark">
                <el-input
                  :rows="6"
                  v-model="formData.applyRemark"
                  placeholder="请输入申请信息"
                  type="textarea"
                />
              </el-form-item>
            </ElCol>
          </ElRow>
        </ElForm>
      </ElScrollbar>
    </div>
    <div class="footer-btn">
      <el-button @click="router.back">取消</el-button>
      <el-button @click="handleSubmit" :loading="loading" type="primary">确定</el-button>
    </div>
    <QualityRuleTable ref="QualityRuleTableRef" />
  </div>
</template>

<script setup lang="ts">
import {
  ElForm,
  ElRow,
  ElScrollbar,
  ElCol,
  ElFormItem,
  ElInput,
  FormInstance,
  ElRadioGroup,
  ElRadio,
  ElSelect,
  ElOption,
  ElButton,
  ElTable,
  ElIcon,
  ElMessage,
  ElTooltip,
} from 'element-plus'
import { reactive, ref } from 'vue'
import QualityRuleTable from './QualityRuleTable.vue'
import { addDataImportTaskApi, queryListByParamApi } from '@/api/data_import'
import { configQueryListApi } from '@/api/catalog_config'
import ObjectFileList from './ObjectFileList.vue'
import { useRouter } from 'vue-router'
import { queryPgListTableApi } from '@/api/data_source'
import { qaScoreListApi } from '@/api/data_governance'

const router = useRouter()

const formRef = ref<FormInstance>()
const formData = ref<any>({ dataType: 2, taskDTO: { timerType: 1 }, dataImportFileDTOList: [] })

const loading = ref<boolean>(false)
const handleSubmit = async () => {
  await formRef.value?.validate(async (valid) => {
    if (!valid) return
    try {
      loading.value = true
      const { message, status } = await addDataImportTaskApi(formData.value)
      if ([200].includes(status)) {
        ElMessage.success(message)
        router.back()
      } else {
        ElMessage.error(message)
      }
      loading.value = false
    } catch (error) {
      loading.value = false
    }
  })
}

const scoreList = ref<any[]>([])
const getScoreList = async (form: any = {}) => {
  try {
    const { data, status, message } = await qaScoreListApi(form)
    if ([200].includes(status)) {
      scoreList.value = data
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}
getScoreList()

const tableData = ref<any[]>([])
const getTableData = async (params: any, callback: any) => {
  try {
    const { data, message, status } = await queryPgListTableApi(params)
    if ([200].includes(status)) {
      callback(data)
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}

const catalogDataTable = ref<any[]>([])
const handleTableDataChange = (dataCatalogId: any) => {
  getTableData({ dataCatalogId }, (data: any[]) => {
    catalogDataTable.value = data
  })
}

const TableRef = ref<any>()
const handleDataBaseChange = (dataSourceId: any) => {
  TableRef.value?.clearSelection()
  getTableData({ dataSourceId }, (data: any[]) => {
    tableData.value = data.map((item: any) => ({
      targetFileName: item,
      fileName: item,
      ruleList: [],
    }))
  })
}

const dataSourceList = ref<any[]>([])
const getDataSourceList = async (dataType: number) => {
  try {
    const { data, message, status } = await queryListByParamApi({ dataType })
    if (status === 200) {
      dataSourceList.value = data
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}
getDataSourceList(formData.value.dataType)

const handleDataType = (dataType: any) => {
  formData.value.datasourceId = ''
  formData.value.dataCatalogId = ''
  getDataSourceList(dataType)
  getDataCataLogSource(dataType)
}

const dataCataLogSource = ref<any[]>([])
const getDataCataLogSource = async (dataType: number) => {
  try {
    const { data, message, status } = await configQueryListApi({ dataType })
    if (status === 200) {
      dataCataLogSource.value = data
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}
getDataCataLogSource(formData.value.dataType)

const QualityRuleTableRef = ref<any>()
const handleSelectionChange = (list: any) => {
  formData.value.dataImportFileDTOList = list
  console.log(list, 'sssss')
}
const handleEdit = (row: any, edit: boolean) => {
  row.edit = edit
}

const rules = reactive<any>({
  'taskDTO.taskName': [
    { required: true, message: '请输入任务名称！', trigger: 'blur' },
    { min: 1, max: 20, message: '1-20字符之间！', trigger: 'blur' },
  ],
  dataType: [{ required: true, message: '入库方式必填！', trigger: 'blur' }],
  datasourceId: [{ required: true, message: '数据源必填！', trigger: 'blur' }],
  dataImportFileDTOList: [{ required: true, message: '请选择文件！', trigger: 'change' }],
  dataCatalogId: [{ required: true, message: '目录必填！', trigger: 'change' }],
  'taskDTO.timerType': [{ required: true, message: '定时任务必填！', trigger: 'change' }],
  'taskDTO.timerCycle': [{ required: true, message: '执行周期必填！', trigger: 'change' }],
  'taskDTO.timerCycleDay': [{ required: true, message: '时间必填必填！', trigger: 'change' }],
  'taskDTO.timerCycleHour': [{ required: true, message: '时间必填必填！', trigger: 'change' }],
  'taskDTO.isRun': [{ required: true, message: '是否立即执行必填！', trigger: 'change' }],
  qaScoreId: [{ required: true, message: '质量评分必填！', trigger: 'change' }],
  applyRemark: [{ required: true, message: '请输入申请信息！', trigger: 'blur' }],
})
</script>

<style lang="less" scoped>
.addGather {
  height: 100%;
  background-color: @withe;
  overflow: hidden;
  box-sizing: border-box;
  padding: 30px;
  display: flex;
  flex-direction: column;
  .form-data {
    flex: 1;
    box-sizing: border-box;
    overflow: hidden;
    .form-main {
      width: 900px;
      .fromTable {
        :deep(.el-form-item__label) {
          margin-top: 38px;
        }
        .tableData {
          width: 100%;
          box-sizing: border-box;
          .btn {
            text-align: right;
          }
          .footer {
            margin-top: 10px;
            display: flex;
            align-items: center;
            > p {
              flex: 1;
              overflow: hidden;
              box-sizing: border-box;
              margin-left: 30px;
              font-size: 12px;
              color: #999999;
              line-height: 18px;
            }
          }
        }
      }
      .select-model {
        width: 100%;
        display: flex;
        align-items: center;
        box-sizing: border-box;
        .select-row {
          margin-left: 15px;
          flex: 1;
          box-sizing: border-box;
          display: flex;
          align-items: center;
          .row-item {
            flex: 1;
            box-sizing: border-box;
          }
          .row-item + .row-item {
            margin-left: 15px;
          }
        }
      }
    }
    .dataBase {
      width: 100%;
      box-sizing: border-box;
      :deep(.el-form-item__label) {
        margin-top: 38px;
      }
      .tips {
        display: flex;
        align-items: center;
        box-sizing: border-box;
        overflow: hidden;
        .icon {
          font-size: 16px;
          color: RGBA(255, 172, 41, 1);
        }
        span {
          flex: 1;
          overflow: hidden;
          box-sizing: border-box;
          margin-left: 10px;
          font-size: 12px;
          color: #999999;
        }
      }
      .dataBaseList {
        display: flex;
        box-sizing: border-box;
        align-items: center;
        .table {
          flex: 1;
          overflow: hidden;
          box-sizing: border-box;
        }
        .btn {
          width: 90px;
          height: 36px;
          background: var(--el-color-primary);
          border-radius: 4px;
          margin: 0 20px;
          font-size: 18px;
          color: @withe;
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }
    }
    .tableText {
      display: flex;
      align-items: center;
      .icon {
        margin-right: 10px;
        color: #ffac29;
        font-size: 16px;
        cursor: pointer;
      }
    }
  }
  .footer-btn {
    width: 900px;
    text-align: right;
  }
}
</style>
