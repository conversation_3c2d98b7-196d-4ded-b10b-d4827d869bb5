<template>
  <ElDialog
    v-model="dialogFormVisible"
    title="对比分析"
    top="10vh"
    width="80%"
    :close-on-click-modal="false"
  >
    <ul class="contrastData">
      <li>
        <ElText size="large" type="primary">V{{ tableData.version }}</ElText>
        <el-table
          header-cell-class-name="common-table-header"
          cell-class-name="common-table-cell"
          ref="multipleTableRef"
          :data="tableData.dataCatalogApplyRelationVOList"
          height="50vh"
          style="width: 100%; margin-top: 15px"
          row-key="tid"
          :tree-props="treeProps"
          default-expand-all
        >
          <el-table-column property="catalogAlias" label="目录名称" show-overflow-tooltip>
            <template #default="scope">
              <ElText
                :type="
                  commonData.some(
                    (item: any) =>
                      item.catalogAlias === scope.row.catalogAlias &&
                      item.level === scope.row.level,
                  )
                    ? 'primary'
                    : 'danger'
                "
                >{{ scope.row.catalogAlias }}</ElText
              >
            </template>
          </el-table-column>
          <el-table-column property="level" label="级别" show-overflow-tooltip width="80px">
            <template #default="scope">
              <ElText
                :type="
                  commonData.some(
                    (item: any) =>
                      item.catalogAlias === scope.row.catalogAlias &&
                      item.level === scope.row.level,
                  )
                    ? 'primary'
                    : 'danger'
                "
                >{{ scope.row.level }}</ElText
              >
            </template>
          </el-table-column>
        </el-table>
      </li>
      <li>
        <ElText size="large" type="primary">V{{ treeData.version }}</ElText>
        <el-table
          header-cell-class-name="common-table-header"
          cell-class-name="common-table-cell"
          ref="multipleTableRef"
          :data="treeData.dataCatalogApplyRelationVOList"
          height="50vh"
          style="width: 100%; margin-top: 15px"
          row-key="tid"
          :tree-props="treeProps"
          default-expand-all
        >
          <el-table-column property="catalogAlias" label="目录名称" show-overflow-tooltip>
            <template #default="scope">
              <ElText
                :type="
                  commonData.some(
                    (item: any) =>
                      item.catalogAlias === scope.row.catalogAlias &&
                      item.level === scope.row.level,
                  )
                    ? 'primary'
                    : 'danger'
                "
                >{{ scope.row.catalogAlias }}</ElText
              >
            </template>
          </el-table-column>
          <el-table-column property="level" label="级别" show-overflow-tooltip width="80px">
            <template #default="scope">
              <ElText
                :type="
                  commonData.some(
                    (item: any) =>
                      item.catalogAlias === scope.row.catalogAlias &&
                      item.level === scope.row.level,
                  )
                    ? 'primary'
                    : 'danger'
                "
                >{{ scope.row.level }}</ElText
              >
            </template>
          </el-table-column>
        </el-table>
      </li>
    </ul>
  </ElDialog>
</template>

<script setup lang="ts">
import { dataCatalogQueryByIdApi } from '@/api/lake_catalog'
import useDialogForm from '@/hooks/useDialogForm'
import { ElDialog, ElMessage, ElText } from 'element-plus'
import { ref } from 'vue'

const treeProps = { children: 'childVOList' }

const { dialogFormVisible, setDialogFormVisible } = useDialogForm()

const tableData = ref<any>({})
const treeData = ref<any>({})
const commonData = ref<any>([])

const getDetails = async (tid: string, callback: any) => {
  try {
    const { data, message, status } = await dataCatalogQueryByIdApi(tid)
    if ([200].includes(status)) {
      callback(data)
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}

const handleOpen = (ids: string[]) => {
  Promise.all([
    getDetails(ids[0], (data: any) => {
      tableData.value = data
    }),
    getDetails(ids[1], (data: any) => {
      treeData.value = data
    }),
  ]).then(() => {
    console.log(tableData.value, treeData.value)
    const list = flattenTree(tableData.value.dataCatalogApplyRelationVOList)
    const arr = flattenTree(treeData.value.dataCatalogApplyRelationVOList)
    commonData.value = list.filter((item: any) =>
      arr.some(
        (item2: any) => item.level === item2.level && item.catalogAlias === item2.catalogAlias,
      ),
    )
    setDialogFormVisible(true)
  })
}

const flattenTree = (tree: any[]): any[] => {
  const result: any[] = []
  // 遍历树形结构的数组
  function traverse(nodes: any[]) {
    nodes.forEach((node: any) => {
      // 将当前节点添加到结果数组
      result.push({
        catalogAlias: node.catalogAlias,
        level: node.level,
      })
      // 如果当前节点有子节点，则继续遍历子节点
      if (node.childVOList && node.childVOList.length > 0) {
        traverse(node.childVOList)
      }
    })
  }
  // 启动递归遍历
  traverse(tree)
  return result
}

defineExpose({ handleOpen })
</script>

<style lang="less" scoped>
.contrastData {
  display: flex;
  box-sizing: border-box;
  > li {
    flex: 1;
    background-color: @withe;
    box-sizing: border-box;
    padding: 10px;
  }
  li + li {
    margin-left: 20px;
  }
}
</style>
