<template>
  <el-form ref="ruleFormRef" :model="ruleForm">
    <div class="search-content">
      <div class="search-input">
        <div class="search-item">
          <el-form-item prop="name">
            <el-input
              class="form-item"
              v-model="ruleForm.name"
              placeholder="请输入场景名称"
              @keydown.enter="emit('handleSearch', ruleForm)"
            >
              <template #suffix>
                <SvgIcon
                  @click="emit('handleSearch', ruleForm)"
                  style="cursor: pointer"
                  name="search"
                />
              </template>
            </el-input>
          </el-form-item>
        </div>
        <div class="search-item">
          <el-form-item prop="fileTypeId">
            <el-select
              class="form-item"
              style="width: 100%"
              v-model="ruleForm.fileTypeId"
              placeholder="请选择数据类型"
              clearable
              @change="emit('handleSearch', ruleForm)"
            >
              <el-option
                v-for="p in fileTypeOptions"
                :key="p.tid"
                :label="p.fileTypeName"
                :value="p.tid"
              />
            </el-select>
          </el-form-item>
        </div>
      </div>
      <div v-if="permission.hasButton('sceneList_add')" class="search-btn">
        <el-button type="primary" @click="emit('handleCreate')">
          <template #icon>
            <SvgIcon name="add" />
          </template>
          创建场景
        </el-button>
      </div>
    </div>
  </el-form>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'
import type { FormInstance } from 'element-plus'
import { queryFileTypeApi } from '@/api/sequential_scene'
import useUserInfo from '@/store/useUserInfo'

const permission = useUserInfo()

// 字典值
const fileTypeOptions = ref<any[]>([])
const getFileType = async () => {
  try {
    const { data, status } = await queryFileTypeApi()
    if ([200].includes(status)) {
      fileTypeOptions.value = data
    }
  } catch (error) {}
}
getFileType()

const ruleFormRef = ref<FormInstance>()
const ruleForm = reactive<any>({})

const emit = defineEmits<{
  (e: 'handleSearch', form: any): void
  (e: 'handleCreate'): void
}>()
</script>
