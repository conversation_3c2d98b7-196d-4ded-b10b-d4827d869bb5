import request, { baseUrl } from '@/utils/request'

// 查询-节点集合（树）
export const queryTreeListApi = () => request.get(`/node/queryTreeList`)

// 查询-节点参数
export const queryNodeParamApi = (tid: string) => request.post(`/node/queryNodeParam/${tid}`)

// 查询-模型集合（分页）
export const queryPageByParamApi = (data: any) => request.post(`/model/queryPageByParam`, data)

// 查询-模型类型集合（树）
export const queryModeltypeTreeListApi = (data: any) =>
  request.post(`/modeltype/queryTreeList`, data)

// 删除-模型
export const removeByIdApi = (tid: string) => request.get(`/model/removeById/${tid}`)

// 新增-模型
export const modelAddApi = (data: any) => request.post(`/model/add`, data)

// 修改-模型
export const modifyByIdApi = (data: any) => request.post(`/model/modifyById`, data)

// 查询-模型
export const queryByIdApi = (tid: string) => request.get(`/model/queryById/${tid}`)

// 查询-任务模型集合（分页）
export const queryTaskApi = (data: any) => request.post(`/task/queryPageByParam`, data)

// 删除-任务模型
export const removeTaskApi = (tid: string) => request.get(`/task/removeById/${tid}`)

// 启用-任务
export const startTaskApi = (tid: string) => request.get(`/task/startTask/${tid}`)

// 停止-任务
export const stopTaskApi = (tid: string) => request.get(`/task/stopTask/${tid}`)

// 任务详情左侧列表接口
export const queryTaskModelApi = (data: any) => request.post(`/taskModel/queryPageByParam`, data)

// 查询-任务模型
export const taskModelDetailApi = (tid: string) => request.get(`/taskModel/queryById/${tid}`)

// 新增-任务模型
export const addTaskApi = (data: any) => request.post(`/taskModel/addTask`, data)

// 修改-任务模型
export const modifyTaskByTaskIdApi = (data: any) => request.post(`/task/modifyById`, data)

// 获取轮询状态
export const queryTaskModelStatusApi = (tid: string) =>
  request.get(`/taskModel/queryTaskModelStatus/${tid}`)

// 查询-任务模型节点日志集合
export const queryListByParamApi = (data: any) =>
  request.post(`/taskModelNodeLog/queryListByParam`, data)

// 获取详细任务质检报告
export const getDetailQaReportApi = (data: any) =>
  request.post(`/taskModelQaReport/getDetailQaReport`, data)

// 获取任务节点文件的发布服务
export const nodeServerListApi = (data: any) => request.post(`/taskModel/nodeServerList`, data)

// 修改模型名称 备注
export const modifyModelByIdApi = (data: any) => request.post(`/model/modifyModelById`, data)

//查询-节点配置
export const queryNodeConfigApi = () => request.get(`/nodeConfig/queryNodeConfig`)

// 修改-节点配置M
export const nodeConfigModifyByIdApi = (data: any) => request.post(`/nodeConfig/modifyById`, data)

// 查询-默认节点配置
export const queryDefaultNodeConfigApi = () => request.get(`/nodeConfig/queryDefaultNodeConfig`)

// 查询-自定义节点集合（分页）
export const queryCustomPageByParamApi = (data: any) => request.post(`/node/queryPageByParam`, data)

// 删除-自定义节点
export const removeCustomByIdApi = (tid: string) => request.get(`/node/removeById/${tid}`)

// 查询-自定义节点类型集合（树）
export const queryCustomTreeTypeListApi = () => request.get(`/node/queryTreeTypeList`)

// 上传自定义算子
export const uploadCustomNodeApi = () => `${baseUrl}/node/uploadCustomNode`

// 上传自定义算子图标转Base64
export const uploadIconToBase64Api = () => `${baseUrl}/platform/uploadIconToBase64`

// 自定义算子新增-节点
export const customAddApi = (data: any) => request.post(`/node/add`, data)

// 自定义算子修改-节点
export const customModifyByIdApi = (data: any) => request.post(`/node/modifyById`, data)

// 自定义算子修改-节点
export const queryByIdCustomByIdApi = (tid: string) => request.get(`/node/queryById/${tid}`)

// 验证-节点线规约关系
export const checkNodeLineProtocolApi = (data: any) =>
  request.post(`/nodeLineProtocol/checkNodeLineProtocol`, data)

// 下载自定义算子模板
export const downloadOperaApi = () => request.downLoad(`/node/downloadOpera`)

// 创建用户列表
export const queryCreateUsersApi = (data: any) => request.post(`/task/queryCreateUsers`, data)
