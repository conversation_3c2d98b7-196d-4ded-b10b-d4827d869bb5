import request, { baseUrl } from '@/utils/request'

// 数据查询
export const queryWfsApi = (data: any) => {
  const params = {
    service: 'WFS',
    version: '1.0.0',
    request: 'GetFeature',
    typename: 'user_file_geom',
    outputFormat: 'application/json',
    srsname: 'EPSG:4326',
    ...data,
  }
  return request.get(`/wfs/`, params)
}

// 查询-行政区划树
export const querySysAdminCodeApi = () => request.get(`/platform/querySysAdminCode`)

// 获取当前视野GeoJson
export const getGridApi = (data: any) => request.post(`/sot/getGrid`, data)

// 查询网格码匹配文件
export const getFileListApi = (data: any) => request.post(`/sot/getFileList`, data)

// 获取当前视野GeoJson(BeiDou)
export const getBdJsonApi = (data: any) => request.post(`/sot/bdJson`, data)

// 查询网格码匹配文件(BeiDou)
export const getBdFileListApi = (data: any) => request.post(`/sot/getBdFileList`, data)

// 文件上传
export const vectorTowktApi = () => `${baseUrl}/spaceHanlde/vectorToWkt`
