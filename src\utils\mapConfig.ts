import allprojs from '@/utils/proj'
import { register } from 'ol/proj/proj4'
import proj4 from 'proj4'
import * as projExtent from 'ol/proj'

const mapboxToken =
  'pk.eyJ1IjoiYXJhcGF5IiwiYSI6ImNsNXEyYXJhMDB2MWkzaWw4dWgxcWptOHYifQ.UYSWGUj_ufnOu-7KACtl4w'

// 获取星图底图token
const getToken = () => {
  const token = `0315f1c8be340fff50ba8d45e611debd649610bf829471772fc9d785739e8c93`
  let reqParam = ''
  const global = window as any
  if (
    global.geovis_sign &&
    (global['geovis_sign'].secretId as any) &&
    (global['geovis_sign'].clientId as any) &&
    (global['geovis_sign'].expireTime as any) &&
    (global['geovis_sign'].sign as any)
  ) {
    reqParam = `&secretId=${global['geovis_sign'].secretId}&clientId=${global['geovis_sign'].clientId}&expireTime=${global['geovis_sign'].expireTime}&sign=${global['geovis_sign'].sign}`
  } else {
    reqParam = '&token=' + token
  }

  return reqParam
}

// 星图地图
const getBaseMap = (type: string = 'img') => {
  let url = `https://tiles1.geovisearth.com/base/v1/${type}/{z}/{x}/{y}?format=webp&tmsIds=w${getToken()}`
  return url
}

// 星图底图标注
const markMap = `https://tiles1.geovisearth.com/base/v1/cia/{z}/{x}/{y}?format=webp&tmsIds=w${getToken()}`

// 中心坐标点
const center: [number, number] = [116.39880632, 38.90487972]

// 默认坐标系
const projection = 'EPSG:3857'

// 投影坐标系转换
const getTransformExtentData = (proj: string, bbox: any[], espg: string = projection) => {
  const projData = allprojs.allprojs.find((item: any) => item.name === proj)
  if (projData) {
    proj4.defs(projData.name, projData.proj4text)
    register(proj4)
  }
  const bbox3857 = projExtent.transformExtent(bbox, proj, espg)
  return bbox3857
}

export { mapboxToken, getBaseMap, markMap, center, projection, getTransformExtentData }
