import request from '@/utils/request'

// 查询-数据源（分页）
export const queryPageByParamApi = (data: any) => request.post(`/datasource/queryPageByParam`, data)

// 修改-数据源
export const modifyByIdApi = (data: any) => request.post(`/datasource/modifyById`, data)

// 测试链接-数据源
export const connectByParamApi = (data: any) => request.post(`/datasource/connectByParam`, data)

// 新增-数据源
export const AddByParamApi = (data: any) => request.post(`/datasource/add`, data)

// 查询-数据源
export const queryByIdApi = (tid: any) => request.get(`/datasource/queryById/${tid}`)

// 删除-数据源
export const removeByIdApi = (tid: any) => request.delete(`/datasource/removeById/${tid}`)

// 查询数据库所有表
export const queryPgListTableApi = (params: any) => request.get(`/rdsManage/queryListTable`, params)

// 查询数据库表字段
export const queryPgTableFieldApi = (params: any) =>
  request.get(`/rdsManage/queryTableField`, params)

// 查询数据表详情列表
export const queryPgDetailListApi = (params: any) =>
  request.get(`/rdsManage/queryTableDetailList`, params)

// 编辑数据表
export const editPgDetailApi = (data: any) => request.post(`/rdsManage/modifyDetail`, data)

// 查询文件列表
export const queryListApi = (data: any) => request.post(`/objectStorage/queryList`, data)

//查询es数据详情列表
export const queryDetailListApi = (data: any) => request.get(`/esDetail/queryDetailList`, data)

//es编辑数据
export const editDetailApi = (data: any) => request.post(`/esDetail/editDetail`, data)

// 查询mongo所有集合
export const queryListCollectionApi = (data: any) =>
  request.get(`/mongoDetail/queryListCollection`, data)

// 查询mongodb数据详情列表
export const queryDbDetailListApi = (data: any) => request.get(`/mongoDetail/queryDetailList`, data)

// db编辑数据
export const mongoEditDetailApi = (data: any) => request.post(`/mongoDetail/editDetail`, data)
