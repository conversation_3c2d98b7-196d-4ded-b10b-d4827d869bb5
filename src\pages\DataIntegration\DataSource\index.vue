<template>
  <MainLayout>
    <template #header>
      <SearchTool
        @handle-search="handleSearch"
        @handle-create="handleAddEditDetail(true, undefined)"
      />
    </template>

    <el-table
      v-loading="pageData.loading"
      header-cell-class-name="common-table-header"
      cell-class-name="common-table-cell"
      ref="multipleTableRef"
      :data="tableData"
      height="100%"
      style="width: 100%"
      row-key="tid"
    >
      <el-table-column property="datasourceName" label="数据源名称" show-overflow-tooltip>
        <template #default="scope">
          <span class="datasource-name" @click="handleDetails(scope.row)">{{
            scope.row.datasourceName
          }}</span>
        </template>
      </el-table-column>
      <el-table-column property="adapterName" label="适配器" show-overflow-tooltip />
      <el-table-column property="statusValue" label="状态" show-overflow-tooltip />
      <el-table-column property="createTime" label="创建时间" show-overflow-tooltip />
      <el-table-column property="notes" label="描述" show-overflow-tooltip />
      <el-table-column
        v-if="permission.hasButton(['dataSource_edit', 'dataSource_details', 'dataSource_del'])"
        label="操作"
        width="120"
        fixed="right"
      >
        <template #default="scope">
          <el-button
            v-if="permission.hasButton('dataSource_edit')"
            @click="handleAddEditDetail(true, scope.row.tid)"
            class="common-icon-btn"
            type="primary"
            plain
            link
          >
            <template #icon>
              <SvgIcon name="edit" />
            </template>
          </el-button>
          <el-button
            v-if="permission.hasButton('dataSource_details')"
            @click="handleAddEditDetail(false, scope.row.tid)"
            class="common-icon-btn"
            type="primary"
            plain
            link
          >
            <template #icon>
              <SvgIcon name="details" />
            </template>
          </el-button>
          <el-button
            v-if="permission.hasButton('dataSource_del')"
            @click="handleDel(scope.row.tid)"
            class="common-icon-btn"
            type="danger"
            plain
            link
          >
            <template #icon>
              <SvgIcon name="delete" />
            </template>
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <template #footer>
      <el-pagination
        v-model:currentPage="pageData.page"
        v-model:page-size="pageData.limit"
        :page-sizes="pageData.pageSizes"
        background
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageData.total"
        @size-change="pageData.handleSizeChange"
        @current-change="pageData.handleCurrentChange"
      />
    </template>
  </MainLayout>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import MainLayout from '@/components/MainLayout/index.vue'
import SearchTool from './SearchTool.vue'
import usePageData from '@/hooks/usePageData'
import { queryPageByParamApi, removeByIdApi } from '@/api/data_source'
import { ElMessage, ElMessageBox } from 'element-plus'
import { onActivated } from 'vue'
import useUserInfo from '@/store/useUserInfo'

const router = useRouter()

const permission = useUserInfo()

onActivated(() => {
  pageData.handleSearch(null, 1)
})

const { tableData, pageData } = usePageData(queryPageByParamApi, false)
const handleSearch = (form: any) => {
  pageData.handleSearch(form, 1)
}

// 数据源数据库详情
const handleDetails = ({ tid, adapterCode, status }: any) => {
  if (![1].includes(status)) {
    ElMessage.warning('数据源连接失败，请连接成功后再试！')
    return
  }
  if (['MONGO_DB'].includes(adapterCode)) {
    router.push({ name: 'sourceDBDetail', query: { id: tid } })
    return
  }
  if (['ELASTICSEARCH'].includes(adapterCode)) {
    router.push({ name: 'sourceEsDetail', query: { id: tid } })
    return
  }
  if (['POSTGRESQL', 'DM', 'ORACLE', 'MYSQL'].includes(adapterCode)) {
    router.push({ name: 'sourceDataDetail', query: { id: tid } })
    return
  }
  if (['QINIU', 'PARA_STOR', 'MINIO', 'ALIYUN', 'FTP', 'NFS'].includes(adapterCode)) {
    router.push({ name: 'sourceFileDetail', query: { id: tid } })
  }
}

// 新增编辑
const handleAddEditDetail = (isEdit: any, id: any) => {
  router.push({ name: 'dataSourceAddEditDetail', query: { id, isEdit } })
}

// 删除
const handleDel = (tid: string) => {
  ElMessageBox.confirm('确认要删除该数据嘛？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    try {
      const { status, message } = await removeByIdApi(tid)
      if ([200].includes(status)) {
        ElMessage.success(message)
        pageData.handleSearch(null, 1)
      } else {
        ElMessage.error(message)
      }
    } catch (error) {
      console.error(error)
    }
  })
}
</script>

<style scoped lang="less">
.datasource-name {
  color: var(--el-color-primary);
  cursor: pointer;
}
</style>
