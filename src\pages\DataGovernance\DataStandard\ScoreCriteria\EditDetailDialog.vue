<template>
  <ElDialog
    v-model="dialogFormVisible"
    :title="title"
    top="10vh"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <ElForm ref="formRef" :model="formData" :rules="rules" label-width="auto">
      <ElFormItem label="健康度评分标准名称" prop="scoreName">
        <ElInput :readonly="isReadonly" v-model="formData.scoreName" placeholder="请输入规则名称" />
      </ElFormItem>
      <ElFormItem label="打分区间" class="score-range">
        <ElFormItem label="优秀" label-width="80px">
          <el-slider
            style="--el-slider-main-bg-color: #67c23a; --el-slider-disabled-color: #67c23a"
            :disabled="isReadonly"
            v-model="formData.excellent"
            range
            :max="100"
          />
        </ElFormItem>
        <ElFormItem label="良好" label-width="80px">
          <el-slider
            style="--el-slider-main-bg-color: #409eff; --el-slider-disabled-color: #409eff"
            :disabled="isReadonly"
            v-model="formData.good"
            range
            :max="100"
          />
        </ElFormItem>
        <ElFormItem label="一般" label-width="80px">
          <el-slider
            style="--el-slider-main-bg-color: #e6a23c; --el-slider-disabled-color: #e6a23c"
            :disabled="isReadonly"
            v-model="formData.same"
            range
            :max="100"
          />
        </ElFormItem>
        <ElFormItem label="不合格" label-width="80px">
          <el-slider
            style="--el-slider-main-bg-color: #f56c6c; --el-slider-disabled-color: #f56c6c"
            :disabled="isReadonly"
            v-model="formData.unqualified"
            range
            :max="100"
          />
        </ElFormItem>
      </ElFormItem>
    </ElForm>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="setDialogFormVisible(false)">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </span>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
import { ElDialog, ElForm, ElFormItem, ElInput, ElMessage, ElSlider } from 'element-plus'
import useDialogForm from '@/hooks/useDialogForm'
import { computed, ref } from 'vue'
import { qaScoreAddApi, qaScoreUpdateApi } from '@/api/data_governance'

const { dialogFormVisible, formRef, setDialogFormVisible, handleDialogClose, validateForm } =
  useDialogForm()

const handleClose = () => {
  handleDialogClose()
  formData.value = {
    excellent: [0, 0],
    good: [0, 0],
    same: [0, 0],
    unqualified: [0, 0],
  }
}

const rules = ref<any>({
  scoreName: [{ required: true, message: '请输入健康度评分标准名称', trigger: 'blur' }],
})
const formData = ref<any>({
  excellent: [0, 0],
  good: [0, 0],
  same: [0, 0],
  unqualified: [0, 0],
})
const isReadonly = ref<boolean>(false)

const title = computed(() => {
  if (!isReadonly.value) {
    return formData.value.tid ? '编辑标准' : '新增标准'
  }
  return '标准详情'
})

const handleOpen = (row: any, isEdit: boolean) => {
  isReadonly.value = !isEdit
  if (row) {
    formData.value = {
      ...row,
      excellent: [row.excellentMin, 100],
      good: [row.goodMin, row.excellentMin],
      same: [row.mediumMin, row.goodMin],
      unqualified: [0, row.mediumMin],
    }
  }
  setDialogFormVisible(true)
}

const handleSubmit = async () => {
  if (isReadonly.value) {
    setDialogFormVisible(false)
    return
  }
  await validateForm()
  if (
    formData.value.excellent[0] !== formData.value.good[1] ||
    formData.value.good[0] !== formData.value.same[1] ||
    formData.value.same[0] !== formData.value.unqualified[1]
  ) {
    ElMessage.warning('不同健康度评分标准分数值不能重叠！')
    return
  }
  const params: any = {
    ...formData.value,
    excellentMin: formData.value.excellent[0],
    goodMin: formData.value.good[0],
    mediumMin: formData.value.same[0],
  }
  const { status, message } = formData.value.tid
    ? await qaScoreUpdateApi(params)
    : await qaScoreAddApi(params)
  if ([200].includes(status)) {
    ElMessage.success(message)
    emit('handleRefresh')
    setDialogFormVisible(false)
  } else {
    ElMessage.error(message)
  }
}

const emit = defineEmits<{ (e: 'handleRefresh'): void }>()
defineExpose({ handleOpen })
</script>

<style scoped lang="less">
.score-range {
  :deep(.el-form-item__content) {
    display: block;
    box-sizing: border-box;
    padding-right: 20px;
  }
}
</style>
