<template>
  <div v-show="graphJsonData.nodes.length && graphJsonData.lines.length" class="bloodRelationship">
    <p class="title">血缘关系</p>
    <div class="graph">
      <RelationGraph :options="options" ref="RelationGraphRef">
        <template #node="{ node }">
          <span :title="(node as any).text">{{ (node as any).text }}</span>
        </template>
      </RelationGraph>
    </div>
  </div>
</template>

<script setup lang="ts">
import RelationGraph, { RGOptions } from 'relation-graph-vue3'
import { onMounted, ref, watch } from 'vue'
import { queryRelationListApi } from '@/api/data_catalog'

const props = defineProps<{ formData: any }>()

const options: RGOptions = {
  backgroundColor: '#F8F9FA', // 背景色
  defaultNodeColor: '#ffffff', // 节点颜色
  defaultNodeFontColor: '#333333', // 节点文字颜色
  defaultNodeBorderColor: '#666666', // 节点边框颜色
  defaultNodeShape: 1, // 节点形状
  defaultLineColor: '#666666', // 线条颜色
  defaultLineShape: 1, // 线条样式
  defaultLineWidth: 1, // 线条粗细
  defaultNodeBorderWidth: 1,
  defaultNodeWidth: 200,
  defaultNodeHeight: 40,
  disableNodeClickEffect: true,
  checkedLineColor: 'rgba(90, 139, 254, 1)',
  allowShowMiniToolBar: false,
  defaultLineMarker: {
    markerWidth: 15,
    markerHeight: 15,
    refX: 30,
    refY: 7,
    data: 'M 14 7 L 1 .3 L 4 7 L .4 13 L 14 7, Z',
  },
  layout: {
    label: '中心',
    layoutName: 'tree',
    from: 'left',
  },
}

const RelationGraphRef = ref<any>()
const graphJsonData = ref<any>({
  rootId: 'root',
  nodes: [],
  lines: [],
})

const getRelationList = async () => {
  try {
    const { data, status } = await queryRelationListApi({ userFileId: props.formData.tid })
    if ([200].includes(status)) {
      const nodes: any[] = data.listNodes.map((item: any) => ({
        id: item.id,
        text: `${item.name}`,
        color: [item.id].includes(props.formData.tid) ? options.checkedLineColor : undefined,
        fontColor: [item.id].includes(props.formData.tid) ? options.defaultNodeColor : undefined,
        borderColor: [item.id].includes(props.formData.tid) ? options.checkedLineColor : undefined,
      }))
      const lines: any[] = data.listLinks.map((item: any) => ({
        from: item.source,
        to: item.target,
      }))
      graphJsonData.value = {
        rootId: props.formData.tid,
        nodes,
        lines,
      }
      if (nodes.length && nodes.length) {
        RelationGraphRef.value?.setJsonData(graphJsonData.value)
      }
    }
  } catch (error) {}
}

watch(
  () => props.formData.tid,
  () => {
    if (props.formData.tid) {
      getRelationList()
    }
  },
)

onMounted(() => {
  getRelationList()
})
</script>

<style lang="less" scoped>
.bloodRelationship {
  .title {
    font-weight: 600;
    color: #333333;
    font-size: 14px;
    margin-bottom: 15px;
  }
  .graph {
    overflow: hidden;
    height: 50vh;
    :deep(.relation-graph .rel-node) {
      line-height: 40px;
      .ellipseLine();
      padding: 0 10px;
      box-sizing: border-box;
    }
  }
}
</style>
