<template>
  <ElDialog
    v-model="dialogFormVisible"
    top="10vh"
    width="1000px"
    title="质检报告"
    :close-on-click-modal="false"
  >
    <div class="headerTop">
      <p class="title">{{ tableDataName }}</p>
      <ElButton @click="handleExport" class="btn" type="primary">
        <template #icon>
          <SvgIcon name="upload" />
        </template>
        导出
      </ElButton>
    </div>
    <div class="checkReport">
      <div class="leftTable">
        <el-table
          header-cell-class-name="common-table-header"
          cell-class-name="common-table-cell"
          :data="tableData"
          height="100%"
          row-key="tid"
          border
          highlight-current-row
          :current-row-key="currentRowKey"
          @row-click="handleRowClick"
          :row-style="{ cursor: 'pointer' }"
        >
          <el-table-column property="startTime" label="质检时间" show-overflow-tooltip>
            <template #default="scope">
              {{ scope.row.startTime || scope.row.createTime }}
            </template>
          </el-table-column>
          <el-table-column property="scoreLevel" label="检查结果" show-overflow-tooltip>
            <template #default="scope">
              <span :style="{ color: healthColor(scope.row.scoreLevel), cursor: 'pointer' }">{{
                scope.row.scoreLevel || '--'
              }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="rightTable">
        <div class="topInfo">
          <div class="info">
            <p class="infoStyle"></p>
            <p>{{ rowData.startTime || rowData.createTime }}</p>
          </div>
          <div class="info">
            <div class="infoStyle1">
              <p>
                检查结果：<span :style="{ color: healthColor(rowData.scoreLevel) }">{{
                  rowData.scoreLevel
                }}</span>
              </p>
              <p>检查得分：{{ rowData.scoreValue }}</p>
            </div>
            <p>
              质检时长：{{
                rowData.executeTime
                  ? `${rowData.executeTime}ms`
                  : diffTime(rowData.startTime, rowData.endTime)
              }}
            </p>
          </div>
        </div>
        <div class="bottomTable">
          <el-table
            header-cell-class-name="common-table-header"
            cell-class-name="common-table-cell"
            :data="rowData.ruleList"
            height="100%"
            row-key="tid"
            border
          >
            <el-table-column property="ruleName" label="规则名称" show-overflow-tooltip />
            <el-table-column property="ruleMethod" label="质量规则" show-overflow-tooltip>
              <template #default="scope">
                {{ scope.row.ruleMethod || scope.row.ruleName }}
              </template>
            </el-table-column>
            <el-table-column property="problem" label="异常信息" show-overflow-tooltip />
            <el-table-column
              v-if="[1].includes(props.dataType)"
              property="qaCount"
              label="检查数据量(条)"
              show-overflow-tooltip
            />
            <el-table-column
              v-if="[1].includes(props.dataType)"
              property="problemCount"
              label="异常数据量(条)"
              show-overflow-tooltip
            />
            <el-table-column property="suggest" label="建议" show-overflow-tooltip />
          </el-table>
        </div>
      </div>
    </div>
  </ElDialog>
</template>
<script lang="ts" setup>
import { reportApi } from '@/api/data_catalog'
import { listTableLogApi, exportReportApi, exportTableLogApi } from '@/api/data_house'
import useDialogForm from '@/hooks/useDialogForm'
import { healthColor, transferSecondsToTime } from '@/utils'
import { downloadFile } from '@/utils/fileUtils'
import { dayjs, ElDialog, ElMessage, ElTable, ElTableColumn } from 'element-plus'
import { ref } from 'vue'

const props = defineProps<{ dataType: number }>()

const { dialogFormVisible, setDialogFormVisible } = useDialogForm()

const userFileId = ref<string>('')
const handleExport = () => {
  console.log(userFileId.value, 'sssss')
  downloadFile(
    [1].includes(props.dataType)
      ? exportTableLogApi(userFileId.value)
      : exportReportApi(userFileId.value),
  )
}

const tableData = ref<any[]>([])
const rowData = ref<any>({})
const tableDataName = ref<string>('')

const currentRowKey = ref<string>('')
const handleRowClick = (row: any) => {
  rowData.value = row
  currentRowKey.value = row.tid
}

// 数据仓
const getTableLog = async ({ tid, tableName }: any) => {
  try {
    tableDataName.value = tableName
    const { data, message, status } = await listTableLogApi({ userTableId: tid })
    if ([200].includes(status)) {
      tableData.value = data
      rowData.value = data[0]
      currentRowKey.value = data[0].tid
      setDialogFormVisible(true)
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}

// 数据湖
const getFileLog = async ({ tid, fileName, suffix }: any) => {
  try {
    tableDataName.value = `${fileName}.${suffix}`
    const { data, message, status } = await reportApi({ userFileId: tid })
    if ([200].includes(status)) {
      data.tid = 1
      tableData.value = [data]
      rowData.value = data
      currentRowKey.value = data.tid
      setDialogFormVisible(true)
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}

const handleOpen = async (row: any) => {
  userFileId.value = row.tid
  if ([1].includes(props.dataType)) {
    getTableLog(row)
  } else {
    getFileLog(row)
  }
}

const diffTime = (startTime: any, endTime: any) => {
  const start = dayjs(startTime)
  const end = dayjs(endTime)
  const diffMilliseconds = end.diff(start)
  return transferSecondsToTime(diffMilliseconds)
}

defineExpose({ handleOpen })
</script>
<style lang="less" scoped>
.headerTop {
  position: relative;
  height: 40px;
  .title {
    line-height: 40px;
    text-align: center;
    font-size: 14px;
    color: #333333;
  }
  .btn {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
  }
}

.checkReport {
  margin-top: 5px;
  height: 50vh;
  box-sizing: border-box;
  display: flex;
  overflow: hidden;
  .leftTable {
    height: 100%;
    width: 220px;
    box-sizing: border-box;
  }
  .rightTable {
    margin-left: 10px;
    flex: 1;
    overflow: hidden;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    .topInfo {
      color: #666666;
      border: 1px solid #dcdfe6;
      border-bottom: none;
      padding: 10px 20px;
      .info {
        line-height: 30px;
        display: flex;
        .infoStyle {
          flex: 1;
          overflow: hidden;
          box-sizing: border-box;
          margin-right: 20px;
          .ellipseLine();
        }
        .infoStyle1 {
          flex: 1;
          overflow: hidden;
          box-sizing: border-box;
          margin-right: 20px;
          display: flex;
          align-items: center;
          p + p {
            margin-left: 20px;
          }
        }
      }
    }
    .bottomTable {
      flex: 1;
      overflow: hidden;
      box-sizing: border-box;
    }
  }
}
</style>
