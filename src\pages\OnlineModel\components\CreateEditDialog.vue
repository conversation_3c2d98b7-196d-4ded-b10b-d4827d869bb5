<template>
  <el-dialog
    :title="props.isCreateTask ? '新增任务' : '编辑任务'"
    v-model="dialogFormVisible"
    width="640px"
    :close-on-click-modal="false"
    @close="handleDialogClose"
  >
    <el-form ref="formRef" :model="formData" :rules="rules">
      <el-form-item label="任务名称" prop="taskName">
        <el-input placeholder="请输入任务名称" v-model="formData.taskName"></el-input>
      </el-form-item>
      <el-form-item label="定时任务" prop="timerType">
        <el-radio-group v-model="formData.timerType">
          <el-radio :value="1" label="否"></el-radio>
          <el-radio :value="2" label="是"></el-radio>
        </el-radio-group>
      </el-form-item>
      <template v-if="[2].includes(formData.timerType)">
        <el-form-item label="执行周期" required>
          <div class="select-model">
            <el-form-item style="width: 150px" prop="timerCycle">
              <el-select v-model="formData.timerCycle" placeholder="请选择">
                <el-option label="小时" :value="5" />
                <el-option label="天" :value="4" />
                <el-option label="周" :value="3" />
                <el-option label="月" :value="2" />
              </el-select>
            </el-form-item>
            <div class="select-row">
              <el-form-item
                v-if="[3].includes(formData.timerCycle)"
                class="row-item"
                prop="timerCycleDay"
              >
                <el-select v-model="formData.timerCycleDay" placeholder="选择时间">
                  <el-option label="星期一" :value="1" />
                  <el-option label="星期二" :value="2" />
                  <el-option label="星期三" :value="3" />
                  <el-option label="星期四" :value="4" />
                  <el-option label="星期五" :value="5" />
                  <el-option label="星期六" :value="6" />
                  <el-option label="星期日" :value="7" />
                </el-select>
              </el-form-item>
              <el-form-item
                v-if="[2].includes(formData.timerCycle)"
                class="row-item"
                prop="timerCycleDay"
              >
                <el-select v-model="formData.timerCycleDay" placeholder="选择时间">
                  <el-option v-for="p in 30" :key="p" :label="p" :value="p" />
                </el-select>
              </el-form-item>
              <el-form-item class="row-item" prop="timerCycleHour">
                <el-time-select
                  v-show="[5].includes(formData.timerCycle)"
                  v-model="formData.timerCycleHour"
                  start="00:00"
                  step="00:01"
                  end="0:59"
                  placeholder="选择时间"
                />
                <el-time-select
                  v-show="![5].includes(formData.timerCycle)"
                  v-model="formData.timerCycleHour"
                  start="00:00"
                  step="00:01"
                  end="23:59"
                  placeholder="选择时间"
                />
              </el-form-item>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="立即执行" prop="isRun">
          <el-radio-group v-model="formData.isRun">
            <el-radio :value="0" label="否"></el-radio>
            <el-radio :value="1" label="是"></el-radio>
          </el-radio-group>
        </el-form-item>
      </template>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="setDialogFormVisible(false)">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import useDialogForm from '@/hooks/useDialogForm'
import { reactive, ref } from 'vue'

const props = defineProps<{ isCreateTask?: boolean }>()

const formData = ref<any>({
  timerType: 1,
})
const rules = reactive<any>({
  taskName: [
    { required: true, message: '请输入任务名称！', trigger: 'blur' },
    { min: 1, max: 20, message: '1-20字符之间！', trigger: 'blur' },
  ],
  timerType: [{ required: true, message: '定时任务必填！', trigger: 'change' }],
  timerCycle: [{ required: true, message: '执行周期必填！', trigger: 'change' }],
  timerCycleDay: [{ required: true, message: '时间必填必填！', trigger: 'change' }],
  timerCycleHour: [{ required: true, message: '时间必填必填！', trigger: 'change' }],
  isRun: [{ required: true, message: '是否立即执行必填！', trigger: 'change' }],
})

const { dialogFormVisible, setDialogFormVisible, formRef, handleDialogClose, validateForm } =
  useDialogForm()

const handleSubmit = async () => {
  try {
    await validateForm()
    emit('handleSubmit', formData.value, setDialogFormVisible)
  } catch (error) {}
}

const handleOpen = (row: any) => {
  if (row) {
    delete row.reqParam
    formData.value = row
  }
  setDialogFormVisible(true)
}

const emit = defineEmits<{
  handleSubmit: [form: any, setDialogFormVisible: any]
}>()
defineExpose({ handleOpen })
</script>

<style lang="less" scoped>
.select-model {
  width: 100%;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  .select-row {
    margin-left: 15px;
    flex: 1;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    .row-item {
      flex: 1;
      box-sizing: border-box;
    }
    .row-item + .row-item {
      margin-left: 15px;
    }
  }
}
</style>
