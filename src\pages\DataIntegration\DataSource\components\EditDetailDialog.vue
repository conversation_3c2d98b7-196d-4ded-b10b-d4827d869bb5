<template>
  <el-dialog
    v-model="dialogFormVisible"
    top="10vh"
    title="编辑"
    width="600px"
    :close-on-click-modal="false"
    destroy-on-close
    @close="handleDialogClose"
  >
    <el-scrollbar height="50vh" style="padding-right: 10px">
      <el-form ref="formRef" :model="formData" scroll-to-error label-width="auto">
        <el-form-item v-for="p in fieldOptions" :label="p" :key="p" :prop="p">
          <el-input v-model="formData[p]" placeholder="请输入" />
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="setDialogFormVisible(false)">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import useDialogForm from '@/hooks/useDialogForm'
import { ref } from 'vue'
import { useRoute } from 'vue-router'
import { editDetailApi, mongoEditDetailApi } from '@/api/data_source.ts'
import { ElMessage } from 'element-plus'

const route = useRoute()

const { dialogFormVisible, handleDialogClose, setDialogFormVisible, formRef } = useDialogForm()

const rowId = ref<any>()
const formData = ref<any>({})
const fieldOptions = ref<any[]>([])

const handleOpen = (row: any) => {
  fieldOptions.value = Object.keys(row.detail)
  formData.value = row.detail
  rowId.value = row.id
  setDialogFormVisible(true)
}
const handleSubmit = async () => {
  try {
    const { id, dbname } = route.query
    const params = {
      dataSourceId: id,
      id: rowId.value,
      tableName: ['sourceEsDetail'].includes(route.name as string) ? undefined : dbname,
      detail: { ...formData.value },
    }
    const { status, message } = ['sourceEsDetail'].includes(route.name as string)
      ? await editDetailApi(params)
      : await mongoEditDetailApi(params)
    if ([200].includes(status)) {
      ElMessage.success(message)
      emit('handleRefresh')
      setDialogFormVisible(false)
    } else {
      ElMessage.error(message)
    }
  } catch (e) {}
}

const emit = defineEmits<{
  (e: 'handleRefresh'): void
}>()
defineExpose({ handleOpen })
</script>

<style scoped lang="less"></style>
