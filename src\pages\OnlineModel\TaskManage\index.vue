<template>
  <MainLayout>
    <template #header>
      <SearchTool @handle-search="handleSearch" />
    </template>
    <el-table
      v-loading="pageData.loading"
      header-cell-class-name="common-table-header"
      cell-class-name="common-table-cell"
      ref="multipleTableRef"
      :data="tableData"
      height="100%"
      style="width: 100%"
      row-key="tid"
    >
      <el-table-column property="taskName" label="任务名称" show-overflow-tooltip>
      </el-table-column>
      <el-table-column property="dataTypeName" label="是否定时任务" show-overflow-tooltip>
        <template #default="scope">
          {{ scope.row.timerType === 2 ? '是' : '否' }}
        </template>
      </el-table-column>
      <el-table-column property="startTime" label="最近执行时间" show-overflow-tooltip />
      <el-table-column property="taskStatusValue" label="最近任务执行状态" />
      <el-table-column property="planTime" label="下次执行时间">
        <template #default="scope">
          {{ scope.row.planTime || '--' }}
        </template>
      </el-table-column>
      <el-table-column
        v-if="
          permission.hasButton([
            'taskManage_start',
            'taskManage_stop',
            'taskManage_edit',
            'taskManage_details',
            'taskManage_del',
          ])
        "
        label="操作"
        width="180"
        fixed="right"
      >
        <template #default="scope">
          <el-button
            v-if="isExecuteShow(scope.row) && permission.hasButton('taskManage_start')"
            @click="handleStart(scope.row.tid)"
            title="开始执行"
            class="common-icon-btn"
            type="primary"
            plain
            link
          >
            <template #icon>
              <SvgIcon name="videoPlay" />
            </template>
          </el-button>
          <el-button
            v-if="isSuspendShow(scope.row) && permission.hasButton('taskManage_stop')"
            @click="handleStop(scope.row.tid)"
            title="暂停执行"
            class="common-icon-btn"
            type="primary"
            plain
            link
          >
            <template #icon>
              <SvgIcon name="videoPause" />
            </template>
          </el-button>
          <el-button
            v-if="![3].includes(scope.row.taskStatus) && permission.hasButton('taskManage_edit')"
            title="编辑"
            @click="handleEdit(scope.row)"
            class="common-icon-btn"
            type="primary"
            plain
            link
          >
            <template #icon>
              <SvgIcon name="edit" />
            </template>
          </el-button>
          <el-button
            v-if="scope.row.taskModelCount && permission.hasButton('taskManage_details')"
            title="详情"
            @click="handleDetails(scope.row)"
            class="common-icon-btn"
            type="primary"
            plain
            link
          >
            <template #icon>
              <SvgIcon name="details" />
            </template>
          </el-button>
          <el-button
            v-if="![3].includes(scope.row.taskStatus) && permission.hasButton('taskManage_del')"
            @click="handleDel(scope.row.tid)"
            title="删除"
            class="common-icon-btn"
            type="danger"
            plain
            link
          >
            <template #icon>
              <SvgIcon name="delete" />
            </template>
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <template #footer>
      <el-pagination
        v-model:currentPage="pageData.page"
        v-model:page-size="pageData.limit"
        :page-sizes="pageData.pageSizes"
        background
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageData.total"
        @size-change="pageData.handleSizeChange"
        @current-change="pageData.handleCurrentChange"
      />
    </template>
    <CreateEditDialog @handle-submit="handleSubmit" ref="CreateEditDialogRef" />
  </MainLayout>
</template>

<script setup lang="ts">
import MainLayout from '@/components/MainLayout/index.vue'
import SearchTool from '@/pages/OnlineModel/TaskManage/SearchTool.vue'
import CreateEditDialog from '@/pages/OnlineModel/components/CreateEditDialog.vue'
import usePageData from '@/hooks/usePageData'
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import {
  queryTaskApi,
  removeTaskApi,
  startTaskApi,
  stopTaskApi,
  modifyTaskByTaskIdApi,
} from '@/api/online_model'
import { ElMessageBox, ElMessage } from 'element-plus'
import useUserInfo from '@/store/useUserInfo'

const permission = useUserInfo()

const router = useRouter()

const { tableData, pageData } = usePageData(queryTaskApi, false)

pageData.handleSearch({ taskTypes: 3 }, 1)

const handleSearch = (params: any) => {
  pageData.handleSearch(
    {
      ...params,
      taskTypes: 3,
      leCreateTime: params.dateRange && params.dateRange[0],
      geCreateTime: params.dateRange && params.dateRange[1],
      dateRange: undefined,
    },
    1,
  )
}

// 执行按钮是否显示
const isExecuteShow = (row: any) => {
  const { taskStatus, timerType } = row
  if (![2].includes(timerType) && [0, 1, 4].includes(taskStatus)) {
    return true
  }
  if ([2].includes(timerType) && [4].includes(taskStatus)) {
    return true
  }
  return false
}
// 开始执行
const handleStart = async (tid: string) => {
  try {
    const { message, status } = await startTaskApi(tid)
    if ([200].includes(status)) {
      ElMessage.success(message || '操作成功！')
      pageData.handleSearch(null, 1)
    } else {
      ElMessage.error(message || '操作失败！')
    }
  } catch (error) {}
}

// 暂停按钮是否显示
const isSuspendShow = (row: any) => {
  const { taskStatus, timerType } = row
  if (![2].includes(timerType) && [3].includes(taskStatus)) {
    return true
  }
  if ([2].includes(timerType) && [0, 1, 2, 3].includes(taskStatus)) {
    return true
  }
  return false
}
// 结束执行
const handleStop = async (tid: string) => {
  try {
    const { message, status } = await stopTaskApi(tid)
    if ([200].includes(status)) {
      ElMessage.success(message || '操作成功！')
      pageData.handleSearch(null, 1)
    } else {
      ElMessage.error(message || '操作失败！')
    }
  } catch (error) {}
}

// 删除
const handleDel = (tid: string) => {
  ElMessageBox.confirm('是否确定删除当前数据？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    try {
      const { status, message } = await removeTaskApi(tid)
      if ([200].includes(status)) {
        ElMessage.success(message)
        pageData.handleSearch(null, 1)
      } else {
        ElMessage.error(message)
      }
    } catch (error) {
      console.error(error)
    }
  })
}

// 编辑
const CreateEditDialogRef = ref<any>()
const handleEdit = (row: any) => {
  CreateEditDialogRef.value.handleOpen(row)
}
const handleSubmit = async (form: any, setDialogFormVisible: any) => {
  try {
    const { status, message } = await modifyTaskByTaskIdApi(form)
    if ([200].includes(status)) {
      ElMessage.success(message || '修改成功！')
      setDialogFormVisible(false)
      pageData.handleSearch(null, 1)
    } else {
      ElMessage.error(message || '修改失败！')
    }
  } catch (error) {}
}

// 详情
const handleDetails = (row: any) => {
  router.push({
    name: 'taskManageDetail',
    query: {
      tid: row.tid,
      taskName: row.taskName,
    },
  })
}
</script>

<style lang="less" scoped></style>
