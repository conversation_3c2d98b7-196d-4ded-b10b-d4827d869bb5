<template>
  <div class="LeftMenu">
    <el-input v-model="searchText" placeholder="请输入算子名称" clearable style="width: 100%">
      <template #suffix>
        <SvgIcon style="cursor: pointer" name="search" />
      </template>
    </el-input>
    <div class="menuList">
      <el-scrollbar>
        <el-tree
          ref="treeRef"
          :data="dataSource"
          node-key="tid"
          :indent="10"
          default-expand-all
          :expand-on-click-node="false"
          :props="propsTree"
          :filter-node-method="filterNode"
        >
          <template #default="{ node }">
            <span
              class="custom-tree-node"
              draggable="true"
              @dragend="(e: any) => handleDragend(node.data, e)"
            >
              <img
                :src="node.data.icon ? `data:image/jpeg;base64,${node.data.icon}` : fileDir"
                alt="file"
              />
              <span :title="node.label">{{ node.label }}</span>
            </span>
          </template>
        </el-tree>
      </el-scrollbar>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import fileDir from '@/assets/fileIconImg/file_dir.png'
import { ElMessage, ElTree } from 'element-plus'
import useTreeData from '@/pages/OnlineModel/hooks/useTreeData'

const props = defineProps<{ isRunning: boolean }>()

const treeRef = ref<InstanceType<typeof ElTree>>()
const { propsTree, dataSource } = useTreeData()

// 搜索
const searchText = ref<string>('')
watch(searchText, (val: string) => {
  treeRef.value!.filter(val)
})
const filterNode = (value: string, data: any) => {
  if (!value) return true
  return data.nodeName.includes(value)
}

const handleDragend = (node: any, e: any) => {
  console.log(node, 'object')
  if (node.isChildNode && props.isRunning) {
    ElMessage.warning('当前模型正在运行中，无法新建节点！')
    return
  }
  if (node.isChildNode && !props.isRunning) {
    emit('handleNodeClick', node, e)
  }
}

const emit = defineEmits<{
  handleNodeClick: [node: any, e: any]
}>()
</script>

<style lang="less" scoped>
.LeftMenu {
  height: 100%;
  width: 200px;
  background-color: @withe;
  box-shadow: 0 8px 8px 8px rgba(0, 0, 0, 0.05);
  padding: 20px 10px;
  box-sizing: border-box;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  z-index: 100;
  user-select: none;
  .menuList {
    margin-top: 10px;
    flex: 1;
    box-sizing: border-box;
    overflow: hidden;
    .custom-tree-node {
      display: flex;
      align-items: center;
      width: auto;
      overflow: hidden;
      img {
        width: 16px;
      }
      span {
        margin-left: 8px;
        flex: 1;
        .ellipseLine();
      }
    }
  }
}
</style>
