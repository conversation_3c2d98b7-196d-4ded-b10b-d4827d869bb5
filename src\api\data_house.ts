import useUserInfo from '@/store/useUserInfo'
import request, { baseUrl } from '@/utils/request'

// 查询列表
export const userTableParamApi = (data: any) => request.post(`/userTable/queryPageByParam`, data)

// 删除-用户数据库
export const userTableRemoveByIdApi = (tid: string) =>
  request.delete(`/userTable/removeById/${tid}`)

// 查询数据仓质检报告
export const listTableLogApi = (data: any) => request.get(`/qaTableRule/listTableLog`, data)

// 查询当前表规则
export const listTableRuleApi = (data: any) => request.get(`/qaTableRule/listTableRule`, data)

// 查询当前表字段
export const listTableColumnApi = (data: any) => request.get(`/qaTableRule/listTableColumn`, data)

// 配置质检规则
export const userTableUpdateRuleApi = (data: any) =>
  request.post(`/qaTableRule/updateRule/${data.userTableId}`, data.ruleList)

// 立即质检
export const userTableStartQaApi = (data: any) =>
  request.post(`/qaTableRule/startQa/${data.userTableId}`, data.ruleList)

// 删除表数据
export const removeDataApi = (data: any) => request.post(`/rdsManage/removeData`, data)

// 查询-数据目录（主键）
export const catalogQueryByIdApi = (tid: string) => request.post(`/dataCatalog/queryById/${tid}`)

// 查询-数据目录数据库（主键）
export const catalogQueryDatabaseByIdApi = (tid: string) =>
  request.get(`/dataCatalog/queryDatabaseById/${tid}`)

// sql语句执行
export const sqlEditRunApi = (data: any) => request.post(`/sqlEdit/run`, data)

// sql语句执行
export const getHistoryPageApi = (data: any) => request.post(`/sqlEdit/getHistoryPage`, data)

// 发布-用户数据库服务
export const userTablePublishApi = (tid: string) => request.get(`/userTable/publish/${tid}`)

// 根据id查询数据详情
export const getUserTableProductDetailByUserFileIdApi = (data: any) =>
  request.get(`/dateWareHouse/getProductDetailByUserFileId`, data)

// 获取服务
export const userTableServerListApi = (tid: string) =>
  request.post(`/dateWareHouse/serverList/${tid}`)

// 查询文件元数据
export const getUserTableExcellentDetailApi = (data: any) =>
  request.get(`/dateWareHouse/getExcellentDetail`, data)

// 注册数据
export const userTableproductRegisterApi = (data: any) =>
  request.post(`/dateWareHouse/productRegister`, data)

// 上传缩略图
export const dateWareHouseUploadThumbnailApi: string = `${baseUrl}/dateWareHouse/uploadThumbnail`

// 导出质检报告
export const exportReportApi = (tid: string) => {
  const permission = useUserInfo()
  return `${baseUrl}/qaDetail/exportReport?userFileId=${tid}&token=${permission.token}`
}

// 导出数据仓质检报告
export const exportTableLogApi = (tid: string) => {
  const permission = useUserInfo()
  return `${baseUrl}/qaTableRule/exportTableLog?userTableId=${tid}&token=${permission.token}`
}
