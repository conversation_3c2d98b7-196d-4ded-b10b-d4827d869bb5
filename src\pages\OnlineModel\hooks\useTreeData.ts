import { ElMessage } from 'element-plus'
import { ref } from 'vue'
import { queryTreeListApi } from '@/api/online_model.ts'

export default function useTreeData() {
  const propsTree: any = {
    label: 'nodeName',
    value: 'tid',
    children: 'childVOList'
  }

  const dataSource = ref<any[]>([])
  const getTreeList = async () => {
    try {
      const { data, status, message } = await queryTreeListApi()
      if ([200].includes(status)) {
        dataSource.value = data
      } else {
        ElMessage.error(message)
      }
    } catch (error) {}
  }

  getTreeList()

  return { dataSource, propsTree }
}
