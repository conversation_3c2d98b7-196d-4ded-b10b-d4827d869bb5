import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import legacy from '@vitejs/plugin-legacy'
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons'
import viteCompression from 'vite-plugin-compression'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import { resolve } from 'path'
import cesium from 'vite-plugin-cesium'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    cesium(),
    // 浏览器兼容
    legacy({
      targets: ['defaults', 'not IE 11'],
      additionalLegacyPolyfills: ['regenerator-runtime/runtime'],
    }),
    // svg动态加载
    createSvgIconsPlugin({
      iconDirs: [resolve(process.cwd(), 'src/icons')],
      symbolId: 'icon-[dir]-[name]',
    }),
    // * gzip compress
    viteCompression({
      verbose: true,
      disable: false,
      threshold: 10240,
      algorithm: 'gzip',
      ext: '.gz',
    }),
    // el组件库自动导入
    AutoImport({
      resolvers: [ElementPlusResolver()],
    }),
    Components({
      resolvers: [ElementPlusResolver({ importStyle: 'sass' })],
    }),
  ],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
    },
  },
  css: {
    preprocessorOptions: {
      less: {
        additionalData: `@import "@/styles/var.less";`,
      },
    },
  },
  server: {
    host: true,
    port: 8000,

    proxy: {
      '/api': {
        target: 'https://datam2.geovisearth.com/api',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, ''),
      },
      '/e-server': {
        target: 'https://datam2.geovisearth.com/e-server',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/e-server/, ''),
      },
      '/websocket': {
        target: 'wss://datam2.geovisearth.com/websocket',
        changeOrigin: true,
        ws: true,
        rewrite: (path) => path.replace(/^\/websocket/, ''),
      },
      '/api/iot/api/v1/webSocket': {
        target: 'wss://datam2.geovisearth.com/api/iot/api/v1/webSocket',
        changeOrigin: true,
        ws: true,
        rewrite: (path) => path.replace(/^\/api\/iot\/api\/v1\/webSocket/, ''),
      },
      '/modelApi': {
        target: 'http://***********:8088',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/modelApi/, ''),
      },
    },
  },
  esbuild: {
    pure: ['console.log', 'debugger'],
  },
  build: {
    sourcemap: false,
    outDir: 'dist',
    minify: 'esbuild',
    rollupOptions: {
      output: {
        chunkFileNames: 'assets/js/[name]-[hash].js',
        entryFileNames: 'assets/js/[name]-[hash].js',
        assetFileNames: 'assets/[ext]/[name]-[hash].[ext]',
      },
    },
  },
})
