<template>
  <MainLayout>
    <template #header>
      <SearchTool @handle-search="handleSearch" @handle-create="handleAdd" />
    </template>
    <el-table
      v-loading="pageData.loading"
      header-cell-class-name="common-table-header"
      cell-class-name="common-table-cell"
      ref="multipleTableRef"
      :data="tableData"
      height="100%"
      style="width: 100%"
      row-key="tid"
    >
      <el-table-column property="taskName" label="任务名称" show-overflow-tooltip />
      <el-table-column
        property="adapterCodeDesc"
        label="采集适配器"
        show-overflow-tooltip
        min-width="100px"
      >
      </el-table-column>
      <el-table-column property="datasourceName" label="数据源" show-overflow-tooltip />
      <el-table-column property="dataTypeName" label="入库方式" show-overflow-tooltip />
      <el-table-column property="auditStatusName" label="审核状态" show-overflow-tooltip />
      <el-table-column
        property="taskStatusStr"
        label="是否定时执行"
        show-overflow-tooltip
        min-width="120px"
      >
        <template #default="scope">
          {{ scope.row.timerType === 2 ? '是' : '否' }}
        </template>
      </el-table-column>
      <el-table-column
        property="startTime"
        label="最近执行时间"
        show-overflow-tooltip
        min-width="120px"
      />
      <el-table-column
        property="taskStatusValue"
        label="最近执行状态"
        show-overflow-tooltip
        min-width="120px"
      />
      <el-table-column
        property="planTime"
        label="下次执行时间"
        show-overflow-tooltip
        min-width="120px"
      >
        <template #default="scope">
          {{ scope.row.planTime || '--' }}
        </template>
      </el-table-column>
      <el-table-column property="userName" label="创建人" show-overflow-tooltip />
      <el-table-column property="createTime" label="创建时间" show-overflow-tooltip />
      <el-table-column label="操作" width="150" fixed="right">
        <template #default="scope">
          <el-button
            v-if="isExecuteShow(scope.row)"
            @click="handleStart(scope.row.tid)"
            title="开始执行"
            class="common-icon-btn"
            type="primary"
            plain
            link
          >
            <template #icon>
              <SvgIcon name="videoPlay" />
            </template>
          </el-button>
          <el-button
            v-if="isSuspendShow(scope.row) && permission.hasButton('taskManage_stop')"
            @click="handleStop(scope.row.tid)"
            title="暂停执行"
            class="common-icon-btn"
            type="primary"
            plain
            link
          >
            <template #icon>
              <SvgIcon name="videoPause" />
            </template>
          </el-button>
          <el-button
            v-if="permission.hasButton('dataGather_edit')"
            title="编辑"
            @click="EditDialogRef.handleOpen(scope.row)"
            class="common-icon-btn"
            type="primary"
            plain
            link
          >
            <template #icon>
              <SvgIcon name="edit" />
            </template>
          </el-button>
          <el-button
            v-if="isShowDetail(scope.row) && permission.hasButton('dataGather_details')"
            title="详情"
            @click="handleDetail(scope.row)"
            class="common-icon-btn"
            type="primary"
            plain
            link
          >
            <template #icon>
              <SvgIcon name="details" />
            </template>
          </el-button>
          <el-button
            v-if="![3].includes(scope.row.taskStatus) && permission.hasButton('dataGather_del')"
            @click="handleDel(scope.row.tid)"
            title="删除"
            class="common-icon-btn"
            type="danger"
            plain
            link
          >
            <template #icon>
              <SvgIcon name="delete" />
            </template>
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <template #footer>
      <el-pagination
        v-model:currentPage="pageData.page"
        v-model:page-size="pageData.limit"
        :page-sizes="pageData.pageSizes"
        background
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageData.total"
        @size-change="pageData.handleSizeChange"
        @current-change="pageData.handleCurrentChange"
      />
    </template>
    <EditDialog @handle-refresh="pageData.handleSearch(null, 1)" ref="EditDialogRef" />
  </MainLayout>
</template>

<script setup lang="ts">
import MainLayout from '@/components/MainLayout/index.vue'
import SearchTool from './SearchTool.vue'
import usePageData from '@/hooks/usePageData'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import useUserInfo from '@/store/useUserInfo'
import { queryTaskApi, removeTaskApi, startTaskApi, stopTaskApi } from '@/api/online_model'
import EditDialog from './EditDialog.vue'
import { ref } from 'vue'

const permission = useUserInfo()

const router = useRouter()
const { tableData, pageData } = usePageData(queryTaskApi, false)
pageData.handleSearch({ taskTypes: 1 }, 1)

// 执行按钮是否显示
const isExecuteShow = (row: any) => {
  const { taskStatus, timerType } = row
  if (![2].includes(timerType) && [0, 1, 4].includes(taskStatus)) {
    return true
  }
  if ([2].includes(timerType) && [4].includes(taskStatus)) {
    return true
  }
  return false
}
// 开始执行
const handleStart = async (tid: string) => {
  try {
    const { message, status } = await startTaskApi(tid)
    if ([200].includes(status)) {
      ElMessage.success(message || '操作成功！')
      pageData.handleSearch(null, 1)
    } else {
      ElMessage.error(message || '操作失败！')
    }
  } catch (error) {}
}

// 暂停按钮是否显示
const isSuspendShow = (row: any) => {
  const { taskStatus, timerType } = row
  if (![2].includes(timerType) && [3].includes(taskStatus)) {
    return true
  }
  if ([2].includes(timerType) && [0, 1, 2, 3].includes(taskStatus)) {
    return true
  }
  return false
}
// 结束执行
const handleStop = async (tid: string) => {
  try {
    const { message, status } = await stopTaskApi(tid)
    if ([200].includes(status)) {
      ElMessage.success(message || '操作成功！')
      pageData.handleSearch(null, 1)
    } else {
      ElMessage.error(message || '操作失败！')
    }
  } catch (error) {}
}

const EditDialogRef = ref<any>()
const handleAdd = () => {
  router.push({ name: 'addGather' })
}

const handleSearch = (form: any) => {
  pageData.handleSearch({ ...pageData.searchForm, ...form }, 1)
}

const isShowDetail = (row: any) => {
  return (
    ([1].includes(row.taskStatus) && [1].includes(row.auditStatus)) ||
    [0, 2, 3].includes(row.auditStatus)
  )
}
const handleDetail = ({ tid, auditStatus }: any) => {
  if ([1].includes(auditStatus)) {
    router.push({ name: 'dataGatherDetail', query: { tid } })
  } else {
    router.push({ name: 'gatherDetail', query: { tid } })
  }
}

// 删除
const handleDel = (tid: string) => {
  ElMessageBox.confirm('是否确定删除当前数据？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    try {
      const { status, message } = await removeTaskApi(tid)
      if ([200].includes(status)) {
        ElMessage.success(message)
        pageData.handleSearch(null, 1)
      } else {
        ElMessage.error(message)
      }
    } catch (error) {
      console.error(error)
    }
  })
}
</script>

<style scoped lang="less"></style>
