<template>
  <div class="DataDetail">
    <LeftMenu @updateTaskId="getTableData" />
    <div class="main">
      <div class="header">
        <div class="title">
          <div class="back" @click="router.back">
            <SvgIcon name="arrowLeft" />
            <span>返回</span>
          </div>
          <span>{{ rowData?.taskVO?.taskName }}{{ rowData.createTimeStr }}</span>
          <div class="icon">
            <SvgIcon
              @click="menuActive = 1"
              :class="['item', [1].includes(menuActive) ? 'active' : '']"
              name="onlineModelList"
            />
            <SvgIcon
              @click="menuActive = 0"
              :class="['item', [0].includes(menuActive) ? 'active' : '']"
              name="onlineModelArchitecture"
            />
          </div>
        </div>
        <div class="text">
          <p>
            开始：<span>{{ rowData.startTime }}</span>
          </p>
          <p>
            结束：<span>{{ rowData.endTime }}</span>
          </p>
          <p>
            时长：<span>{{
              rowData.runTimes && rowData.runTimes >= 1000
                ? transferSecondsToTime(rowData.runTimes || 0, 'milliseconds')
                : `${rowData.runTimes || 0}ms`
            }}</span>
          </p>
        </div>
      </div>
      <div class="content">
        <el-table
          v-show="[1].includes(menuActive)"
          stripe
          header-cell-class-name="common-table-header"
          cell-class-name="common-table-cell"
          ref="multipleTableRef"
          :data="tableData"
          height="100%"
          style="width: 100%"
          row-key="tid"
        >
          <el-table-column
            property="dataName"
            :label="[1].includes(rowData.dataType) ? '表名' : '文件名称'"
            show-overflow-tooltip
          />
          <el-table-column
            property="catalogName"
            :label="[1].includes(rowData.dataType) ? '数据库' : '数据目录'"
            show-overflow-tooltip
          />
          <el-table-column property="startTime" label="采集时间" show-overflow-tooltip />
        </el-table>
        <div class="dataLog">
          <div class="status">
            <p>
              已处理文件数: <span>{{ logData.total }}</span>
            </p>
            <p>
              失败文件: <span>{{ logData.fail }}</span>
            </p>
          </div>
          <div class="logContent">
            <ElScrollbar>
              <div
                class="log-item"
                v-for="(item, index) in logData.dataImportFileLogVOList"
                :key="index"
              >
                <span>{{ item.createTime }}</span>
                <span>{{ item.log }}</span>
              </div>
            </ElScrollbar>
          </div>
        </div>
      </div>
      <div v-show="[1].includes(menuActive)" class="page">
        <el-pagination
          v-model:currentPage="pageData.page"
          v-model:page-size="pageData.limit"
          :page-sizes="pageData.pageSizes"
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="pageData.total"
          @size-change="pageData.handleSizeChange"
          @current-change="pageData.handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import LeftMenu from './LeftMenu.vue'
import { ref } from 'vue'
import usePageData from '@/hooks/usePageData'
import { queryByDataImportIdApi, queryByIdApi, queryDetailPageApi } from '@/api/data_import'
import { ElMessage, ElScrollbar } from 'element-plus'
import { transferSecondsToTime } from '@/utils'

const router = useRouter()

const { tableData, pageData } = usePageData(queryDetailPageApi, false)

const menuActive = ref<number>(1)
const getTableData = async (tid: any) => {
  await getqueryById(tid)
  pageData.handleSearch({ dataImportId: tid }, 1)
  getLogData(tid)
}

const rowData = ref<any>({})
const getqueryById = async (tid: string) => {
  try {
    const { data, message, status } = await queryByIdApi(tid)
    if ([200].includes(status)) {
      rowData.value = data
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}

const logData = ref<any>({})
const getLogData = async (tid: any) => {
  try {
    const { data, message, status } = await queryByDataImportIdApi(tid)
    if ([200].includes(status)) {
      logData.value = data
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}
</script>

<style lang="less" scoped>
.DataDetail {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  display: flex;
  overflow: hidden;
  .main {
    flex: 1;
    box-sizing: border-box;
    overflow: hidden;
    margin-left: 20px;
    background-color: @withe;
    box-shadow: 0 8px 8px 8px rgba(0, 0, 0, 0.05);
    display: flex;
    flex-direction: column;
    padding: 20px;
    .header {
      margin-bottom: 20px;
      .title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        box-sizing: border-box;
        .back {
          display: flex;
          align-items: center;
          cursor: pointer;
          :nth-child(1) {
            font-size: 28px;
          }
          span {
            font-size: 14px;
            color: #333333;
          }
        }
        > span {
          font-size: 18px;
          color: #333333;
        }
        .icon {
          font-size: 20px;
          color: #a7b7cb;
          .item {
            cursor: pointer;
          }
          .item + .item {
            margin-left: 10px;
          }
          .active {
            color: var(--el-color-primary);
          }
        }
      }
      .text {
        margin-top: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        p {
          font-size: 14px;
          color: #333333;
          span {
            color: #666666;
          }
        }
        p + p {
          margin-left: 20px;
        }
      }
    }
    .content {
      flex: 1;
      box-sizing: border-box;
      overflow: hidden;
      .dataLog {
        height: 100%;
        box-sizing: border-box;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        .status {
          display: flex;
          align-items: center;
          p + p {
            margin-left: 20px;
          }
          > :nth-child(1) {
            span {
              color: var(--el-color-primary);
            }
          }
          > :nth-child(2) {
            span {
              color: red;
            }
          }
        }
        .logContent {
          margin-top: 10px;
          flex: 1;
          box-sizing: border-box;
          overflow: hidden;
          border: 1px solid #333333;
          background: #eaeaea;
          padding: 15px;
          .log-item {
            display: flex;
            align-items: center;
            span {
              font-size: 14px;
              font-weight: 400;
              color: #333333;
              line-height: 24px;
            }
            :last-child {
              margin-left: 10px;
              flex: 1;
              overflow: hidden;
              .ellipseLine();
            }
          }
        }
      }
    }
    .page {
      margin-top: 15px;
      display: flex;
      justify-content: flex-end;
    }
  }
}
</style>
