<template>
  <ElDialog
    v-model="dialogFormVisible"
    top="10vh"
    width="500px"
    title="提交审核"
    :close-on-click-modal="false"
  >
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="auto">
      <el-form-item label="提交人" prop="catalogAlias">
        {{ permission.userInfo?.name }}
      </el-form-item>
      <el-form-item label="描述信息" prop="applyDesc">
        <el-input
          v-model="formData.applyDesc"
          :rows="3"
          type="textarea"
          placeholder="请填写申请描述信息"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="setDialogFormVisible(false)">取 消</el-button>
        <el-button type="primary" :loading @click="handleSubmit">确 定</el-button>
      </span>
    </template>
  </ElDialog>
</template>
<script setup lang="ts">
import { dataCatalogAddApi } from '@/api/lake_catalog'
import useDialogForm from '@/hooks/useDialogForm'
import useUserInfo from '@/store/useUserInfo'
import { ElMessage } from 'element-plus'
import { ref } from 'vue'

const props = defineProps<{ tableData: any[] }>()

const permission = useUserInfo()

const { dialogFormVisible, setDialogFormVisible, formRef, validateForm } = useDialogForm()
const formData = ref<any>({ applyDesc: '' })

const rules = ref<any>({
  applyDesc: [{ required: true, message: '申请描述信息必填！', trigger: 'blur' }],
})

const loading = ref<boolean>(false)
const handleSubmit = async () => {
  try {
    loading.value = true
    await validateForm()
    const { status, message } = await dataCatalogAddApi({
      ...formData.value,
      dataType: 2,
      dataCatalogApplyRelationDTOList: props.tableData,
    })
    if ([200].includes(status)) {
      ElMessage.success(message)
      setDialogFormVisible(false)
    } else {
      ElMessage.error(message)
    }
    loading.value = false
  } catch (error) {
    loading.value = false
    console.error(error)
  }
}

const handleOpen = () => {
  setDialogFormVisible(true)
}

defineExpose({ handleOpen })
</script>
