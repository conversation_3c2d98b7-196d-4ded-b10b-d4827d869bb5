import request, { baseUrl } from '@/utils/request'

// 登录
export const loginApi = (data: any) => request.post(`/datamanage/login`, data)

// 登出
export const logoutApi = () => request.get(`/datamanage/logout`)

//获取用户信息
export const getSsoUserApi = () => request.get(`/datamanage/getSsoUser`)

// 获取token
export const getSsoTokenApi = (ticket: string) =>
  request.get(`/datamanage/getTokenByTicket/${ticket}`)

// sso登录地址
export const ssoLoginUrl = () =>
  `${baseUrl}/sso/login?back=${window.encodeURIComponent(window.location.href)}`
