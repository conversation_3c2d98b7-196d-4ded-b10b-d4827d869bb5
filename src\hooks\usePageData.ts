import { ElMessage } from 'element-plus'
import { reactive, ref } from 'vue'

export default function usePageData(api: any, isInit = true) {
  type Page = {
    page: number
    limit: number
    total: number
    pageSizes: Array<any>
    searchForm: any
    loading: boolean
    handleSearch: (form?: any, page?: number) => Promise<void>
    handleSizeChange(val: number): void
    handleCurrentChange(val: number): void
  }

  // 列表数据变量
  const tableData = ref<Array<any> | any>([])

  // 列表分页数据
  const pageData = reactive<Page>({
    page: 1,
    limit: 25,
    pageSizes: [25, 50, 75, 100],
    total: 0,
    searchForm: {},
    loading: false,
    async handleSearch(form, page) {
      if (form) {
        pageData.searchForm = { ...form }
      }
      if (page) {
        pageData.page = page
      }
      await getTableData()
    },
    handleSizeChange(val) {
      pageData.limit = val
      getTableData()
    },
    handleCurrentChange(val) {
      pageData.page = val
      getTableData()
    }
  })

  // 获取列表数据方法
  const getTableData = async () => {
    if (!api) {
      return
    }
    try {
      pageData.loading = true
      tableData.value = []
      const { limit, page, searchForm } = pageData
      const { data, status, message, total } = await api({ limit, page, ...searchForm })
      if ([200].includes(status)) {
        tableData.value = data || []
        pageData.total = total ? parseInt(total) : 0
      } else {
        ElMessage.error(message)
      }
      pageData.loading = false
    } catch (error) {
      tableData.value = []
      pageData.loading = false
      console.error(error)
    }
  }

  // 是否初始化
  if (isInit) {
    getTableData()
  }

  return { tableData, pageData }
}
