<template>
  <el-form ref="ruleFormRef" :model="ruleForm">
    <div class="search-content">
      <div class="search-btn">
        <template v-if="dataCatalogId">
          <el-dropdown v-if="permission.hasButton('dataCatalog_upload')" style="margin-right: 12px">
            <el-button type="primary">
              <template #icon>
                <SvgIcon name="upload" />
              </template>
              上传
              <SvgIcon style="margin-left: 10px" name="arrowDown" />
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="emit('handleSearchClick', 'handleUpload', 3)">
                  空间数据
                </el-dropdown-item>
                <el-dropdown-item @click="emit('handleSearchClick', 'handleUpload', 1)">
                  上传文件
                </el-dropdown-item>
                <el-dropdown-item @click="emit('handleSearchClick', 'handleUpload', 2)">
                  上传文件夹
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <el-button v-if="permission.hasButton('dataCatalog_dir')" type="primary"
            @click="emit('handleSearchClick', 'handleCreateFolder', ruleForm)">
            <template #icon>
              <SvgIcon name="add" />
            </template>
            新建文件夹
          </el-button>
        </template>
        <template v-else>
          <el-button v-if="permission.hasButton('dataCatalog_export')" type="primary" @click="handleExport">
            <template #icon>
              <SvgIcon name="download" />
            </template>
            导出资源
          </el-button>
        </template>
        <el-button v-if="permission.hasButton('dataCatalog_copy')" type="primary"
          @click="emit('handleSearchClick', 'handleCopyData', ruleForm)">
          <template #icon>
            <SvgIcon name="copyDocument" />
          </template>
          复制
        </el-button>
        <el-button v-if="permission.hasButton('dataCatalog_move')" type="primary"
          @click="emit('handleSearchClick', 'handleMove', ruleForm)">
          <template #icon>
            <SvgIcon name="MoveIcon" />
          </template>
          移动
        </el-button>
        <el-button v-if="permission.hasButton('dataCatalog_download')" type="primary"
          @click="emit('handleSearchClick', 'handleDownload', ruleForm)">
          <template #icon>
            <SvgIcon name="download" />
          </template>
          下载
        </el-button>
        <el-button v-if="permission.hasButton('dataCatalog_del')" type="danger"
          @click="emit('handleSearchClick', 'handleDelete', ruleForm)">
          <template #icon>
            <SvgIcon name="delete" />
          </template>
          删除
        </el-button>
      </div>
      <div class="search-input">
        <div class="search-item">
          <el-form-item prop="fileName">
            <el-input class="form-item" v-model="ruleForm.fileName" placeholder="请输入文件名称"
              @keydown.enter="emit('handleSearchClick', 'handleSearch', ruleForm)">
              <template #suffix>
                <SvgIcon @click="emit('handleSearchClick', 'handleSearch', ruleForm)" style="cursor: pointer"
                  name="search" />
              </template>
            </el-input>
          </el-form-item>
        </div>
        <div class="search-item">
          <el-form-item prop="fileTypeId">
            <el-cascader @change="emit('handleSearchClick', 'handleSearch', ruleForm)" :props="catalogProps"
              :options="globalData.catalogMenuList" style="width: 100%" v-model="ruleForm.fileTypeId" clearable
              placeholder="请选择数据类型" />
          </el-form-item>
        </div>

        <div class="search-item">
          <el-form-item prop="mobile">
            <el-date-picker @change="emit('handleSearchClick', 'handleSearch', ruleForm)" style="width: 100%"
              value-format="YYYY-MM-DD" v-model="ruleForm.dateRange" type="daterange" range-separator="至"
              start-placeholder="上传开始日期" end-placeholder="上传结束日期" clearable />
          </el-form-item>
        </div>
        <div class="item" style="margin-left: 10px">
          <el-button type="primary" @click="handleReset">
            <template #icon>
              <SvgIcon name="refresh" />
            </template>
            重置
          </el-button>
        </div>
      </div>
    </div>
  </el-form>
</template>

<script setup lang="ts">
import { computed, reactive, ref, watch } from 'vue'
import type { CascaderProps, FormInstance } from 'element-plus'
import useUserInfo from '@/store/useUserInfo'
import { useRoute } from 'vue-router'
import useGlobalData from '@/store/useGlobalData'
import { downloadFile } from '@/utils/fileUtils'
import { exportCatalogListApi } from '@/api/data_catalog'

// 权限制作
const permission = useUserInfo()

const handleExport = () => {
  downloadFile(`${exportCatalogListApi()}?token=${permission.token}`)
}

const route = useRoute()
const catalogProps: CascaderProps = {
  expandTrigger: 'hover',
  value: 'tid',
  label: 'fileTypeName',
  children: 'childVOList',
  emitPath: false,
}
const globalData = useGlobalData()

const ruleFormRef = ref<FormInstance>()
const ruleForm = reactive<any>({})

const filePath = computed<any>(() => route.query?.filePath || '/')
const dataCatalogId = computed<any>(() => route.query?.dataCatalogId)
watch(filePath, () => {
  ruleFormRef.value?.resetFields()
})

watch(dataCatalogId, () => {
  ruleFormRef.value?.resetFields()
})

const emit = defineEmits<{
  handleSearchClick: [name: string, form?: any]
}>()

// 重置表单并发射事件
const handleReset = () => {
  // 清空表单
  ruleFormRef.value?.resetFields()
  // 手动清空日期选择器的值
  ruleForm.fileName = ''
  ruleForm.fileTypeId = null
  ruleForm.dateRange = []
  // 发送重置事件
  emit('handleSearchClick', 'handleReset', ruleForm)
}
</script>

<style scoped lang="less">
.search-btn {
  margin-left: 0;

  :deep(.el-button:focus-visible) {
    outline: none;
  }
}

.search-input {
  margin-left: 15px;
  justify-content: flex-end;
  margin-right: 0px;
}

.icon {
  font-size: 18px;
  color: #d2d2d2;
  cursor: pointer;
}

.active {
  color: var(--el-color-primary);
}

.icon+.icon {
  margin-left: 10px;
}
</style>
