<template>
  <div class="searchTool">
    <div v-if="!contentShow.isShowSearch">
      <div class="search">
        <el-input placeholder="输入文件名称搜索" v-model="searchForm.fileName" clearable />
        <div class="search-btn">
          <el-button @click="handleSearch" type="primary">查询</el-button>
          <el-button @click="handleSeniorSearch(1)" type="primary">高级查询</el-button>
          <el-button @click="handleSeniorSearch(0)" type="primary">流数据查询</el-button>
        </div>
      </div>
      <el-collapse-transition>
        <TableData
          v-show="contentShow.isShowTable"
          @handle-click="handleClick"
          :table-data="tableData"
          :page-data="pageData"
          :handleExport
          :searchType="searchForm.searchType"
        />
      </el-collapse-transition>
    </div>
    <div v-show="contentShow.isShowSearch">
      <div class="search-tab">
        <ul class="header">
          <li
            v-for="p in tabList"
            :key="p.id"
            :class="{ active: [active].includes(p.id) }"
            @click="handleTab(p)"
          >
            {{ p.name }}
          </li>
        </ul>
        <SvgIcon class="svgicon" name="closeBold" @click="handleClose" />
      </div>
      <el-collapse-transition>
        <FormData
          :searchType="searchForm.searchType"
          :active
          :coordinateObj="props.coordinateObj"
          :trellisCode="props.trellisCode"
          @handle-tool="handleTool"
          @handle-submit="handleSubmit"
          @handle-reset-map="(val) => emit('handleResetMap', val)"
          v-show="!contentShow.isShowTable"
        />
      </el-collapse-transition>
      <el-collapse-transition>
        <TableData
          v-show="contentShow.isShowTable"
          @handle-click="handleClick"
          :table-data="tableData"
          :page-data="pageData"
          :handleExport
          :searchType="searchForm.searchType"
        />
      </el-collapse-transition>
    </div>
    <FileMetadata ref="FileMetadataRef" :is-edit="false" />
    <SceneMetaData ref="SceneMetaDataRef" />
    <AddEditDialog ref="AddEditDialogRef" />
  </div>
</template>

<script setup lang="ts">
import FormData from './FormData.vue'
import TableData from './TableData.vue'
import FileMetadata from '@/components/FileMetadata/index.vue'
import SceneMetaData from './SceneMetaData.vue'
import { computed, reactive, ref, watchEffect } from 'vue'
import usePageData from './usePageData'
import { ElMessage } from 'element-plus'
import { downloadFileApi } from '@/api/common'
import { downloadFile } from '@/utils/fileUtils'
import useUserInfo from '@/store/useUserInfo'
import AddEditDialog from '@/pages/DataIntegration/StreamData/AddEditDialog.vue'
import { IotServerDetailsApi } from '@/api/stream_data'

const props = defineProps<{ coordinateObj: any; trellisCode: any }>()

const userInfo = useUserInfo()

const tabList: any = computed<any[]>(() => {
  const list = [
    { id: 2, name: '经纬度' },
    { id: 3, name: '框选' },
    // { id: 4, name: 'GeoSot网格码' },
  ]
  if (searchForm.searchType) {
    return [
      { id: 1, name: '行政区划' },
      ...list,
      { id: 6, name: '文件查询' },
      { id: 5, name: '北斗网格码' },
    ]
  }
  return list
})
const active = ref<number>(1)
const handleTab = (p: any) => {
  contentShow.isShowTable = false
  active.value = p.id
  if ([4, 5].includes(active.value)) {
    props.trellisCode.initTrellisCode([4].includes(active.value))
  } else {
    props.trellisCode.clearTrelisCode()
  }
  emit('handleResetMap')
}

// 普通查询
const { pageData, tableData, handleExport } = usePageData()

const contentShow = reactive<any>({
  isShowTable: false,
  isShowSearch: false,
})

// 搜索类容
const searchForm = reactive<any>({
  fileName: '',
  searchType: 1,
})
const handleSearch = async () => {
  try {
    if (!searchForm.fileName) {
      ElMessage.warning('请输入需要搜索的内容！')
      return
    }

    const params = {
      cql_filter: `file_name like '%${searchForm.fileName}%'${userInfo.userInfo.tidList ? ` and uid in(${userInfo.userInfo.tidList.join(',')})` : ''}`,
    }
    await pageData.handleSearch(1, 1, params, 1)
    contentShow.isShowTable = true
  } catch (error) {}
}

// 表格操作
const handleClick = (name: string, row: any) => {
  switch (name) {
    case 'handleDownload':
      downloadFile(`${downloadFileApi()}?tid=${row.user_file_id}&token=${userInfo.token}`)
      return
    case 'handleDetails':
      handleDetails({ tid: row.user_file_id, scene_type: row.scene_type })
      return
    case 'handlePreview':
      emit('handlePreview', row)
      return
    case 'handleBack':
      tableData.value = []
      contentShow.isShowTable = false
      emit('handleResetMap')
      return
    default:
      break
  }
}

// 元数据详情
const SceneMetaDataRef = ref<any>()
const FileMetadataRef = ref<any>()
const AddEditDialogRef = ref<InstanceType<typeof AddEditDialog>>()
const getIotDetails = async (row: any) => {
  try {
    const { data, message, status } = await IotServerDetailsApi(row.tid)
    if (status === 200) {
      AddEditDialogRef.value?.handleOpen(3, data)
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}

const handleDetails = (row: any) => {
  if ([0].includes(searchForm.searchType)) {
    getIotDetails(row)
    return
  }
  if (row.scene_type) {
    SceneMetaDataRef.value.handleOpen(row)
    return
  }
  FileMetadataRef.value.handleOpen(row)
}

// 高级搜索
const handleSeniorSearch = (type: number) => {
  searchForm.fileName = ''
  searchForm.searchType = type
  if (type) {
    active.value = 1
  } else {
    active.value = 2
  }
  tableData.value = []
  contentShow.isShowTable = false
  contentShow.isShowSearch = true
  emit('handleResetMap')
}
const handleSubmit = async (form: any) => {
  try {
    console.log(searchForm.searchType)
    await pageData.handleSearch(searchForm.searchType, active.value, form, 1)
    contentShow.isShowTable = true
  } catch (error) {}
}

const handleClose = () => {
  active.value = 1
  tableData.value = []
  contentShow.isShowTable = false
  contentShow.isShowSearch = false
  props.trellisCode.clearTrelisCode()
  emit('handleResetMap')
}

const handleTool = (key: any) => {
  emit('handleTool', key)
}

const emit = defineEmits<{
  (e: 'handleTool', key: string): void
  (e: 'handlePreview', row: any): void
  (e: 'handleResetMap', isDel?: boolean): void
}>()

// 监控网格码查询列表展示时不给选取网格嘛
watchEffect(() => {
  if (!contentShow.isShowTable && contentShow.isShowSearch && [4, 5].includes(active.value)) {
    props.trellisCode.setIsSelectCode(true)
  } else {
    props.trellisCode.setIsSelectCode(false)
  }
})
</script>

<style scoped lang="less">
.searchTool {
  width: 476px;
  box-sizing: border-box;
  position: absolute;
  top: 30px;
  left: 30px;
  z-index: 100;
  .search {
    display: flex;
    margin-bottom: 10px;
    :deep(.el-input) {
      flex: 1;
      .el-input__wrapper {
        box-shadow: none;
        background: #0d224b;
        border: 1px solid #163671;
        &:hover {
          box-shadow: none;
        }
        .el-input__inner {
          color: #8798b7;
        }
      }
    }
    .search-btn {
      margin-left: 15px;
    }
  }
  .search-tab {
    padding: 0 20px;
    height: 34px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;
    background: #0d224b;
    margin-bottom: 10px;
    .header {
      display: flex;
      box-sizing: border-box;
      background: #0d224b;
      li {
        line-height: 34px;
        padding: 0 7px;
        font-size: 14px;
        font-weight: 400;
        color: var(--el-color-primary);
        cursor: pointer;
      }
      li.active {
        border-bottom: 2px solid var(--el-color-primary);
      }
      li + li {
        margin-left: 10px;
      }
    }
    .svgicon {
      font-size: 16px;
      color: var(--el-color-primary);
      cursor: pointer;
    }
  }

  .main-content {
    margin-top: 10px;
  }
}
</style>
