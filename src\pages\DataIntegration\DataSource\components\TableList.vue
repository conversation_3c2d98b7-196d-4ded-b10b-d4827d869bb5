<script setup lang="ts">
import SearchTool from '@/pages/DataIntegration/DataSource/components/SearchTool.vue'
import EditDetailDialog from '@/pages/DataIntegration/DataSource/components/EditDetailDialog.vue'
import usePageData from '@/hooks/usePageData'
import { ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import { queryDetailListApi, queryDbDetailListApi } from '@/api/data_source.ts'

defineProps<{ permission: boolean }>()

const route = useRoute()

const isDBDetails = ['sourceDBDetail'].includes(route.name as string)

// 搜索数据
const searchData = ref<any>({})
const handleSearch = (form: any) => {
  searchData.value = form
  initData()
}

const { pageData, tableData } = usePageData(
  isDBDetails ? queryDbDetailListApi : queryDetailListApi,
  false,
)
const initData = () => {
  const { id, dbname } = route.query
  pageData.handleSearch(
    { dataSourceId: id, tableName: isDBDetails ? dbname : undefined, ...searchData.value },
    1,
  )
}

watch(
  () => route.query.dbname,
  () => {
    if (route.query.dbname) {
      handleSearch({})
    }
  },
  {
    immediate: true,
  },
)

const EditDetailDialogRef = ref<any>()
const handleEdit = (row: any) => {
  EditDetailDialogRef.value?.handleOpen(row)
}
</script>

<template>
  <div class="TableList">
    <div class="header">
      <SearchTool :is-select="false" @handleSearch="handleSearch" />
    </div>
    <div class="main">
      <el-scrollbar>
        <ul class="main-list">
          <li v-for="p in tableData" :key="p.tid">
            <el-scrollbar>
              <span class="jsonData">
                {{ JSON.stringify(p.detail, null, 2).trim() }}
              </span>
            </el-scrollbar>
            <!-- hasButton(['sourceEsDetail_edit', 'sourceDBDetail_edit']) -->
            <el-button
              v-if="permission"
              @click="handleEdit(p)"
              class="common-icon-btn"
              type="primary"
              plain
              link
            >
              <template #icon>
                <SvgIcon name="edit" />
              </template>
            </el-button>
          </li>
        </ul>
      </el-scrollbar>
    </div>
    <div class="footer">
      <el-pagination
        v-model:currentPage="pageData.page"
        v-model:page-size="pageData.limit"
        :page-sizes="pageData.pageSizes"
        background
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageData.total"
        @size-change="pageData.handleSizeChange"
        @current-change="pageData.handleCurrentChange"
      />
    </div>
    <EditDetailDialog ref="EditDetailDialogRef" @handleRefresh="initData" />
  </div>
</template>

<style scoped lang="less">
.TableList {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  .header {
    padding: 15px;
    background-color: @withe;
  }
  .main {
    flex: 1;
    background-color: @withe;
    overflow: hidden;
    box-sizing: border-box;
    .main-list {
      padding: 10px 15px;
      display: grid;
      justify-content: space-evenly;
      grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
      grid-gap: 20px;
      li {
        height: 300px;
        box-shadow: 0 8px 8px 8px rgba(0, 0, 0, 0.05);
        border-radius: 4px 4px 4px 4px;
        padding: 20px;
        box-sizing: border-box;
        position: relative;
        .jsonData {
          text-align: left;
          padding: auto;
          white-space: pre-wrap;
        }
        .common-icon-btn {
          position: absolute;
          right: 15px;
          top: 15px;
        }
      }
    }
  }
  .footer {
    background-color: @withe;
    box-sizing: border-box;
    padding: 15px;
    display: flex;
    justify-content: flex-end;
  }
}
</style>
