<template>
  <ElDialog
    v-model="dialogFormVisible"
    title="新建算子"
    top="10vh"
    width="850px"
    :close-on-click-modal="false"
    @close="handleDialogClose"
  >
    <ElForm
      ref="formRef"
      :disabled="isEditDisable"
      :model="formData"
      :rules="rules"
      label-width="140px"
    >
      <ElRow :gutter="20">
        <ElCol :span="12">
          <ElFormItem label="算子名称" prop="nodeName">
            <ElInput v-model="formData.nodeName" placeholder="请输入算子名称" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="算子类型" prop="ptid">
            <ElCascader
              style="width: 100%"
              v-model="formData.ptid"
              :props="{ label: 'nodeName', value: 'tid', children: 'childVOList', emitPath: false }"
              :options="treeData"
              :show-all-levels="false"
            />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="算子上游连接关系" prop="fromNodeIdList">
            <ElTreeSelect
              v-model="formData.fromNodeIdList"
              :data="dataSource"
              multiple
              show-checkbox
              collapse-tags
              collapse-tags-tooltip
              :props="{ ...propsTree, disabled: 'disabled' }"
              value-key="tid"
              style="width: 100%"
            />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="算子下游连接关系" prop="toNodeIdList">
            <ElTreeSelect
              v-model="formData.toNodeIdList"
              :data="dataSource"
              multiple
              show-checkbox
              collapse-tags
              collapse-tags-tooltip
              :props="{ ...propsTree, disabled: 'disabled' }"
              value-key="tid"
              style="width: 100%"
            />
          </ElFormItem>
        </ElCol>
        <ElCol :span="24">
          <ElFormItem label="算子图标" prop="icon">
            <ElUpload
              class="avatar-uploader"
              name="multipartFile"
              :accept="'.png,.jpg,.jpeg,.svg,.webp'"
              :action="uploadIconToBase64Api()"
              :show-file-list="false"
              :headers="{
                token: permission.token,
              }"
              :on-success="handleIconUpload"
            >
              <img
                v-if="formData.icon"
                :src="`data:image/jpeg;base64,${formData.icon}`"
                class="avatar"
              />
              <el-icon v-else class="avatar-uploader-icon">
                <SvgIcon name="add" />
              </el-icon>
            </ElUpload>
          </ElFormItem>
        </ElCol>
        <ElCol :span="24">
          <ElFormItem label="算子jar包" prop="userFileId">
            <div class="uplaod-demo">
              <ElUpload
                ref="fileUploadRef"
                class="upload-btn"
                v-model:file-list="formData.fileList"
                :accept="'.jar'"
                :action="uploadCustomNodeApi()"
                :limit="1"
                :headers="{
                  token: permission.token,
                }"
                :data="setData"
                :on-success="handleJarUpload"
              >
                <el-button type="primary">
                  <template #icon>
                    <SvgIcon name="upload" />
                  </template>
                  上 传
                </el-button>
              </ElUpload>
              <ElButton @click="handleDownload" class="download-btn" type="primary" link text
                >下载模板</ElButton
              >
            </div>
          </ElFormItem>
        </ElCol>
        <ElCol :span="24">
          <ElFormItem label="算子描述" prop="notes">
            <ElInput
              v-model="formData.notes"
              :rows="3"
              type="textarea"
              placeholder="请输入算子描述"
            />
          </ElFormItem>
        </ElCol>
      </ElRow>
    </ElForm>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="setDialogFormVisible(false)">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </span>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
import {
  ElCol,
  ElDialog,
  ElForm,
  ElFormItem,
  ElRow,
  ElInput,
  ElUpload,
  ElCascader,
  ElButton,
  UploadRawFile,
  ElMessage,
  ElLoading,
} from 'element-plus'
import useDialogForm from '@/hooks/useDialogForm'
import {
  queryCustomTreeTypeListApi,
  uploadCustomNodeApi,
  uploadIconToBase64Api,
  customAddApi,
  customModifyByIdApi,
  queryByIdCustomByIdApi,
  downloadOperaApi,
} from '@/api/online_model'
import { reactive, ref } from 'vue'
import useUserInfo from '@/store/useUserInfo'
import SparkMD5 from 'spark-md5'
import useTreeData from '@/pages/OnlineModel/hooks/useTreeData'

const permission = useUserInfo()

const formData = ref<any>({ fromNodeIdList: [], toNodeIdList: [] })
const rules = reactive<any>({
  ptid: [{ required: true, message: '算子类型必填', trigger: 'blur' }],
  nodeName: [{ required: true, message: '算子名称必填', trigger: 'blur' }],
  nodeCode: [{ required: true, message: '算子编码必填', trigger: 'blur' }],
  userFileId: [{ required: true, message: '请上传jar包', trigger: 'blur' }],
  icon: [{ required: true, message: '请上传图标', trigger: 'blur' }],
  fromNodeIdList: [{ required: true, message: '算子上游连接关系必填', trigger: 'blur' }],
  toNodeIdList: [{ required: true, message: '算子下游连接关系必填', trigger: 'blur' }],
})

const { propsTree, dataSource } = useTreeData()

const { dialogFormVisible, formRef, handleDialogClose, setDialogFormVisible, validateForm } =
  useDialogForm()

// 下载
const handleDownload = async () => {
  const loading = ElLoading.service({
    text: '正在下载模板，请耐心等待！',
    background: 'rgba(0, 0, 0, 0.8)',
  })
  try {
    const res: any = await downloadOperaApi()
    const url = URL.createObjectURL(new Blob([res.data], { type: 'application/zip' }))
    const link = document.createElement('a')
    link.style.display = 'none'
    link.href = url
    link.setAttribute('download', '模板.zip')
    document.body.appendChild(link)
    link.click()
    link.remove()
    loading.close()
  } catch (error) {
    loading.close()
  }
}

// icon图片处理
const handleIconUpload = (response: any) => {
  if ([200].includes(response.status)) {
    formData.value.icon = response.data.base64
  }
}

// 上传jar逻辑处理
const fileUploadRef = ref<any>()
const getFileMd5 = (file: any) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onloadend = () => {
      const arrayBuffer: any = reader.result
      // 生成MD5值
      const spark = new SparkMD5.ArrayBuffer()
      spark.append(arrayBuffer)
      const md5Hash = spark.end()
      resolve(md5Hash)
    }
    reader.onerror = () => {
      reject(new Error('无法读取文件对象'))
    }
    reader.readAsArrayBuffer(file)
  })
}
const setData = async (rawFile: UploadRawFile) => {
  const identifier = await getFileMd5(rawFile)
  return {
    identifier,
    fileName: rawFile.name,
    totalSize: rawFile.size,
    currentChunkSize: rawFile.size,
    chunkSize: rawFile.size,
    totalChunks: 1,
    chunkNumber: 1,
  }
}
const handleJarUpload = (response: any) => {
  if ([200].includes(response.status) && [1].includes(response.data.uploadCode)) {
    formData.value.userFileId = response.data.userFileId
    ElMessage.success('上传成功！')
  } else {
    fileUploadRef.value?.clearFiles()
    ElMessage.error(response.message || '上传失败！')
  }
}

// 获取数据类型
const treeData = ref<any>([])
const getTreeTypeList = async () => {
  try {
    const { data, status } = await queryCustomTreeTypeListApi()
    if ([200].includes(status)) {
      treeData.value = data
    }
  } catch (error) {}
}
getTreeTypeList()

const isEditDisable = ref<boolean>(false)
const handleOpen = async (row: any, isEdit: boolean) => {
  isEditDisable.value = !isEdit
  if (row) {
    const { data, status, message } = await queryByIdCustomByIdApi(row)
    if ([200].includes(status)) {
      formData.value = {
        ...data,
        fileList: data.fileName
          ? [
              {
                name: `${data.fileName}.${data.suffix}`,
                url: '',
              },
            ]
          : [],
      }
      setDialogFormVisible(true)
    } else {
      ElMessage.error(message)
    }
  } else {
    formData.value = { fileList: [], fromNodeIdList: [], toNodeIdList: [] }
    setDialogFormVisible(true)
  }
}

const handleSubmit = async () => {
  try {
    await validateForm()
    delete formData.value.fileList
    const { message, status } = formData.value.tid
      ? await customModifyByIdApi(formData.value)
      : await customAddApi(formData.value)
    if ([200].includes(status)) {
      ElMessage.success(message)
      emit('handleRefresh')
      setDialogFormVisible(false)
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}

const emit = defineEmits<{
  (e: 'handleRefresh'): void
}>()
defineExpose({ handleOpen })
</script>

<style lang="less" scoped>
.avatar-uploader .avatar {
  width: 80px;
  height: 80px;
  display: block;
}
.uplaod-demo {
  width: 100%;
  position: relative;
  .upload-btn {
    width: 100%;
  }
  .download-btn {
    position: absolute;
    top: 0;
    left: 100px;
    line-height: 32px;
  }
}
</style>

<style>
.avatar-uploader .el-upload {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.el-icon.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 80px;
  height: 80px;
  text-align: center;
}
</style>
