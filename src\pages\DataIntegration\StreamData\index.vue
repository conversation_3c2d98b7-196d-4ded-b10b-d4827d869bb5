<template>
  <MainLayout>
    <template #header>
      <SearchTool
        :dictData
        @handle-search="handleSearch"
        @handle-create="AddEditDialogRef?.handleOpen(1)"
      />
    </template>
    <div class="tableList">
      <el-table
        v-loading="pageData.loading"
        header-cell-class-name="common-table-header"
        cell-class-name="common-table-cell"
        ref="multipleTableRef"
        :data="tableData"
        height="100%"
        style="width: 100%"
        row-key="tid"
      >
        <el-table-column property="serverName" label="服务名称" show-overflow-tooltip />
        <el-table-column property="isSchedule" label="是否定时执行">
          <template #default="scope">
            {{ scope.row.isSchedule ? '是' : '否' }}
          </template>
        </el-table-column>
        <el-table-column property="execTime" label="执行时间" show-overflow-tooltip />
        <el-table-column property="isRunningDesc" label="状态" show-overflow-tooltip />
        <el-table-column property="describe" label="描述" show-overflow-tooltip />
        <el-table-column label="操作" width="220" fixed="right">
          <template #default="scope">
            <el-button
              v-if="!scope.row.isRunning && permission.hasButton('streamData_start')"
              @click="handleStart(scope.row.tid)"
              title="开始执行"
              class="common-icon-btn"
              type="primary"
              plain
              link
            >
              <template #icon>
                <SvgIcon name="videoPlay" />
              </template>
            </el-button>
            <el-button
              v-if="scope.row.isRunning && permission.hasButton('streamData_stop')"
              @click="handleStop(scope.row.tid)"
              title="暂停执行"
              class="common-icon-btn"
              type="primary"
              plain
              link
            >
              <template #icon>
                <SvgIcon name="videoPause" />
              </template>
            </el-button>
            <el-button
              v-if="permission.hasButton('streamData_see')"
              @click="PreviewMapRef?.handleOpenPreview(scope.row)"
              class="common-icon-btn"
              type="primary"
              plain
              link
              title="预览"
            >
              <template #icon>
                <SvgIcon name="view" />
              </template>
            </el-button>
            <el-button
              v-if="scope.row.isRunning && permission.hasButton('streamData_adddress')"
              @click="ServeAddressRef?.handleOpen(scope.row.serverUrls)"
              class="common-icon-btn"
              type="primary"
              plain
              link
              title="服务地址"
            >
              <template #icon>
                <SvgIcon name="address" />
              </template>
            </el-button>
            <el-button
              v-if="permission.hasButton('streamData_edit')"
              @click="AddEditDialogRef?.handleOpen(2, scope.row)"
              class="common-icon-btn"
              type="primary"
              plain
              link
            >
              <template #icon>
                <SvgIcon name="edit" />
              </template>
            </el-button>
            <el-button
              v-if="permission.hasButton('streamData_details')"
              @click="AddEditDialogRef?.handleOpen(3, scope.row)"
              class="common-icon-btn"
              type="primary"
              plain
              link
            >
              <template #icon>
                <SvgIcon name="details" />
              </template>
            </el-button>
            <el-button
              v-if="permission.hasButton('streamData_del')"
              @click="handleDel(scope.row.tid)"
              class="common-icon-btn"
              type="danger"
              plain
              link
            >
              <template #icon>
                <SvgIcon name="delete" />
              </template>
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <template #footer>
      <el-pagination
        v-model:currentPage="pageData.page"
        v-model:page-size="pageData.limit"
        :page-sizes="pageData.pageSizes"
        background
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageData.total"
        @size-change="pageData.handleSizeChange"
        @current-change="pageData.handleCurrentChange"
      />
    </template>
    <AddEditDialog @handleRefresh="pageData.handleSearch(null, 1)" ref="AddEditDialogRef" />
    <ServeAddress ref="ServeAddressRef" />
    <PreviewMap ref="PreviewMapRef" />
  </MainLayout>
</template>

<script setup lang="ts">
import MainLayout from '@/components/MainLayout/index.vue'
import SearchTool from './SearchTool.vue'
import usePageData from '@/hooks/usePageData'
import { ElButton, ElMessage, ElMessageBox } from 'element-plus'
import { ref } from 'vue'
import AddEditDialog from './AddEditDialog.vue'
import {
  IotlistApi,
  IotDictApi,
  IotDelIdApi,
  IotServerStartApi,
  IotServerStopApi,
} from '@/api/stream_data'
import ServeAddress from './ServeAddress.vue'
import PreviewMap from './PreviewMap.vue'
import useUserInfo from '@/store/useUserInfo'

const permission = useUserInfo()

const ServeAddressRef = ref<InstanceType<typeof ServeAddress>>()

const PreviewMapRef = ref<InstanceType<typeof PreviewMap>>()

const dictData = ref<any>({})
const getDictData = async () => {
  try {
    const { data, status, message } = await IotDictApi()
    if (status === 200) {
      dictData.value = data
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}

getDictData()

const { tableData, pageData } = usePageData(IotlistApi)

const AddEditDialogRef = ref<InstanceType<typeof AddEditDialog>>()

const handleSearch = (from: any) => {
  pageData.handleSearch(from, 1)
}

const handleStart = (id: any) => {
  ElMessageBox.confirm('是否确定要启动服务？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    try {
      const { message, status } = await IotServerStartApi(id)
      if ([200].includes(status)) {
        ElMessage.success(message || '操作成功！')
        pageData.handleSearch(null, 1)
      } else {
        ElMessage.error(message || '操作失败！')
      }
    } catch (error) {}
  })
}

const handleStop = (id: any) => {
  ElMessageBox.confirm('是否确定要停止服务？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    try {
      const { message, status } = await IotServerStopApi(id)
      if ([200].includes(status)) {
        ElMessage.success(message || '操作成功！')
        pageData.handleSearch(null, 1)
      } else {
        ElMessage.error(message || '操作失败！')
      }
    } catch (error) {}
  })
}

const handleDel = (id: any) => {
  ElMessageBox.confirm('是否确定要删除该数据？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    try {
      const { message, status } = await IotDelIdApi(id)
      if ([200].includes(status)) {
        ElMessage.success(message)
        pageData.handleSearch(null, 1)
      } else {
        ElMessage.error(message)
      }
    } catch (error) {}
  })
}
</script>

<style lang="less" scoped>
.tableContent {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  .btnIcon {
    text-align: right;
    .icon {
      font-size: 20px;
    }
  }
  .tableList {
    margin-top: 10px;
    flex: 1;
    box-sizing: border-box;
    overflow: hidden;
    .tableGrid {
      display: grid;
      justify-content: space-evenly;
      grid-template-columns: repeat(auto-fill, minmax(230px, 1fr));
      grid-gap: 15px 10px;
      .cardItem {
        padding: 5px;
        box-sizing: border-box;
        background-color: @withe;
        border-radius: 4px 4px 4px 4px;
        box-shadow: 0px 4px 4px 4px rgba(0, 0, 0, 0.05);
        border: 1px solid rgba(0, 0, 0, 0.05);
        .img {
          height: 135px;
          background-size: cover;
          background-position: center;
          background-repeat: no-repeat;
          display: flex;
          justify-content: flex-end;
          position: relative;
          cursor: pointer;
          .see {
            transition: all 0.5s ease;
            position: absolute;
            left: 0;
            top: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            font-size: 32px;
            color: @withe;
            text-align: center;
            line-height: 135px;
            opacity: 0;
          }
          &:hover {
            .see {
              opacity: 1;
            }
          }
        }
        .title {
          margin: 10px;
          border-bottom: 1px solid #dbdbdb;
          padding-bottom: 10px;
          p {
            font-size: 14px;
            font-weight: 400;
            color: #333333;
            line-height: 24px;
          }
          span {
            font-size: 12px;
            font-weight: 400;
            color: #999999;
          }
        }
        .icons {
          margin: 0 10px 0 5px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          .common-icon-btn {
            font-size: 18px !important;
          }
          .common-icon-btn + .common-icon-btn {
            margin-left: 0px !important;
          }
          .createVideo {
            width: 66px;
            height: 16px;
            border-radius: 8px 8px 8px 8px;
            border: 1px solid var(--el-color-primary);
            font-size: 12px;
            font-weight: 400;
            color: var(--el-color-primary);
            cursor: pointer;
            text-align: center;
            list-style: 16px;
          }
        }
      }
    }
  }
}
</style>
