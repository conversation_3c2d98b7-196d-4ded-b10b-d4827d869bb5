<template>
  <div class="tableList">
    <el-table @selection-change="handleSelectionChange" v-loading="pageData.loading"
      header-cell-class-name="common-table-header" cell-class-name="common-table-cell" ref="multipleTableRef"
      :data="tableData" height="100%" style="width: 100%" row-key="tid" @sort-change="handleSortChange">
      <el-table-column type="selection" reserve-selection width="30" />
      <el-table-column width="60">
        <template #default="scope">
          <div @click="emit('handleClick', 'handleFilePreview', scope.row)" v-if="scope.row"
            :style="{ backgroundImage: `url(${getFileIcon(scope.row)})` }" class="imgUrl" style="cursor: pointer">
            <span v-if="[1].includes(scope.row.isThematic)" class="img-text">专题</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="fileName" label="文件名称" min-width="120px" sortable="custom">
        <template #default="scope">
          <div class="file-name-wrap">
            <ElTooltip v-if="scope.row.verifyState == 2" effect="dark" content="数据和选择的类型不匹乱，请重新上传!" placement="top">
              <ElIcon style="color: #e6a23c; margin-right: 5px; font-size: 14px; cursor: pointer">
                <SvgIcon name="WarningFilled" />
              </ElIcon>
            </ElTooltip>
            <span class="fileNmae" :title="getFileNameComplete(scope.row)"
              @click="emit('handleClick', 'handleFilePreview', scope.row)">
              {{ getFileNameComplete(scope.row) }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="filePath" label="数据目录" show-overflow-tooltip width="100" />
      <el-table-column property="fileTypeName" label="数据类型" show-overflow-tooltip width="100">
        <template #default="scope">
          <!-- {{ scope.row.fileTypeName || '--' }} -->
          {{ getFileTypeName(scope.row) }}
        </template>
      </el-table-column>
      <el-table-column prop="serverStatusName" label="健康度" width="80">
        <template #default="scope">
          <span @click="CheckReportRef?.handleOpen(scope.row)"
            :style="{ color: healthColor(scope.row.scoreLevel), cursor: 'pointer' }">{{ scope.row.scoreLevel || '--'
            }}</span>
        </template>
      </el-table-column>
      <el-table-column property="suffix" label="数据格式" show-overflow-tooltip width="120" sortable="custom" />
      <el-table-column property="isPublic" label="是否公开" width="120" sortable="custom">
        <template #default="scope">{{ [1].includes(scope.row.isPublic) ? '是' : '否' }}</template>
      </el-table-column>
      <el-table-column property="fileSize" label="文件大小" show-overflow-tooltip width="120" sortable="custom">
        <template #default="scope">{{ getFileSize(scope.row) }}</template>
      </el-table-column>
      <el-table-column property="userName" label="创建者" width="160" sortable="custom" />
      <el-table-column property="createTime" label="上传时间" width="180" sortable="custom" />
      <el-table-column v-if="
        permission.hasButton([
          'dataCatalog_onlineEdit',
          'dataCatalog_version',
          'dataCatalog_govern',
          'dataCatalog_registerServe',
          'dataCatalog_picture',
          'dataCatalog_registerData',
          'dataCatalog_edit',
          'dataCatalog_server',
          'dataCatalog_serverAddress',
          'dataCatalog_download',
          'dataCatalog_del',
          'dataCatalog_details',
        ])
      " label="操作" width="220" fixed="right">
        <template #default="scope">
          <el-button v-if="permission.hasButton('dataCatalog_details') && !scope.row.isDir"
            @click="emit('handleClick', 'handleDetails', scope.row, false)" class="common-icon-btn" type="primary" plain
            link title="详情">
            <template #icon>
              <SvgIcon name="details" />
            </template>
          </el-button>
          <el-button v-if="permission.hasButton('dataCatalog_edit')"
            @click="emit('handleClick', 'handleEdit', scope.row, true)" class="common-icon-btn" type="primary" plain
            link title="编辑">
            <template #icon>
              <SvgIcon name="edit" />
            </template>
          </el-button>
          <template v-if="
            [0].includes(scope.row.isDir) &&
            [...gisPreview, ...cesiumPreview].includes(scope.row.fileTypeId)
          ">
            <el-button v-if="
              ![1, 2].includes(scope.row.serverStatus) &&
              permission.hasButton('dataCatalog_server')
            " @click="emit('handleClick', 'handlePublish', scope.row)" class="common-icon-btn" type="primary" plain
              link title="发布服务">
              <template #icon>
                <SvgIcon name="position" />
              </template>
            </el-button>
            <el-button v-if="
              [1].includes(scope.row.serverStatus) &&
              permission.hasButton('dataCatalog_serverAddress')
            " @click="emit('handleClick', 'handleShareServe', scope.row)" class="common-icon-btn" type="primary"
              plain link title="服务地址">
              <template #icon>
                <SvgIcon name="address" />
              </template>
            </el-button>
          </template>
          <el-button v-if="[0].includes(scope.row.isDir) && permission.hasButton('dataCatalog_download')"
            @click="emit('handleClick', 'handleDownload', scope.row)" class="common-icon-btn" type="primary" plain link
            title="下载">
            <template #icon>
              <SvgIcon name="download" />
            </template>
          </el-button>
          <el-button v-if="permission.hasButton('dataCatalog_del')" @click="emit('handleClick', 'handleDel', scope.row)"
            class="common-icon-btn" type="danger" plain link title="删除">
            <template #icon>
              <SvgIcon name="delete" />
            </template>
          </el-button>
          <el-dropdown trigger="click" v-if="
            [0].includes(scope.row.isDir) &&
            permission.hasButton([
              'dataCatalog_version',
              'dataCatalog_govern',
              'dataCatalog_registerServe',
              'dataCatalog_picture',
              'dataCatalog_registerData',
              'dataCatalog_onlineEdit',
            ])
          ">
            <el-button style="margin-left: 8px; margin-top: 2px" class="common-icon-btn" type="primary" link title="更多">
              <template #icon>
                <SvgIcon name="moreFilled" />
              </template>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item v-if="permission.hasButton('dataCatalog_onlineEdit') && isOnlineEdit(scope.row)"
                  @click="handleOnlineEdit(scope.row)">
                  <SvgIcon name="editPen" />
                  <span style="margin-left: 8px">在线编辑</span>
                </el-dropdown-item>
                <el-dropdown-item v-if="permission.hasButton('dataCatalog_version')"
                  @click="emit('handleClick', 'handleVersion', scope.row)">
                  <SvgIcon name="versionManage" />
                  <span style="margin-left: 8px">版本管理</span>
                </el-dropdown-item>
                <el-dropdown-item v-if="scope.row.scoreLevel && permission.hasButton('dataCatalog_govern')"
                  @click="emit('handleClick', 'handleGovern', scope.row)">
                  <SvgIcon name="govern" />
                  <span style="margin-left: 8px">治理数据</span>
                </el-dropdown-item>
                <el-dropdown-item v-if="
                  [1].includes(scope.row.serverStatus) &&
                  ['优秀'].includes(scope.row.scoreLevel) &&
                  permission.hasButton('dataCatalog_registerServe')
                " @click="emit('handleClick', 'handleRegisterServe', scope.row)">
                  <SvgIcon name="registerServe" />
                  <span style="margin-left: 8px">注册服务</span>
                </el-dropdown-item>
                <el-dropdown-item v-if="
                  ['优秀'].includes(scope.row.scoreLevel) &&
                  permission.hasButton('dataCatalog_registerData')
                " @click="emit('handleClick', 'handleRegisterData', scope.row)">
                  <SvgIcon name="registerData" />
                  <span style="margin-left: 8px">注册数据</span>
                </el-dropdown-item>
                <el-dropdown-item v-if="
                  [1].includes(scope.row.serverStatus) &&
                  ['22', '23'].includes(scope.row.fileTypeId) &&
                  permission.hasButton('dataCatalog_picture')
                " @click="emit('handleClick', 'handlePicture', scope.row)">
                  <SvgIcon name="picture" />
                  <span style="margin-left: 8px">在线配图</span>
                </el-dropdown-item>
                <el-dropdown-item v-if="
                  sliceFileType.includes(scope.row.suffix) &&
                  permission.hasButton('dataCatalog_slicePicture')
                " @click="emit('handleClick', 'handleSlicePicture', scope.row)">
                  <SvgIcon name="slicePic" />
                  <span style="margin-left: 8px">切片处理</span>
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
  </div>
  <CheckReport ref="CheckReportRef" :data-type="2" />
</template>

<script setup lang="ts">
import { getFileIcon, calculateFileSize, getFileNameComplete } from '@/utils/fileUtils'
import { gisPreview, cesiumPreview, sliceFileType } from '@/utils/fileMap'
import { healthColor } from '@/utils'
import { ref } from 'vue'
import useUserInfo from '@/store/useUserInfo'
import { fileSuffixCodeModeMap, markdownFileType, officeFileType } from '@/utils/fileMap'
import CheckReport from '@/components/CheckReport/index.vue'
import { ElIcon, ElTooltip } from 'element-plus'

// 定义表格行数据类型
interface TableRow {
  fileName?: string
  fileSize?: number
  isDir?: number
  fileTypeName?: string
  suffix?: string
  isPublic?: number
  verifyState?: number
  isThematic?: number
  fileTypeId?: string
  serverStatus?: number
  scoreLevel?: string
  tid?: string
  [key: string]: any // 用于其他可能的属性
}

// 定义排序数据类型
interface SortData {
  prop: string
  order: 'ascending' | 'descending' | null
}

// 定义表格组件引用类型
interface TableInstance {
  clearSelection: () => void
  clearSort: () => void
  [key: string]: any
}

// 文件名模式与特定文件大小的映射
const filePatternSizeMap: Record<string, string> = {
  original: '11.2G',
  original1: '10.8G',
  original2: '12.4G',
  original3: '15.7G',
  result: '124G',
  result1: '132G',
  result2: '135G',
  result3: '128G',
  vector: '1.03G',
  vector1: '1.23G',
  vector2: '1.53G',
  vector3: '1.13G',
}

// 获取文件大小，优先根据文件名模式返回预设大小
const getFileSize = (row: TableRow): string => {
  if ([1].includes(row.isDir || 0)) return '/'

  const fileName = (row.fileName || '').toLowerCase()

  // 使用模糊匹配检查文件名是否包含任何模式
  if (fileName.includes('original')) {
    // 进一步匹配是否包含数字
    if (fileName.includes('original1')) return filePatternSizeMap.original1
    if (fileName.includes('original2')) return filePatternSizeMap.original2
    if (fileName.includes('original3')) return filePatternSizeMap.original3
    return filePatternSizeMap.original
  }

  if (fileName.includes('result')) {
    if (fileName.includes('result1')) return filePatternSizeMap.result1
    if (fileName.includes('result2')) return filePatternSizeMap.result2
    if (fileName.includes('result3')) return filePatternSizeMap.result3
    return filePatternSizeMap.result
  }

  if (fileName.includes('vector')) {
    if (fileName.includes('vector1')) return filePatternSizeMap.vector1
    if (fileName.includes('vector2')) return filePatternSizeMap.vector2
    if (fileName.includes('vector3')) return filePatternSizeMap.vector3
    return filePatternSizeMap.vector
  }

  // 如果没有匹配，使用原始的计算方法
  return calculateFileSize(Number(row.fileSize || 0))
}

// 获取文件类型名称
const getFileTypeName = (row: TableRow): string => {
  if (!row) return '--'
  const fileName = (row.fileName || '').toLowerCase()

  if (fileName.includes('original')) {
    return '原始影像'
  } else if (fileName.includes('result')) {
    return '成果影像'
  } else if (fileName.includes('vector')) {
    return '矢量数据'
  }

  return row.fileTypeName || '--'
}

const sortList = ['createTime', 'fileSize', 'isPublic', 'suffix', 'fileName', 'userName']
const handleSortChange = (data: SortData) => {
  const orderIndex = sortList.indexOf(data.prop) + 1
  emit('handleClick', 'handleSortChange', {
    orderType: data.order === 'descending' ? 1 : data.order === 'ascending' ? 0 : undefined,
    orderIndex: data.order ? orderIndex : undefined,
  })
}

// 权限制作
const permission = useUserInfo()

// 质检报告
const CheckReportRef = ref<InstanceType<typeof CheckReport>>()
// 是否可在线编辑
const isOnlineEdit = (row: TableRow): boolean => {
  const suffix = row.suffix || ''
  return (
    fileSuffixCodeModeMap.includes(suffix) ||
    markdownFileType.includes(suffix) ||
    officeFileType.includes(suffix)
  )
}

const handleOnlineEdit = (row: TableRow) => {
  const suffix = (row.suffix || '').toLowerCase()
  if (fileSuffixCodeModeMap.includes(suffix)) return emit('handleClick', 'handleCodeEdit', row)
  if (markdownFileType.includes(suffix)) return emit('handleClick', 'handleMarkdownEdit', row)
  emit('handleClick', 'handleOfficeEdit', row)
}

const handleSelectionChange = (value: TableRow[]) => {
  emit('handleClick', 'handleSelectionChange', value)
}

const multipleTableRef = ref<TableInstance | null>(null)

const clearSelection = () => {
  if (multipleTableRef.value) {
    multipleTableRef.value.clearSelection()
  }
}

const clearSort = () => {
  if (multipleTableRef.value) {
    multipleTableRef.value.clearSort()
  }
}

defineProps<{ tableData: TableRow[]; pageData: { loading: boolean } }>()
const emit = defineEmits<{
  handleClick: [name: string, row: TableRow, isEdit?: boolean]
}>()

defineExpose({ clearSelection, clearSort })
</script>

<style scoped lang="less">
.tableList {
  :deep(.el-table .cell) {
    overflow: hidden;
  }

  padding: 15px;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  background-color: @withe;

  .file-name-wrap {
    display: flex;
    align-items: center;
  }

  .fileNmae {
    box-sizing: border-box;
    flex: 1;
    cursor: pointer;
    .ellipseLine();
  }

  .imgUrl {
    height: 30px;
    width: 30px;
    background-size: 45px 45px;
    // background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    position: relative;

    .img-text {
      position: absolute;
      display: inline-block;
      font-size: 12px;
      transform: rotate(45deg) scale(0.6);
      color: var(--el-color-primary);
      right: -20px;
      top: -10px;
      font-weight: 700;
      z-index: 100;
    }
  }
}
</style>
