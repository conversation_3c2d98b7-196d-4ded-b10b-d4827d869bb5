<template>
  <el-menu-item
    v-if="!props.meunItem.children || props.meunItem.children.length <= 0"
    :index="props.meunItem.path"
  >
    <!-- <SvgIcon v-if="props.meunItem.icon" class="icon-svg" :name="props.meunItem.icon" /> -->
    <template #title>
      <span>{{ props.meunItem.name }}</span>
    </template>
  </el-menu-item>
  <el-sub-menu v-else :index="props.meunItem.path">
    <template #title>
      <!-- <SvgIcon v-if="props.meunItem.icon" class="icon-svg" :name="props.meunItem.icon" /> -->
      <span>{{ props.meunItem.name }}</span>
    </template>
    <SidebarItem v-for="item in props.meunItem.children" :key="item.path" :meun-item="item" />
  </el-sub-menu>
</template>

<script setup lang="ts">
interface Props {
  meunItem: any
}

const props = defineProps<Props>()
</script>

<style scoped></style>
