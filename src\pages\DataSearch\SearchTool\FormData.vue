<template>
  <div class="formData">
    <div class="search">
      <el-scrollbar height="100%">
        <el-form :model="formData" class="search-form">
          <template v-if="[1].includes(props.active)">
            <div class="search-title">
              <img :src="icon" alt="图标" />
              <p>基本信息</p>
            </div>
            <el-form-item prop="country" label-width="10px">
              <el-select :disabled="true" v-model="formData.country" placeholder="请选择活动区域">
                <el-option label="中国" value=""></el-option>
              </el-select>
            </el-form-item>
            <el-form-item prop="province" label-width="10px">
              <el-select
                popper-class="dataMap-select"
                v-model="formData.province"
                placeholder="请选择省级行政区"
                @change="handleSelectProvince"
              >
                <el-option
                  v-for="p in provinceOptions"
                  :key="p.admincode"
                  :label="p.codeName"
                  :value="p.admincode"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item prop="city" label-width="10px">
              <el-select
                popper-class="dataMap-select"
                v-model="formData.city"
                placeholder="请选择地市"
              >
                <el-option
                  v-for="p in cityOptions"
                  :key="p.admincode"
                  :label="p.codeName"
                  :value="p.admincode"
                ></el-option>
              </el-select>
            </el-form-item>
          </template>
          <template v-if="[2].includes(props.active)">
            <div class="search-title">
              <img :src="icon" alt="图标" />
              <p>基本信息</p>
            </div>
            <el-row>
              <el-col :span="13" class="long-input">
                <el-form-item label-width="65px" label="左上角" prop="leftLong">
                  <el-input placeholder="请输入内容" v-model="formData.leftLong">
                    <template #prepend>经度</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="11" class="long-input">
                <el-form-item label-width="30px" prop="leftLat">
                  <el-input placeholder="请输入内容" v-model="formData.leftLat">
                    <template #prepend>纬度</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="13" class="long-input">
                <el-form-item label-width="65px" label="右下角" prop="rightLong">
                  <el-input placeholder="请输入内容" v-model="formData.rightLong">
                    <template #prepend>经度</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="11" class="long-input">
                <el-form-item label-width="30px" prop="rightLat">
                  <el-input placeholder="请输入内容" v-model="formData.rightLat">
                    <template #prepend>纬度</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </template>
          <template v-if="[3].includes(props.active)">
            <div class="search-title">
              <img :src="icon" alt="图标" />
              <p>选择图形</p>
            </div>
            <el-form-item label-width="30px" prop="region">
              <img
                v-for="p in boxImg"
                :key="p.id"
                class="box-select"
                width="40"
                @click="handleSelectBox(p)"
                :src="[imgActive].includes(p.id) ? p.imgActive : p.img"
                alt="png"
              />
            </el-form-item>
          </template>
          <template v-if="[6].includes(props.active)">
            <div class="search-title">
              <img :src="icon" alt="图标" />
              <p>文件上传</p>
            </div>
            <ElFormItem label-width="30px" prop="file">
              <ElUpload
                class="upload-demo"
                :action="vectorTowktApi()"
                :accept="'.zip,.geojson,.shp'"
                :show-file-list="false"
                :headers="{
                  token: permission.token,
                }"
                :on-success="handleAvatarSuccess"
              >
                <ElIcon class="el-icon--upload"><SvgIcon name="UploadFilled" /></ElIcon>
                <div class="el-upload__text">
                  <ElText type="primary">点击上传</ElText>
                </div>
                <template #tip>
                  <span class="el-upload_tips"> 支持geojson,zip,shp<br /> </span>
                </template>
              </ElUpload>
            </ElFormItem>
          </template>
          <template v-if="[4, 5].includes(props.active)">
            <div class="search-title">
              <img :src="icon" alt="图标" />
              <p>北斗网格码</p>
            </div>
            <el-form-item label-width="10px">
              <el-input v-model="formData.code" readonly placeholder="请选择网格码"></el-input>
            </el-form-item>
          </template>
          <template v-if="props.searchType">
            <div class="search-title">
              <img :src="icon" alt="图标" />
              <p>数据类型</p>
            </div>
            <el-form-item label-width="10px">
              <el-button
                :class="{ btnActive: (formData.file_type_id || []).includes(p.tid) }"
                v-for="p in typeOptions"
                :key="p.tid"
                @click="handleSelectFileType(p)"
                >{{ p.fileTypeName }}</el-button
              >
            </el-form-item>
          </template>
          <div class="search-title">
            <img :src="icon" alt="图标" />
            <p>更多查询</p>
          </div>
          <el-form-item label-width="80px" label="采集时间">
            <el-date-picker
              popper-class="dataMap-picker"
              v-model="formData.create_time"
              type="daterange"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              range-separator="~"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label-width="80px" label="数据名称" prop="file_name">
            <el-input
              v-model="formData.file_name"
              placeholder="输入名称关键字"
              clearable
            ></el-input>
          </el-form-item>
        </el-form>
      </el-scrollbar>
    </div>
    <div class="form-btn">
      <el-button @click="handleSubmit" class="btn" type="primary" plain>查询</el-button>
      <el-button @click="handleReset" class="btn" type="primary" plain>重置</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import icon from '@/assets/dataSearchImg/icon.png'
import square from '@/assets/dataSearchImg/square.png'
import squareActive from '@/assets/dataSearchImg/squareActive.png'
import circular from '@/assets/dataSearchImg/circular.png'
import circularActive from '@/assets/dataSearchImg/circularActive.png'
import polygon from '@/assets/dataSearchImg/polygon.png'
import polygonActive from '@/assets/dataSearchImg/polygonActive.png'
import { querySysAdminCodeApi, vectorTowktApi } from '@/api/data_search'
import { ElFormItem, ElMessage, UploadProps } from 'element-plus'
import useGlobalData from '@/store/useGlobalData'
import useUserInfo from '@/store/useUserInfo'

const permission = useUserInfo()

const props = defineProps<{
  active: number
  coordinateObj: any
  trellisCode: any
  searchType: number
}>()

// 获取省数据
const provinceOptions = ref<any[]>([])
const getOptions = async () => {
  try {
    const { data, status, message } = await querySysAdminCodeApi()
    if ([200].includes(status)) {
      provinceOptions.value = data
    } else {
      ElMessage.error(message)
    }
  } catch (error) {}
}
getOptions()

// 获取市级数据
const cityOptions = computed<any[]>(() => {
  if (formData.value.province) {
    return provinceOptions.value.find((item: any) => formData.value.province === item.admincode)
      .childList
  }
  return []
})

// 选取省份时 市区默认为空
const handleSelectProvince = () => {
  formData.value.city = ''
}

// 获取数据类型
const globalData = useGlobalData()
const typeOptions = [
  ...globalData.catalogMenuList.slice(0, 3),
  { tid: 1, fileTypeName: '时序场景' },
]

// 重置
const handleReset = () => {
  formData.value = { country: '', create_time: [] }
  imgActive.value = ''
  if ([4, 5].includes(props.active)) {
    props.trellisCode.clearSelectedSquare()
  }
  if ([3].includes(props.active)) {
    props.coordinateObj.removeAllEvent()
    props.coordinateObj.clearEntity()
  }
}
watch(
  () => props.active,
  () => {
    handleReset()
    imgActive.value = ''
  },
)

const getSceneType = (ids: any[]) => ids.includes(1)

const getFileTypeId = (ids: any[]) => {
  const typeIds = typeOptions
    .filter((item: any) => ids.includes(item.tid) && ![1].includes(item.tid))
    .reduce((pre: any, cur: any) => {
      return [...pre, ...cur.childVOList]
    }, [])
    .map((item: any) => item.tid)
    .join(',')
  return typeIds
}

const handleAvatarSuccess: UploadProps['onSuccess'] = (response) => {
  const { data, message, status } = response
  if ([200].includes(status)) {
    formData.value.file = data
    ElMessage.success('上传成功')
  } else {
    ElMessage.error(message || '上传失败')
    formData.value.file = ''
  }
}

// 高级查询
const handleSubmit = () => {
  let params: any = {}
  // 北斗网格码查询
  if ([4, 5].includes(props.active)) {
    params = {
      codes: formData.value.code,
      dates:
        formData.value.create_time && formData.value.create_time.length
          ? formData.value.create_time
          : undefined,
      dataTypeIds:
        formData.value.file_type_id && getFileTypeId(formData.value.file_type_id)
          ? getFileTypeId(formData.value.file_type_id).split(',')
          : undefined,
      sceneType:
        formData.value.file_type_id && getSceneType(formData.value.file_type_id) ? 1 : undefined,
      dataName: formData.value.file_name,
    }
    emit('handleSubmit', params)
    emit('handleResetMap', false)
    imgActive.value = ''
    return
  }
  if ([1].includes(props.active) && (formData.value.province || formData.value.city)) {
    params.cql_filter = `admin_code like '%/${formData.value.province || formData.value.city}/%'`
  }
  if ([2].includes(props.active)) {
    if (
      formData.value.leftLong &&
      formData.value.leftLat &&
      formData.value.rightLong &&
      formData.value.rightLat
    ) {
      params.bbox = `${formData.value.leftLong},${formData.value.leftLat},${formData.value.rightLong},${formData.value.rightLat}`
    }
  }
  if ([3].includes(props.active) && formData.value.coordinate) {
    if (['1'].includes(imgActive.value)) {
      params.cql_filter = `${params.cql_filter || ''} ${
        params.cql_filter ? 'and' : ''
      } DWITHIN(geom,POINT(${formData.value.coordinate.ponit.join(' ')}),${formData.value.coordinate.distance}, meters)`
    } else {
      const list: any[] = formData.value.coordinate.map((item: any[]) => item.join(' ')).join(',')
      params.cql_filter = `${params.cql_filter || ''} ${
        params.cql_filter ? 'and' : ''
      } INTERSECTS(geom,POLYGON((${list})))`
    }
  }
  if ([6].includes(props.active)) {
    params.cql_filter = `${params.cql_filter || ''} ${
      params.cql_filter ? 'and' : ''
    } INTERSECTS(geom,${formData.value.file})`
  }
  if (formData.value.file_type_id && getFileTypeId(formData.value.file_type_id)) {
    params.cql_filter = `${params.cql_filter || ''} ${
      params.cql_filter ? 'and' : ''
    } file_type_id in(${getFileTypeId(formData.value.file_type_id)})`
  }
  if (formData.value.file_type_id && getSceneType(formData.value.file_type_id)) {
    params.cql_filter = `${params.cql_filter || ''} ${
      params.cql_filter ? 'or' : ''
    } scene_type=${1}`
  }
  if (formData.value.create_time && formData.value.create_time.length) {
    params.cql_filter = `${params.cql_filter || ''} ${
      params.cql_filter ? 'and' : ''
    } create_time<='${formData.value.create_time[1]}' and create_time>='${
      formData.value.create_time[0]
    }'`
  }
  if (formData.value.file_name) {
    params.cql_filter = `${params.cql_filter || ''} ${
      params.cql_filter ? 'and' : ''
    } file_name like '%${formData.value.file_name}%'`
  }

  if (permission.userInfo.tidList) {
    params.cql_filter = `${params.cql_filter || ''} ${
      params.cql_filter ? 'and' : ''
    } uid in(${permission.userInfo.tidList.join(',')})`
  }

  if (params.cql_filter) {
    params.cql_filter = params.cql_filter.trim()
  }
  emit('handleSubmit', params)
  emit('handleResetMap', false)
  imgActive.value = ''
}

// 数据表单
const formData = ref<any>({ country: '', province: '', city: '', create_time: [] })

// 获取原型数据
watch(
  () => props.coordinateObj.entity,
  () => {
    if (!props.coordinateObj.entity) {
      imgActive.value = ''
    }
    formData.value.coordinate = props.coordinateObj.entity
      ? props.coordinateObj.getPositionPoints(props.coordinateObj.entity, imgActive.value)
      : undefined
  },
)

// 网格数据
watch(
  () => props.trellisCode.codeData.selectedSquare,
  () => {
    const list: any[] = props.trellisCode.codeData.selectedSquare
    formData.value.code = list.length ? list.map((item: any) => item.properties.code) : undefined
  },
  {
    deep: true,
  },
)

// 选取数据类型
const handleSelectFileType = (p: any) => {
  if ((formData.value.file_type_id || []).includes(p.tid)) {
    formData.value.file_type_id = formData.value.file_type_id.filter((item: any) => item !== p.tid)
  } else {
    if (formData.value.file_type_id) {
      formData.value.file_type_id.push(p.tid)
      return
    }
    formData.value.file_type_id = [p.tid]
  }
}

// 图形框选
const imgActive = ref<string>('')
const handleSelectBox = (p: any) => {
  imgActive.value = p.id
  emit('handleTool', imgActive.value)
}
const boxImg: any = [
  {
    name: '矩形',
    img: square,
    imgActive: squareActive,
    id: '0',
  },
  {
    name: '圆形',
    img: circular,
    imgActive: circularActive,
    id: '1',
  },
  {
    name: '多边形',
    img: polygon,
    imgActive: polygonActive,
    id: '2',
  },
]

const emit = defineEmits<{
  handleSubmit: [form: any]
  handleTool: [name: string | undefined]
  handleResetMap: [isDel?: boolean]
}>()
</script>

<style scoped lang="less">
.formData {
  height: 60vh;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  background-color: rgba(37, 45, 75, 0.8);
  padding-bottom: 20px;
  overflow: hidden;
  .search {
    flex: 1;
    box-sizing: border-box;
    overflow: hidden;
    .search-title {
      height: 20px;
      display: flex;
      line-height: 20px;
      margin: 15px 0;
      img {
        width: 16px;
        height: 20px;
      }
      p {
        margin-left: 10px;
        font-size: 14px;
        font-weight: 400;
        color: #bdcdd9;
      }
    }

    .search-form {
      padding: 0 20px;
      :deep(.el-form-item__label) {
        font-size: 14px !important;
        color: #8799b7 !important;
      }
      :deep(.el-select) {
        width: 100%;
      }
      :deep(.el-input__wrapper) {
        box-shadow: none;
        background: #252d4b;
        border: 1px solid #333951;
        .el-input__inner,
        .el-range-input {
          color: @withe;
          margin: 0 5px;
          &::placeholder {
            color: #8798b7;
          }
        }
        .el-icon {
          color: #8798b7;
        }
      }
      :deep(.el-date-editor .el-range-separator) {
        color: #8799b7;
      }
      .el-button {
        background: #0d224b;
        color: var(--el-color-primary);
        border: 1px solid var(--el-color-primary);
      }
      .el-button--primary.is-plain:focus,
      .el-button--primary.is-plain:hover {
        background: var(--el-color-primary);
        border-color: var(--el-color-primary);
        color: @withe;
      }
      .btnActive {
        background: var(--el-color-primary);
        border: 1px solid var(--el-color-primary);
        color: @withe;
      }

      .long-input {
        :deep(.el-form-item__label) {
          line-height: 32px;
          height: 32px;
        }
        :deep(.el-input-group__prepend) {
          border-radius: 0px;
          box-shadow: none;
          border: 1px solid #333951;
          background: #252d4b;
          color: #b2e3ff;
        }
      }

      .box-select {
        cursor: pointer;
      }
      .box-select + .box-select {
        margin-left: 40px;
      }
    }
  }

  .form-btn {
    padding-top: 10px;
    padding-right: 20px;
    display: flex;
    justify-content: flex-end;
    margin-bottom: 10px;
    .btn {
      background-color: transparent;
      border: 1px solid var(--el-color-primary);
      color: var(--el-color-primary);
      &:hover {
        background-color: var(--el-color-primary);
        color: @withe;
      }
      &:active {
        background-color: var(--el-color-primary);
      }
    }
  }
  .upload-demo {
    :deep(.el-upload) {
      border: 1px dashed var(--el-border-color);
      border-radius: 6px;
      cursor: pointer;
      height: 100px;
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      .el-icon--upload {
        font-size: 60px;
        color: #8c939d;
      }
      .el-upload__text {
        display: block;
      }
    }
    .el-upload_tips {
      font-size: 12px;
      color: #8c939d;
    }
  }
}
.search-form {
  :deep(.el-select__wrapper) {
    background: #252d4b;
    box-shadow: none;
  }
  :deep(.el-select__placeholder) {
    color: @withe;
  }
}
</style>
<style>
.dataMap-select.el-popper.is-light {
  border: 1px solid #333951;
  background: #252d4b;
}
.dataMap-select .el-popper__arrow {
  background: #252d4b;
}
.dataMap-select.el-popper.is-light .el-popper__arrow::before {
  border: 1px solid #333951;
  background: #252d4b;
}
.dataMap-select .el-select-dropdown__item {
  color: #8798b7;
}
.dataMap-select .el-select-dropdown__item.selected,
.dataMap-select .el-select-dropdown__item.is-hovering {
  color: #409eff;
  background: rgba(22, 54, 113, 0.4);
}
.dataMap-select .el-select-dropdown__item.hover,
.dataMap-select .el-select-dropdown__item:hover {
  color: #409eff;
  background: rgba(22, 54, 113, 0.4);
}

.popper-tip.is-dark,
.popper-tip.is-dark .el-popper__arrow,
.popper-tip.is-dark .el-popper__arrow::before {
  background: #163671;
}
</style>
